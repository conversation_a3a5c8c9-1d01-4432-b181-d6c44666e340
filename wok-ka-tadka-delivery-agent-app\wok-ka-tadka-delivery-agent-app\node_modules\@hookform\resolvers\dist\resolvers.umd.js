!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","react-hook-form"],t):t((e||self).hookformResolvers={},e.ReactHookForm)}(this,function(e,t){var r=function(e,r,o){if(e&&"reportValidity"in e){var i=t.get(o,r);e.setCustomValidity(i&&i.message||""),e.reportValidity()}},o=function(e,t){var o=function(o){var i=t.fields[o];i&&i.ref&&"reportValidity"in i.ref?r(i.ref,o,e):i.refs&&i.refs.forEach(function(t){return r(t,o,e)})};for(var i in t.fields)o(i)},i=function(e,t){return e.some(function(e){return e.startsWith(t+".")})};e.toNestErrors=function(e,r){r.shouldUseNativeValidation&&o(e,r);var n={};for(var f in e){var s=t.get(r.fields,f),a=Object.assign(e[f]||{},{ref:s&&s.ref});if(i(r.names||Object.keys(e),f)){var d=Object.assign({},t.get(n,f));t.set(d,"root",a),t.set(n,f,d)}else t.set(n,f,a)}return n},e.validateFieldsNatively=o});
//# sourceMappingURL=resolvers.umd.js.map
