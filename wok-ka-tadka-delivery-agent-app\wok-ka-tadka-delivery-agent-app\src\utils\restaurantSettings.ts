// Restaurant Settings Management Utility

export interface RestaurantSettings {
  name: string;
  address: string;
  phone: string;
  email: string;
  gst: string;
  currency: string;
  timezone: string;
  lastUpdated: string;
}

class RestaurantSettingsManager {
  private readonly STORAGE_KEY = 'restaurantSettings';
  private readonly DEFAULT_SETTINGS: RestaurantSettings = {
    name: "Wok Ka Tadka",
    address: "Mumbai Style Chinese & Indian",
    phone: "+91 9876543210",
    email: "<EMAIL>",
    gst: "27ABCDE1234F1Z5",
    currency: "INR",
    timezone: "Asia/Kolkata",
    lastUpdated: new Date().toISOString()
  };

  // Get restaurant settings from localStorage
  getRestaurantSettings(): RestaurantSettings {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const settings = JSON.parse(stored);
        return {
          ...this.DEFAULT_SETTINGS,
          ...settings,
          lastUpdated: settings.lastUpdated || new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('Error reading restaurant settings from storage:', error);
    }
    return { ...this.DEFAULT_SETTINGS };
  }

  // Save restaurant settings to localStorage
  saveRestaurantSettings(settings: Partial<RestaurantSettings>): void {
    try {
      const currentSettings = this.getRestaurantSettings();
      const updatedSettings = {
        ...currentSettings,
        ...settings,
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedSettings));
      
      // Trigger a custom event to notify components about settings change
      window.dispatchEvent(new CustomEvent('restaurantSettingsChanged', {
        detail: updatedSettings
      }));
    } catch (error) {
      console.error('Error saving restaurant settings to storage:', error);
    }
  }

  // Update specific setting
  updateSetting(key: keyof RestaurantSettings, value: string): void {
    const settings = this.getRestaurantSettings();
    settings[key] = value;
    this.saveRestaurantSettings(settings);
  }

  // Reset to default settings
  resetToDefaults(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      
      // Trigger a custom event to notify components about settings reset
      window.dispatchEvent(new CustomEvent('restaurantSettingsChanged', {
        detail: this.DEFAULT_SETTINGS
      }));
    } catch (error) {
      console.error('Error resetting restaurant settings:', error);
    }
  }

  // Get formatted restaurant header for printing
  getFormattedHeader(): { name: string; address: string; phone: string } {
    const settings = this.getRestaurantSettings();
    return {
      name: settings.name,
      address: settings.address,
      phone: settings.phone
    };
  }

  // Get restaurant name only
  getRestaurantName(): string {
    return this.getRestaurantSettings().name;
  }

  // Get restaurant address only
  getRestaurantAddress(): string {
    return this.getRestaurantSettings().address;
  }

  // Get restaurant phone only
  getRestaurantPhone(): string {
    return this.getRestaurantSettings().phone;
  }

  // Get GST number
  getGSTNumber(): string {
    return this.getRestaurantSettings().gst;
  }
}

// Export singleton instance
export const restaurantSettingsManager = new RestaurantSettingsManager();
