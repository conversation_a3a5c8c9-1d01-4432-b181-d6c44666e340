#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1855216 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:149), pid=29464, tid=22196
#
# JRE version: Java(TM) SE Runtime Environment (23.0.2+7) (build 23.0.2+7-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (23.0.2+7-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-all\2qik7nd48slq1ooc2496ixf4i\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: 12th Gen Intel(R) Core(TM) i5-12450H, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
Time: Tue Jul 29 15:09:31 2025 India Standard Time elapsed time: 9.264710 seconds (0d 0h 0m 9s)

---------------  T H R E A D  ---------------

Current thread (0x00000177c1091f80):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=22196, stack(0x00000056cc400000,0x00000056cc500000) (1024K)]


Current CompileTask:
C2:9264 3043   !   4       java.lang.ClassLoader::loadClass (121 bytes)

Stack: [0x00000056cc400000,0x00000056cc500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e0b99]  (no source info available)
V  [jvm.dll+0x87e2b3]  (no source info available)
V  [jvm.dll+0x88073e]  (no source info available)
V  [jvm.dll+0x880e13]  (no source info available)
V  [jvm.dll+0x27b006]  (no source info available)
V  [jvm.dll+0xbc06f]  (no source info available)
V  [jvm.dll+0xbc2cb]  (no source info available)
V  [jvm.dll+0x3b3c22]  (no source info available)
V  [jvm.dll+0x38a55b]  (no source info available)
V  [jvm.dll+0x3899ca]  (no source info available)
V  [jvm.dll+0x244ba1]  (no source info available)
V  [jvm.dll+0x243eff]  (no source info available)
V  [jvm.dll+0x1c34b0]  (no source info available)
V  [jvm.dll+0x253df1]  (no source info available)
V  [jvm.dll+0x2520aa]  (no source info available)
V  [jvm.dll+0x3ee226]  (no source info available)
V  [jvm.dll+0x8254cb]  (no source info available)
V  [jvm.dll+0x6df345]  (no source info available)
C  [ucrtbase.dll+0x37b0]  (no source info available)
C  [KERNEL32.DLL+0x2e8d7]  (no source info available)
C  [ntdll.dll+0x3c34c]  (no source info available)

Lock stack of current Java thread (top to bottom):


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000177c7595d60, length=25, elements={
0x00000177ccfa6700, 0x00000177e9289700, 0x00000177e928a260, 0x00000177e928f480,
0x00000177e92923d0, 0x00000177e9293850, 0x00000177e9299ed0, 0x00000177c1091f80,
0x00000177e92a74e0, 0x00000177e9298b20, 0x00000177e929a560, 0x00000177e929b910,
0x00000177e9298490, 0x00000177e929abf0, 0x00000177e9299840, 0x00000177c6482870,
0x00000177c64842b0, 0x00000177c6480e30, 0x00000177c6481b50, 0x00000177c6483590,
0x00000177c64821e0, 0x00000177c6484940, 0x00000177c64814c0, 0x00000177c7446bd0,
0x00000177c6486380
}

Java Threads: ( => current thread )
  0x00000177ccfa6700 JavaThread "main"                              [_thread_blocked, id=27040, stack(0x00000056cb600000,0x00000056cb700000) (1024K)]
  0x00000177e9289700 JavaThread "Reference Handler"          daemon [_thread_blocked, id=14632, stack(0x00000056cbe00000,0x00000056cbf00000) (1024K)]
  0x00000177e928a260 JavaThread "Finalizer"                  daemon [_thread_blocked, id=30304, stack(0x00000056cbf00000,0x00000056cc000000) (1024K)]
  0x00000177e928f480 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=15868, stack(0x00000056cc000000,0x00000056cc100000) (1024K)]
  0x00000177e92923d0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=8436, stack(0x00000056cc100000,0x00000056cc200000) (1024K)]
  0x00000177e9293850 JavaThread "Service Thread"             daemon [_thread_blocked, id=16092, stack(0x00000056cc200000,0x00000056cc300000) (1024K)]
  0x00000177e9299ed0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=3892, stack(0x00000056cc300000,0x00000056cc400000) (1024K)]
=>0x00000177c1091f80 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=22196, stack(0x00000056cc400000,0x00000056cc500000) (1024K)]
  0x00000177e92a74e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=15060, stack(0x00000056cc500000,0x00000056cc600000) (1024K)]
  0x00000177e9298b20 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=4056, stack(0x00000056cc600000,0x00000056cc700000) (1024K)]
  0x00000177e929a560 JavaThread "Notification Thread"        daemon [_thread_blocked, id=28100, stack(0x00000056cc700000,0x00000056cc800000) (1024K)]
  0x00000177e929b910 JavaThread "Daemon health stats"               [_thread_blocked, id=30368, stack(0x00000056cce00000,0x00000056ccf00000) (1024K)]
  0x00000177e9298490 JavaThread "Incoming local TCP Connector on port 58621"        [_thread_in_native, id=23360, stack(0x00000056ccf00000,0x00000056cd000000) (1024K)]
  0x00000177e929abf0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=26280, stack(0x00000056cd000000,0x00000056cd100000) (1024K)]
  0x00000177e9299840 JavaThread "Daemon"                            [_thread_blocked, id=29980, stack(0x00000056cd100000,0x00000056cd200000) (1024K)]
  0x00000177c6482870 JavaThread "Handler for socket connection from /127.0.0.1:58621 to /127.0.0.1:58622"        [_thread_in_native, id=28700, stack(0x00000056cd200000,0x00000056cd300000) (1024K)]
  0x00000177c64842b0 JavaThread "Cancel handler"                    [_thread_blocked, id=19880, stack(0x00000056cc800000,0x00000056cc900000) (1024K)]
  0x00000177c6480e30 JavaThread "Daemon worker"                     [_thread_in_native, id=28188, stack(0x00000056cd300000,0x00000056cd400000) (1024K)]
  0x00000177c6481b50 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:58621 to /127.0.0.1:58622"        [_thread_blocked, id=7568, stack(0x00000056cd400000,0x00000056cd500000) (1024K)]
  0x00000177c6483590 JavaThread "Stdin handler"                     [_thread_blocked, id=22560, stack(0x00000056cd500000,0x00000056cd600000) (1024K)]
  0x00000177c64821e0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=29712, stack(0x00000056cd600000,0x00000056cd700000) (1024K)]
  0x00000177c6484940 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=21536, stack(0x00000056cd700000,0x00000056cd800000) (1024K)]
  0x00000177c64814c0 JavaThread "File lock request listener"        [_thread_in_native, id=29028, stack(0x00000056cd800000,0x00000056cd900000) (1024K)]
  0x00000177c7446bd0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=8948, stack(0x00000056cd900000,0x00000056cda00000) (1024K)]
  0x00000177c6486380 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.11.1\fileHashes)"        [_thread_blocked, id=28628, stack(0x00000056cda00000,0x00000056cdb00000) (1024K)]
Total: 25

Other Threads:
  0x00000177e9271c70 VMThread "VM Thread"                           [id=21860, stack(0x00000056cbd00000,0x00000056cbe00000) (1024K)]
  0x00000177e9262100 WatcherThread "VM Periodic Task Thread"        [id=12552, stack(0x00000056cbc00000,0x00000056cbd00000) (1024K)]
  0x00000177ccff1010 WorkerThread "GC Thread#0"                     [id=10540, stack(0x00000056cb700000,0x00000056cb800000) (1024K)]
  0x00000177c17a07b0 WorkerThread "GC Thread#1"                     [id=30376, stack(0x00000056cc900000,0x00000056cca00000) (1024K)]
  0x00000177c1974b20 WorkerThread "GC Thread#2"                     [id=28904, stack(0x00000056cca00000,0x00000056ccb00000) (1024K)]
  0x00000177c18144f0 WorkerThread "GC Thread#3"                     [id=3144, stack(0x00000056ccb00000,0x00000056ccc00000) (1024K)]
  0x00000177c18148a0 WorkerThread "GC Thread#4"                     [id=22136, stack(0x00000056ccc00000,0x00000056ccd00000) (1024K)]
  0x00000177c1814c50 WorkerThread "GC Thread#5"                     [id=26236, stack(0x00000056ccd00000,0x00000056cce00000) (1024K)]
  0x00000177c6dab360 WorkerThread "GC Thread#6"                     [id=10740, stack(0x00000056cdb00000,0x00000056cdc00000) (1024K)]
  0x00000177c6dab710 WorkerThread "GC Thread#7"                     [id=26500, stack(0x00000056cdc00000,0x00000056cdd00000) (1024K)]
  0x00000177c74b7b90 WorkerThread "GC Thread#8"                     [id=18552, stack(0x00000056cdd00000,0x00000056cde00000) (1024K)]
  0x00000177c74b9560 WorkerThread "GC Thread#9"                     [id=25108, stack(0x00000056cde00000,0x00000056cdf00000) (1024K)]
  0x00000177cd0034b0 ConcurrentGCThread "G1 Main Marker"            [id=23280, stack(0x00000056cb800000,0x00000056cb900000) (1024K)]
  0x00000177cd004670 WorkerThread "G1 Conc#0"                       [id=27272, stack(0x00000056cb900000,0x00000056cba00000) (1024K)]
  0x00000177cd05e630 ConcurrentGCThread "G1 Refine#0"               [id=26688, stack(0x00000056cba00000,0x00000056cbb00000) (1024K)]
  0x00000177e91af510 ConcurrentGCThread "G1 Service"                [id=23720, stack(0x00000056cbb00000,0x00000056cbc00000) (1024K)]
Total: 16

Threads with active compile tasks:
C2 CompilerThread0  9527 3043   !   4       java.lang.ClassLoader::loadClass (121 bytes)
C2 CompilerThread1  9527 3071       4       java.util.ArrayList::grow (60 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000a0000000, size: 1536 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000017780000000-0x0000017780d70000-0x0000017780d70000), size 14090240, SharedBaseAddress: 0x0000017780000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000017781000000-0x00000177c1000000, reserved size: 1073741824
Narrow klass base: 0x0000017780000000, Narrow klass shift: 0, Narrow klass range: 0x41000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 10 size 36 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 12 total, 12 available
 Memory: 16076M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 1536M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total reserved 1572864K, committed 258048K, used 40247K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 9 survivors (9216K)
 Metaspace       used 19056K, committed 19584K, reserved 1114112K
  class space    used 2779K, committed 3008K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| O|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Untracked |  0
|   1|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| O|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Untracked |  0
|   2|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%|HS|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete |  0
|   3|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%|HC|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Complete |  0
|   4|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%|HC|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Complete |  0
|   5|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| O|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked |  0
|   6|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| O|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked |  0
|   7|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| O|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Untracked |  0
|   8|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%|HS|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Complete |  0
|   9|0x00000000a0900000, 0x00000000a094df30, 0x00000000a0a00000| 30%| O|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked |  0
|  10|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| O|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked |  0
|  11|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| O|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked |  0
|  12|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| O|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked |  0
|  13|0x00000000a0d00000, 0x00000000a0d00000, 0x00000000a0e00000|  0%| F|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Untracked |  0
|  14|0x00000000a0e00000, 0x00000000a0e00000, 0x00000000a0f00000|  0%| F|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Untracked |  0
|  15|0x00000000a0f00000, 0x00000000a0f00000, 0x00000000a1000000|  0%| F|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Untracked |  0
|  16|0x00000000a1000000, 0x00000000a1000000, 0x00000000a1100000|  0%| F|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Untracked |  0
|  17|0x00000000a1100000, 0x00000000a1100000, 0x00000000a1200000|  0%| F|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Untracked |  0
|  18|0x00000000a1200000, 0x00000000a1200000, 0x00000000a1300000|  0%| F|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked |  0
|  19|0x00000000a1300000, 0x00000000a1300000, 0x00000000a1400000|  0%| F|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked |  0
|  20|0x00000000a1400000, 0x00000000a1400000, 0x00000000a1500000|  0%| F|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked |  0
|  21|0x00000000a1500000, 0x00000000a1500000, 0x00000000a1600000|  0%| F|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Untracked |  0
|  22|0x00000000a1600000, 0x00000000a1600000, 0x00000000a1700000|  0%| F|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked |  0
|  23|0x00000000a1700000, 0x00000000a1700000, 0x00000000a1800000|  0%| F|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked |  0
|  24|0x00000000a1800000, 0x00000000a1800000, 0x00000000a1900000|  0%| F|  |TAMS 0x00000000a1800000| PB 0x00000000a1800000| Untracked |  0
|  25|0x00000000a1900000, 0x00000000a1900000, 0x00000000a1a00000|  0%| F|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Untracked |  0
|  26|0x00000000a1a00000, 0x00000000a1a00000, 0x00000000a1b00000|  0%| F|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked |  0
|  27|0x00000000a1b00000, 0x00000000a1b00000, 0x00000000a1c00000|  0%| F|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked |  0
|  28|0x00000000a1c00000, 0x00000000a1c00000, 0x00000000a1d00000|  0%| F|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked |  0
|  29|0x00000000a1d00000, 0x00000000a1d00000, 0x00000000a1e00000|  0%| F|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked |  0
|  30|0x00000000a1e00000, 0x00000000a1e00000, 0x00000000a1f00000|  0%| F|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Untracked |  0
|  31|0x00000000a1f00000, 0x00000000a1f00000, 0x00000000a2000000|  0%| F|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked |  0
|  32|0x00000000a2000000, 0x00000000a2000000, 0x00000000a2100000|  0%| F|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Untracked |  0
|  33|0x00000000a2100000, 0x00000000a2100000, 0x00000000a2200000|  0%| F|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Untracked |  0
|  34|0x00000000a2200000, 0x00000000a2200000, 0x00000000a2300000|  0%| F|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked |  0
|  35|0x00000000a2300000, 0x00000000a2300000, 0x00000000a2400000|  0%| F|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Untracked |  0
|  36|0x00000000a2400000, 0x00000000a2400000, 0x00000000a2500000|  0%| F|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Untracked |  0
|  37|0x00000000a2500000, 0x00000000a2500000, 0x00000000a2600000|  0%| F|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Untracked |  0
|  38|0x00000000a2600000, 0x00000000a2600000, 0x00000000a2700000|  0%| F|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Untracked |  0
|  39|0x00000000a2700000, 0x00000000a2700000, 0x00000000a2800000|  0%| F|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Untracked |  0
|  40|0x00000000a2800000, 0x00000000a2800000, 0x00000000a2900000|  0%| F|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Untracked |  0
|  41|0x00000000a2900000, 0x00000000a2900000, 0x00000000a2a00000|  0%| F|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Untracked |  0
|  42|0x00000000a2a00000, 0x00000000a2a00000, 0x00000000a2b00000|  0%| F|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Untracked |  0
|  43|0x00000000a2b00000, 0x00000000a2b00000, 0x00000000a2c00000|  0%| F|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Untracked |  0
|  44|0x00000000a2c00000, 0x00000000a2c00000, 0x00000000a2d00000|  0%| F|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Untracked |  0
|  45|0x00000000a2d00000, 0x00000000a2d00000, 0x00000000a2e00000|  0%| F|  |TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Untracked |  0
|  46|0x00000000a2e00000, 0x00000000a2e00000, 0x00000000a2f00000|  0%| F|  |TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Untracked |  0
|  47|0x00000000a2f00000, 0x00000000a2f00000, 0x00000000a3000000|  0%| F|  |TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Untracked |  0
|  48|0x00000000a3000000, 0x00000000a3000000, 0x00000000a3100000|  0%| F|  |TAMS 0x00000000a3000000| PB 0x00000000a3000000| Untracked |  0
|  49|0x00000000a3100000, 0x00000000a3100000, 0x00000000a3200000|  0%| F|  |TAMS 0x00000000a3100000| PB 0x00000000a3100000| Untracked |  0
|  50|0x00000000a3200000, 0x00000000a3200000, 0x00000000a3300000|  0%| F|  |TAMS 0x00000000a3200000| PB 0x00000000a3200000| Untracked |  0
|  51|0x00000000a3300000, 0x00000000a3300000, 0x00000000a3400000|  0%| F|  |TAMS 0x00000000a3300000| PB 0x00000000a3300000| Untracked |  0
|  52|0x00000000a3400000, 0x00000000a3400000, 0x00000000a3500000|  0%| F|  |TAMS 0x00000000a3400000| PB 0x00000000a3400000| Untracked |  0
|  53|0x00000000a3500000, 0x00000000a3500000, 0x00000000a3600000|  0%| F|  |TAMS 0x00000000a3500000| PB 0x00000000a3500000| Untracked |  0
|  54|0x00000000a3600000, 0x00000000a3600000, 0x00000000a3700000|  0%| F|  |TAMS 0x00000000a3600000| PB 0x00000000a3600000| Untracked |  0
|  55|0x00000000a3700000, 0x00000000a3700000, 0x00000000a3800000|  0%| F|  |TAMS 0x00000000a3700000| PB 0x00000000a3700000| Untracked |  0
|  56|0x00000000a3800000, 0x00000000a3800000, 0x00000000a3900000|  0%| F|  |TAMS 0x00000000a3800000| PB 0x00000000a3800000| Untracked |  0
|  57|0x00000000a3900000, 0x00000000a3900000, 0x00000000a3a00000|  0%| F|  |TAMS 0x00000000a3900000| PB 0x00000000a3900000| Untracked |  0
|  58|0x00000000a3a00000, 0x00000000a3a00000, 0x00000000a3b00000|  0%| F|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Untracked |  0
|  59|0x00000000a3b00000, 0x00000000a3b00000, 0x00000000a3c00000|  0%| F|  |TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Untracked |  0
|  60|0x00000000a3c00000, 0x00000000a3c00000, 0x00000000a3d00000|  0%| F|  |TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Untracked |  0
|  61|0x00000000a3d00000, 0x00000000a3d00000, 0x00000000a3e00000|  0%| F|  |TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Untracked |  0
|  62|0x00000000a3e00000, 0x00000000a3e00000, 0x00000000a3f00000|  0%| F|  |TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Untracked |  0
|  63|0x00000000a3f00000, 0x00000000a3f00000, 0x00000000a4000000|  0%| F|  |TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Untracked |  0
|  64|0x00000000a4000000, 0x00000000a4000000, 0x00000000a4100000|  0%| F|  |TAMS 0x00000000a4000000| PB 0x00000000a4000000| Untracked |  0
|  65|0x00000000a4100000, 0x00000000a4100000, 0x00000000a4200000|  0%| F|  |TAMS 0x00000000a4100000| PB 0x00000000a4100000| Untracked |  0
|  66|0x00000000a4200000, 0x00000000a4200000, 0x00000000a4300000|  0%| F|  |TAMS 0x00000000a4200000| PB 0x00000000a4200000| Untracked |  0
|  67|0x00000000a4300000, 0x00000000a4300000, 0x00000000a4400000|  0%| F|  |TAMS 0x00000000a4300000| PB 0x00000000a4300000| Untracked |  0
|  68|0x00000000a4400000, 0x00000000a4400000, 0x00000000a4500000|  0%| F|  |TAMS 0x00000000a4400000| PB 0x00000000a4400000| Untracked |  0
|  69|0x00000000a4500000, 0x00000000a4500000, 0x00000000a4600000|  0%| F|  |TAMS 0x00000000a4500000| PB 0x00000000a4500000| Untracked |  0
|  70|0x00000000a4600000, 0x00000000a4600000, 0x00000000a4700000|  0%| F|  |TAMS 0x00000000a4600000| PB 0x00000000a4600000| Untracked |  0
|  71|0x00000000a4700000, 0x00000000a4700000, 0x00000000a4800000|  0%| F|  |TAMS 0x00000000a4700000| PB 0x00000000a4700000| Untracked |  0
|  72|0x00000000a4800000, 0x00000000a4800000, 0x00000000a4900000|  0%| F|  |TAMS 0x00000000a4800000| PB 0x00000000a4800000| Untracked |  0
|  73|0x00000000a4900000, 0x00000000a4900000, 0x00000000a4a00000|  0%| F|  |TAMS 0x00000000a4900000| PB 0x00000000a4900000| Untracked |  0
|  74|0x00000000a4a00000, 0x00000000a4a00000, 0x00000000a4b00000|  0%| F|  |TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Untracked |  0
|  75|0x00000000a4b00000, 0x00000000a4b00000, 0x00000000a4c00000|  0%| F|  |TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Untracked |  0
|  76|0x00000000a4c00000, 0x00000000a4c00000, 0x00000000a4d00000|  0%| F|  |TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Untracked |  0
|  77|0x00000000a4d00000, 0x00000000a4d00000, 0x00000000a4e00000|  0%| F|  |TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Untracked |  0
|  78|0x00000000a4e00000, 0x00000000a4e00000, 0x00000000a4f00000|  0%| F|  |TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Untracked |  0
|  79|0x00000000a4f00000, 0x00000000a4f00000, 0x00000000a5000000|  0%| F|  |TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Untracked |  0
|  80|0x00000000a5000000, 0x00000000a5000000, 0x00000000a5100000|  0%| F|  |TAMS 0x00000000a5000000| PB 0x00000000a5000000| Untracked |  0
|  81|0x00000000a5100000, 0x00000000a5100000, 0x00000000a5200000|  0%| F|  |TAMS 0x00000000a5100000| PB 0x00000000a5100000| Untracked |  0
|  82|0x00000000a5200000, 0x00000000a5200000, 0x00000000a5300000|  0%| F|  |TAMS 0x00000000a5200000| PB 0x00000000a5200000| Untracked |  0
|  83|0x00000000a5300000, 0x00000000a5300000, 0x00000000a5400000|  0%| F|  |TAMS 0x00000000a5300000| PB 0x00000000a5300000| Untracked |  0
|  84|0x00000000a5400000, 0x00000000a5400000, 0x00000000a5500000|  0%| F|  |TAMS 0x00000000a5400000| PB 0x00000000a5400000| Untracked |  0
|  85|0x00000000a5500000, 0x00000000a5500000, 0x00000000a5600000|  0%| F|  |TAMS 0x00000000a5500000| PB 0x00000000a5500000| Untracked |  0
|  86|0x00000000a5600000, 0x00000000a5600000, 0x00000000a5700000|  0%| F|  |TAMS 0x00000000a5600000| PB 0x00000000a5600000| Untracked |  0
|  87|0x00000000a5700000, 0x00000000a5700000, 0x00000000a5800000|  0%| F|  |TAMS 0x00000000a5700000| PB 0x00000000a5700000| Untracked |  0
|  88|0x00000000a5800000, 0x00000000a5800000, 0x00000000a5900000|  0%| F|  |TAMS 0x00000000a5800000| PB 0x00000000a5800000| Untracked |  0
|  89|0x00000000a5900000, 0x00000000a5900000, 0x00000000a5a00000|  0%| F|  |TAMS 0x00000000a5900000| PB 0x00000000a5900000| Untracked |  0
|  90|0x00000000a5a00000, 0x00000000a5a00000, 0x00000000a5b00000|  0%| F|  |TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Untracked |  0
|  91|0x00000000a5b00000, 0x00000000a5b00000, 0x00000000a5c00000|  0%| F|  |TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Untracked |  0
|  92|0x00000000a5c00000, 0x00000000a5c00000, 0x00000000a5d00000|  0%| F|  |TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Untracked |  0
|  93|0x00000000a5d00000, 0x00000000a5d00000, 0x00000000a5e00000|  0%| F|  |TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Untracked |  0
|  94|0x00000000a5e00000, 0x00000000a5e00000, 0x00000000a5f00000|  0%| F|  |TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Untracked |  0
|  95|0x00000000a5f00000, 0x00000000a5f00000, 0x00000000a6000000|  0%| F|  |TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Untracked |  0
|  96|0x00000000a6000000, 0x00000000a6000000, 0x00000000a6100000|  0%| F|  |TAMS 0x00000000a6000000| PB 0x00000000a6000000| Untracked |  0
|  97|0x00000000a6100000, 0x00000000a6100000, 0x00000000a6200000|  0%| F|  |TAMS 0x00000000a6100000| PB 0x00000000a6100000| Untracked |  0
|  98|0x00000000a6200000, 0x00000000a6200000, 0x00000000a6300000|  0%| F|  |TAMS 0x00000000a6200000| PB 0x00000000a6200000| Untracked |  0
|  99|0x00000000a6300000, 0x00000000a6300000, 0x00000000a6400000|  0%| F|  |TAMS 0x00000000a6300000| PB 0x00000000a6300000| Untracked |  0
| 100|0x00000000a6400000, 0x00000000a6400000, 0x00000000a6500000|  0%| F|  |TAMS 0x00000000a6400000| PB 0x00000000a6400000| Untracked |  0
| 101|0x00000000a6500000, 0x00000000a6500000, 0x00000000a6600000|  0%| F|  |TAMS 0x00000000a6500000| PB 0x00000000a6500000| Untracked |  0
| 102|0x00000000a6600000, 0x00000000a6600000, 0x00000000a6700000|  0%| F|  |TAMS 0x00000000a6600000| PB 0x00000000a6600000| Untracked |  0
| 103|0x00000000a6700000, 0x00000000a6700000, 0x00000000a6800000|  0%| F|  |TAMS 0x00000000a6700000| PB 0x00000000a6700000| Untracked |  0
| 104|0x00000000a6800000, 0x00000000a6800000, 0x00000000a6900000|  0%| F|  |TAMS 0x00000000a6800000| PB 0x00000000a6800000| Untracked |  0
| 105|0x00000000a6900000, 0x00000000a6900000, 0x00000000a6a00000|  0%| F|  |TAMS 0x00000000a6900000| PB 0x00000000a6900000| Untracked |  0
| 106|0x00000000a6a00000, 0x00000000a6a00000, 0x00000000a6b00000|  0%| F|  |TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Untracked |  0
| 107|0x00000000a6b00000, 0x00000000a6b00000, 0x00000000a6c00000|  0%| F|  |TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Untracked |  0
| 108|0x00000000a6c00000, 0x00000000a6c00000, 0x00000000a6d00000|  0%| F|  |TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Untracked |  0
| 109|0x00000000a6d00000, 0x00000000a6d00000, 0x00000000a6e00000|  0%| F|  |TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Untracked |  0
| 110|0x00000000a6e00000, 0x00000000a6e00000, 0x00000000a6f00000|  0%| F|  |TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Untracked |  0
| 111|0x00000000a6f00000, 0x00000000a6f00000, 0x00000000a7000000|  0%| F|  |TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Untracked |  0
| 112|0x00000000a7000000, 0x00000000a7000000, 0x00000000a7100000|  0%| F|  |TAMS 0x00000000a7000000| PB 0x00000000a7000000| Untracked |  0
| 113|0x00000000a7100000, 0x00000000a7100000, 0x00000000a7200000|  0%| F|  |TAMS 0x00000000a7100000| PB 0x00000000a7100000| Untracked |  0
| 114|0x00000000a7200000, 0x00000000a7200000, 0x00000000a7300000|  0%| F|  |TAMS 0x00000000a7200000| PB 0x00000000a7200000| Untracked |  0
| 115|0x00000000a7300000, 0x00000000a7300000, 0x00000000a7400000|  0%| F|  |TAMS 0x00000000a7300000| PB 0x00000000a7300000| Untracked |  0
| 116|0x00000000a7400000, 0x00000000a7400000, 0x00000000a7500000|  0%| F|  |TAMS 0x00000000a7400000| PB 0x00000000a7400000| Untracked |  0
| 117|0x00000000a7500000, 0x00000000a7500000, 0x00000000a7600000|  0%| F|  |TAMS 0x00000000a7500000| PB 0x00000000a7500000| Untracked |  0
| 118|0x00000000a7600000, 0x00000000a7600000, 0x00000000a7700000|  0%| F|  |TAMS 0x00000000a7600000| PB 0x00000000a7600000| Untracked |  0
| 119|0x00000000a7700000, 0x00000000a7700000, 0x00000000a7800000|  0%| F|  |TAMS 0x00000000a7700000| PB 0x00000000a7700000| Untracked |  0
| 120|0x00000000a7800000, 0x00000000a7800000, 0x00000000a7900000|  0%| F|  |TAMS 0x00000000a7800000| PB 0x00000000a7800000| Untracked |  0
| 121|0x00000000a7900000, 0x00000000a7900000, 0x00000000a7a00000|  0%| F|  |TAMS 0x00000000a7900000| PB 0x00000000a7900000| Untracked |  0
| 122|0x00000000a7a00000, 0x00000000a7a00000, 0x00000000a7b00000|  0%| F|  |TAMS 0x00000000a7a00000| PB 0x00000000a7a00000| Untracked |  0
| 123|0x00000000a7b00000, 0x00000000a7b00000, 0x00000000a7c00000|  0%| F|  |TAMS 0x00000000a7b00000| PB 0x00000000a7b00000| Untracked |  0
| 124|0x00000000a7c00000, 0x00000000a7c00000, 0x00000000a7d00000|  0%| F|  |TAMS 0x00000000a7c00000| PB 0x00000000a7c00000| Untracked |  0
| 125|0x00000000a7d00000, 0x00000000a7d00000, 0x00000000a7e00000|  0%| F|  |TAMS 0x00000000a7d00000| PB 0x00000000a7d00000| Untracked |  0
| 126|0x00000000a7e00000, 0x00000000a7e00000, 0x00000000a7f00000|  0%| F|  |TAMS 0x00000000a7e00000| PB 0x00000000a7e00000| Untracked |  0
| 127|0x00000000a7f00000, 0x00000000a7f00000, 0x00000000a8000000|  0%| F|  |TAMS 0x00000000a7f00000| PB 0x00000000a7f00000| Untracked |  0
| 128|0x00000000a8000000, 0x00000000a8000000, 0x00000000a8100000|  0%| F|  |TAMS 0x00000000a8000000| PB 0x00000000a8000000| Untracked |  0
| 129|0x00000000a8100000, 0x00000000a8100000, 0x00000000a8200000|  0%| F|  |TAMS 0x00000000a8100000| PB 0x00000000a8100000| Untracked |  0
| 130|0x00000000a8200000, 0x00000000a8200000, 0x00000000a8300000|  0%| F|  |TAMS 0x00000000a8200000| PB 0x00000000a8200000| Untracked |  0
| 131|0x00000000a8300000, 0x00000000a8300000, 0x00000000a8400000|  0%| F|  |TAMS 0x00000000a8300000| PB 0x00000000a8300000| Untracked |  0
| 132|0x00000000a8400000, 0x00000000a8400000, 0x00000000a8500000|  0%| F|  |TAMS 0x00000000a8400000| PB 0x00000000a8400000| Untracked |  0
| 133|0x00000000a8500000, 0x00000000a8500000, 0x00000000a8600000|  0%| F|  |TAMS 0x00000000a8500000| PB 0x00000000a8500000| Untracked |  0
| 134|0x00000000a8600000, 0x00000000a8600000, 0x00000000a8700000|  0%| F|  |TAMS 0x00000000a8600000| PB 0x00000000a8600000| Untracked |  0
| 135|0x00000000a8700000, 0x00000000a8700000, 0x00000000a8800000|  0%| F|  |TAMS 0x00000000a8700000| PB 0x00000000a8700000| Untracked |  0
| 136|0x00000000a8800000, 0x00000000a8800000, 0x00000000a8900000|  0%| F|  |TAMS 0x00000000a8800000| PB 0x00000000a8800000| Untracked |  0
| 137|0x00000000a8900000, 0x00000000a8900000, 0x00000000a8a00000|  0%| F|  |TAMS 0x00000000a8900000| PB 0x00000000a8900000| Untracked |  0
| 138|0x00000000a8a00000, 0x00000000a8a00000, 0x00000000a8b00000|  0%| F|  |TAMS 0x00000000a8a00000| PB 0x00000000a8a00000| Untracked |  0
| 139|0x00000000a8b00000, 0x00000000a8b00000, 0x00000000a8c00000|  0%| F|  |TAMS 0x00000000a8b00000| PB 0x00000000a8b00000| Untracked |  0
| 140|0x00000000a8c00000, 0x00000000a8c00000, 0x00000000a8d00000|  0%| F|  |TAMS 0x00000000a8c00000| PB 0x00000000a8c00000| Untracked |  0
| 141|0x00000000a8d00000, 0x00000000a8d00000, 0x00000000a8e00000|  0%| F|  |TAMS 0x00000000a8d00000| PB 0x00000000a8d00000| Untracked |  0
| 142|0x00000000a8e00000, 0x00000000a8e00000, 0x00000000a8f00000|  0%| F|  |TAMS 0x00000000a8e00000| PB 0x00000000a8e00000| Untracked |  0
| 143|0x00000000a8f00000, 0x00000000a8f00000, 0x00000000a9000000|  0%| F|  |TAMS 0x00000000a8f00000| PB 0x00000000a8f00000| Untracked |  0
| 144|0x00000000a9000000, 0x00000000a9000000, 0x00000000a9100000|  0%| F|  |TAMS 0x00000000a9000000| PB 0x00000000a9000000| Untracked |  0
| 145|0x00000000a9100000, 0x00000000a9100000, 0x00000000a9200000|  0%| F|  |TAMS 0x00000000a9100000| PB 0x00000000a9100000| Untracked |  0
| 146|0x00000000a9200000, 0x00000000a9200000, 0x00000000a9300000|  0%| F|  |TAMS 0x00000000a9200000| PB 0x00000000a9200000| Untracked |  0
| 147|0x00000000a9300000, 0x00000000a9300000, 0x00000000a9400000|  0%| F|  |TAMS 0x00000000a9300000| PB 0x00000000a9300000| Untracked |  0
| 148|0x00000000a9400000, 0x00000000a9400000, 0x00000000a9500000|  0%| F|  |TAMS 0x00000000a9400000| PB 0x00000000a9400000| Untracked |  0
| 149|0x00000000a9500000, 0x00000000a9500000, 0x00000000a9600000|  0%| F|  |TAMS 0x00000000a9500000| PB 0x00000000a9500000| Untracked |  0
| 150|0x00000000a9600000, 0x00000000a9600000, 0x00000000a9700000|  0%| F|  |TAMS 0x00000000a9600000| PB 0x00000000a9600000| Untracked |  0
| 151|0x00000000a9700000, 0x00000000a9700000, 0x00000000a9800000|  0%| F|  |TAMS 0x00000000a9700000| PB 0x00000000a9700000| Untracked |  0
| 152|0x00000000a9800000, 0x00000000a9800000, 0x00000000a9900000|  0%| F|  |TAMS 0x00000000a9800000| PB 0x00000000a9800000| Untracked |  0
| 153|0x00000000a9900000, 0x00000000a9900000, 0x00000000a9a00000|  0%| F|  |TAMS 0x00000000a9900000| PB 0x00000000a9900000| Untracked |  0
| 154|0x00000000a9a00000, 0x00000000a9a00000, 0x00000000a9b00000|  0%| F|  |TAMS 0x00000000a9a00000| PB 0x00000000a9a00000| Untracked |  0
| 155|0x00000000a9b00000, 0x00000000a9b00000, 0x00000000a9c00000|  0%| F|  |TAMS 0x00000000a9b00000| PB 0x00000000a9b00000| Untracked |  0
| 156|0x00000000a9c00000, 0x00000000a9c00000, 0x00000000a9d00000|  0%| F|  |TAMS 0x00000000a9c00000| PB 0x00000000a9c00000| Untracked |  0
| 157|0x00000000a9d00000, 0x00000000a9d00000, 0x00000000a9e00000|  0%| F|  |TAMS 0x00000000a9d00000| PB 0x00000000a9d00000| Untracked |  0
| 158|0x00000000a9e00000, 0x00000000a9e00000, 0x00000000a9f00000|  0%| F|  |TAMS 0x00000000a9e00000| PB 0x00000000a9e00000| Untracked |  0
| 159|0x00000000a9f00000, 0x00000000a9f00000, 0x00000000aa000000|  0%| F|  |TAMS 0x00000000a9f00000| PB 0x00000000a9f00000| Untracked |  0
| 160|0x00000000aa000000, 0x00000000aa000000, 0x00000000aa100000|  0%| F|  |TAMS 0x00000000aa000000| PB 0x00000000aa000000| Untracked |  0
| 161|0x00000000aa100000, 0x00000000aa100000, 0x00000000aa200000|  0%| F|  |TAMS 0x00000000aa100000| PB 0x00000000aa100000| Untracked |  0
| 162|0x00000000aa200000, 0x00000000aa200000, 0x00000000aa300000|  0%| F|  |TAMS 0x00000000aa200000| PB 0x00000000aa200000| Untracked |  0
| 163|0x00000000aa300000, 0x00000000aa300000, 0x00000000aa400000|  0%| F|  |TAMS 0x00000000aa300000| PB 0x00000000aa300000| Untracked |  0
| 164|0x00000000aa400000, 0x00000000aa400000, 0x00000000aa500000|  0%| F|  |TAMS 0x00000000aa400000| PB 0x00000000aa400000| Untracked |  0
| 165|0x00000000aa500000, 0x00000000aa500000, 0x00000000aa600000|  0%| F|  |TAMS 0x00000000aa500000| PB 0x00000000aa500000| Untracked |  0
| 166|0x00000000aa600000, 0x00000000aa600000, 0x00000000aa700000|  0%| F|  |TAMS 0x00000000aa600000| PB 0x00000000aa600000| Untracked |  0
| 167|0x00000000aa700000, 0x00000000aa700000, 0x00000000aa800000|  0%| F|  |TAMS 0x00000000aa700000| PB 0x00000000aa700000| Untracked |  0
| 168|0x00000000aa800000, 0x00000000aa800000, 0x00000000aa900000|  0%| F|  |TAMS 0x00000000aa800000| PB 0x00000000aa800000| Untracked |  0
| 169|0x00000000aa900000, 0x00000000aa900000, 0x00000000aaa00000|  0%| F|  |TAMS 0x00000000aa900000| PB 0x00000000aa900000| Untracked |  0
| 170|0x00000000aaa00000, 0x00000000aaa00000, 0x00000000aab00000|  0%| F|  |TAMS 0x00000000aaa00000| PB 0x00000000aaa00000| Untracked |  0
| 171|0x00000000aab00000, 0x00000000aab00000, 0x00000000aac00000|  0%| F|  |TAMS 0x00000000aab00000| PB 0x00000000aab00000| Untracked |  0
| 172|0x00000000aac00000, 0x00000000aad00000, 0x00000000aad00000|100%| S|CS|TAMS 0x00000000aac00000| PB 0x00000000aac00000| Complete |  0
| 173|0x00000000aad00000, 0x00000000aae00000, 0x00000000aae00000|100%| S|CS|TAMS 0x00000000aad00000| PB 0x00000000aad00000| Complete |  0
| 174|0x00000000aae00000, 0x00000000aaf00000, 0x00000000aaf00000|100%| S|CS|TAMS 0x00000000aae00000| PB 0x00000000aae00000| Complete |  0
| 175|0x00000000aaf00000, 0x00000000ab000000, 0x00000000ab000000|100%| S|CS|TAMS 0x00000000aaf00000| PB 0x00000000aaf00000| Complete |  0
| 176|0x00000000ab000000, 0x00000000ab100000, 0x00000000ab100000|100%| S|CS|TAMS 0x00000000ab000000| PB 0x00000000ab000000| Complete |  0
| 177|0x00000000ab100000, 0x00000000ab200000, 0x00000000ab200000|100%| S|CS|TAMS 0x00000000ab100000| PB 0x00000000ab100000| Complete |  0
| 178|0x00000000ab200000, 0x00000000ab300000, 0x00000000ab300000|100%| S|CS|TAMS 0x00000000ab200000| PB 0x00000000ab200000| Complete |  0
| 179|0x00000000ab300000, 0x00000000ab400000, 0x00000000ab400000|100%| S|CS|TAMS 0x00000000ab300000| PB 0x00000000ab300000| Complete |  0
| 180|0x00000000ab400000, 0x00000000ab500000, 0x00000000ab500000|100%| S|CS|TAMS 0x00000000ab400000| PB 0x00000000ab400000| Complete |  0
| 181|0x00000000ab500000, 0x00000000ab500000, 0x00000000ab600000|  0%| F|  |TAMS 0x00000000ab500000| PB 0x00000000ab500000| Untracked |  0
| 182|0x00000000ab600000, 0x00000000ab600000, 0x00000000ab700000|  0%| F|  |TAMS 0x00000000ab600000| PB 0x00000000ab600000| Untracked |  0
| 183|0x00000000ab700000, 0x00000000ab700000, 0x00000000ab800000|  0%| F|  |TAMS 0x00000000ab700000| PB 0x00000000ab700000| Untracked |  0
| 184|0x00000000ab800000, 0x00000000ab800000, 0x00000000ab900000|  0%| F|  |TAMS 0x00000000ab800000| PB 0x00000000ab800000| Untracked |  0
| 185|0x00000000ab900000, 0x00000000ab900000, 0x00000000aba00000|  0%| F|  |TAMS 0x00000000ab900000| PB 0x00000000ab900000| Untracked |  0
| 186|0x00000000aba00000, 0x00000000aba00000, 0x00000000abb00000|  0%| F|  |TAMS 0x00000000aba00000| PB 0x00000000aba00000| Untracked |  0
| 187|0x00000000abb00000, 0x00000000abb00000, 0x00000000abc00000|  0%| F|  |TAMS 0x00000000abb00000| PB 0x00000000abb00000| Untracked |  0
| 188|0x00000000abc00000, 0x00000000abc00000, 0x00000000abd00000|  0%| F|  |TAMS 0x00000000abc00000| PB 0x00000000abc00000| Untracked |  0
| 189|0x00000000abd00000, 0x00000000abd00000, 0x00000000abe00000|  0%| F|  |TAMS 0x00000000abd00000| PB 0x00000000abd00000| Untracked |  0
| 190|0x00000000abe00000, 0x00000000abe00000, 0x00000000abf00000|  0%| F|  |TAMS 0x00000000abe00000| PB 0x00000000abe00000| Untracked |  0
| 191|0x00000000abf00000, 0x00000000abf00000, 0x00000000ac000000|  0%| F|  |TAMS 0x00000000abf00000| PB 0x00000000abf00000| Untracked |  0
| 192|0x00000000ac000000, 0x00000000ac000000, 0x00000000ac100000|  0%| F|  |TAMS 0x00000000ac000000| PB 0x00000000ac000000| Untracked |  0
| 193|0x00000000ac100000, 0x00000000ac100000, 0x00000000ac200000|  0%| F|  |TAMS 0x00000000ac100000| PB 0x00000000ac100000| Untracked |  0
| 194|0x00000000ac200000, 0x00000000ac200000, 0x00000000ac300000|  0%| F|  |TAMS 0x00000000ac200000| PB 0x00000000ac200000| Untracked |  0
| 195|0x00000000ac300000, 0x00000000ac300000, 0x00000000ac400000|  0%| F|  |TAMS 0x00000000ac300000| PB 0x00000000ac300000| Untracked |  0
| 196|0x00000000ac400000, 0x00000000ac400000, 0x00000000ac500000|  0%| F|  |TAMS 0x00000000ac400000| PB 0x00000000ac400000| Untracked |  0
| 197|0x00000000ac500000, 0x00000000ac500000, 0x00000000ac600000|  0%| F|  |TAMS 0x00000000ac500000| PB 0x00000000ac500000| Untracked |  0
| 198|0x00000000ac600000, 0x00000000ac600000, 0x00000000ac700000|  0%| F|  |TAMS 0x00000000ac600000| PB 0x00000000ac600000| Untracked |  0
| 199|0x00000000ac700000, 0x00000000ac700000, 0x00000000ac800000|  0%| F|  |TAMS 0x00000000ac700000| PB 0x00000000ac700000| Untracked |  0
| 200|0x00000000ac800000, 0x00000000ac800000, 0x00000000ac900000|  0%| F|  |TAMS 0x00000000ac800000| PB 0x00000000ac800000| Untracked |  0
| 201|0x00000000ac900000, 0x00000000ac900000, 0x00000000aca00000|  0%| F|  |TAMS 0x00000000ac900000| PB 0x00000000ac900000| Untracked |  0
| 202|0x00000000aca00000, 0x00000000aca00000, 0x00000000acb00000|  0%| F|  |TAMS 0x00000000aca00000| PB 0x00000000aca00000| Untracked |  0
| 203|0x00000000acb00000, 0x00000000acb00000, 0x00000000acc00000|  0%| F|  |TAMS 0x00000000acb00000| PB 0x00000000acb00000| Untracked |  0
| 204|0x00000000acc00000, 0x00000000acc00000, 0x00000000acd00000|  0%| F|  |TAMS 0x00000000acc00000| PB 0x00000000acc00000| Untracked |  0
| 205|0x00000000acd00000, 0x00000000acd00000, 0x00000000ace00000|  0%| F|  |TAMS 0x00000000acd00000| PB 0x00000000acd00000| Untracked |  0
| 206|0x00000000ace00000, 0x00000000ace00000, 0x00000000acf00000|  0%| F|  |TAMS 0x00000000ace00000| PB 0x00000000ace00000| Untracked |  0
| 207|0x00000000acf00000, 0x00000000acf00000, 0x00000000ad000000|  0%| F|  |TAMS 0x00000000acf00000| PB 0x00000000acf00000| Untracked |  0
| 208|0x00000000ad000000, 0x00000000ad000000, 0x00000000ad100000|  0%| F|  |TAMS 0x00000000ad000000| PB 0x00000000ad000000| Untracked |  0
| 209|0x00000000ad100000, 0x00000000ad100000, 0x00000000ad200000|  0%| F|  |TAMS 0x00000000ad100000| PB 0x00000000ad100000| Untracked |  0
| 210|0x00000000ad200000, 0x00000000ad200000, 0x00000000ad300000|  0%| F|  |TAMS 0x00000000ad200000| PB 0x00000000ad200000| Untracked |  0
| 211|0x00000000ad300000, 0x00000000ad300000, 0x00000000ad400000|  0%| F|  |TAMS 0x00000000ad300000| PB 0x00000000ad300000| Untracked |  0
| 212|0x00000000ad400000, 0x00000000ad400000, 0x00000000ad500000|  0%| F|  |TAMS 0x00000000ad400000| PB 0x00000000ad400000| Untracked |  0
| 213|0x00000000ad500000, 0x00000000ad500000, 0x00000000ad600000|  0%| F|  |TAMS 0x00000000ad500000| PB 0x00000000ad500000| Untracked |  0
| 214|0x00000000ad600000, 0x00000000ad600000, 0x00000000ad700000|  0%| F|  |TAMS 0x00000000ad600000| PB 0x00000000ad600000| Untracked |  0
| 215|0x00000000ad700000, 0x00000000ad700000, 0x00000000ad800000|  0%| F|  |TAMS 0x00000000ad700000| PB 0x00000000ad700000| Untracked |  0
| 216|0x00000000ad800000, 0x00000000ad800000, 0x00000000ad900000|  0%| F|  |TAMS 0x00000000ad800000| PB 0x00000000ad800000| Untracked |  0
| 217|0x00000000ad900000, 0x00000000ad900000, 0x00000000ada00000|  0%| F|  |TAMS 0x00000000ad900000| PB 0x00000000ad900000| Untracked |  0
| 218|0x00000000ada00000, 0x00000000ada00000, 0x00000000adb00000|  0%| F|  |TAMS 0x00000000ada00000| PB 0x00000000ada00000| Untracked |  0
| 219|0x00000000adb00000, 0x00000000adb00000, 0x00000000adc00000|  0%| F|  |TAMS 0x00000000adb00000| PB 0x00000000adb00000| Untracked |  0
| 220|0x00000000adc00000, 0x00000000adc00000, 0x00000000add00000|  0%| F|  |TAMS 0x00000000adc00000| PB 0x00000000adc00000| Untracked |  0
| 221|0x00000000add00000, 0x00000000add00000, 0x00000000ade00000|  0%| F|  |TAMS 0x00000000add00000| PB 0x00000000add00000| Untracked |  0
| 222|0x00000000ade00000, 0x00000000ade00000, 0x00000000adf00000|  0%| F|  |TAMS 0x00000000ade00000| PB 0x00000000ade00000| Untracked |  0
| 223|0x00000000adf00000, 0x00000000adf00000, 0x00000000ae000000|  0%| F|  |TAMS 0x00000000adf00000| PB 0x00000000adf00000| Untracked |  0
| 224|0x00000000ae000000, 0x00000000ae000000, 0x00000000ae100000|  0%| F|  |TAMS 0x00000000ae000000| PB 0x00000000ae000000| Untracked |  0
| 225|0x00000000ae100000, 0x00000000ae100000, 0x00000000ae200000|  0%| F|  |TAMS 0x00000000ae100000| PB 0x00000000ae100000| Untracked |  0
| 226|0x00000000ae200000, 0x00000000ae200000, 0x00000000ae300000|  0%| F|  |TAMS 0x00000000ae200000| PB 0x00000000ae200000| Untracked |  0
| 227|0x00000000ae300000, 0x00000000ae300000, 0x00000000ae400000|  0%| F|  |TAMS 0x00000000ae300000| PB 0x00000000ae300000| Untracked |  0
| 228|0x00000000ae400000, 0x00000000ae400000, 0x00000000ae500000|  0%| F|  |TAMS 0x00000000ae400000| PB 0x00000000ae400000| Untracked |  0
| 229|0x00000000ae500000, 0x00000000ae500000, 0x00000000ae600000|  0%| F|  |TAMS 0x00000000ae500000| PB 0x00000000ae500000| Untracked |  0
| 230|0x00000000ae600000, 0x00000000ae600000, 0x00000000ae700000|  0%| F|  |TAMS 0x00000000ae600000| PB 0x00000000ae600000| Untracked |  0
| 231|0x00000000ae700000, 0x00000000ae700000, 0x00000000ae800000|  0%| F|  |TAMS 0x00000000ae700000| PB 0x00000000ae700000| Untracked |  0
| 232|0x00000000ae800000, 0x00000000ae879588, 0x00000000ae900000| 47%| E|  |TAMS 0x00000000ae800000| PB 0x00000000ae800000| Complete |  0
| 233|0x00000000ae900000, 0x00000000aea00000, 0x00000000aea00000|100%| E|CS|TAMS 0x00000000ae900000| PB 0x00000000ae900000| Complete |  0
| 234|0x00000000aea00000, 0x00000000aeb00000, 0x00000000aeb00000|100%| E|CS|TAMS 0x00000000aea00000| PB 0x00000000aea00000| Complete |  0
| 235|0x00000000aeb00000, 0x00000000aec00000, 0x00000000aec00000|100%| E|CS|TAMS 0x00000000aeb00000| PB 0x00000000aeb00000| Complete |  0
| 236|0x00000000aec00000, 0x00000000aed00000, 0x00000000aed00000|100%| E|CS|TAMS 0x00000000aec00000| PB 0x00000000aec00000| Complete |  0
| 237|0x00000000aed00000, 0x00000000aee00000, 0x00000000aee00000|100%| E|CS|TAMS 0x00000000aed00000| PB 0x00000000aed00000| Complete |  0
| 238|0x00000000aee00000, 0x00000000aef00000, 0x00000000aef00000|100%| E|CS|TAMS 0x00000000aee00000| PB 0x00000000aee00000| Complete |  0
| 239|0x00000000aef00000, 0x00000000af000000, 0x00000000af000000|100%| E|CS|TAMS 0x00000000aef00000| PB 0x00000000aef00000| Complete |  0
| 240|0x00000000af000000, 0x00000000af100000, 0x00000000af100000|100%| E|CS|TAMS 0x00000000af000000| PB 0x00000000af000000| Complete |  0
| 241|0x00000000af100000, 0x00000000af200000, 0x00000000af200000|100%| E|CS|TAMS 0x00000000af100000| PB 0x00000000af100000| Complete |  0
| 242|0x00000000af200000, 0x00000000af300000, 0x00000000af300000|100%| E|CS|TAMS 0x00000000af200000| PB 0x00000000af200000| Complete |  0
| 243|0x00000000af300000, 0x00000000af400000, 0x00000000af400000|100%| E|CS|TAMS 0x00000000af300000| PB 0x00000000af300000| Complete |  0
| 244|0x00000000af400000, 0x00000000af500000, 0x00000000af500000|100%| E|CS|TAMS 0x00000000af400000| PB 0x00000000af400000| Complete |  0
| 245|0x00000000af500000, 0x00000000af600000, 0x00000000af600000|100%| E|CS|TAMS 0x00000000af500000| PB 0x00000000af500000| Complete |  0
| 246|0x00000000af600000, 0x00000000af700000, 0x00000000af700000|100%| E|CS|TAMS 0x00000000af600000| PB 0x00000000af600000| Complete |  0
| 247|0x00000000af700000, 0x00000000af800000, 0x00000000af800000|100%| E|CS|TAMS 0x00000000af700000| PB 0x00000000af700000| Complete |  0
| 248|0x00000000af800000, 0x00000000af900000, 0x00000000af900000|100%| E|CS|TAMS 0x00000000af800000| PB 0x00000000af800000| Complete |  0
| 249|0x00000000af900000, 0x00000000afa00000, 0x00000000afa00000|100%| E|CS|TAMS 0x00000000af900000| PB 0x00000000af900000| Complete |  0
| 250|0x00000000afa00000, 0x00000000afb00000, 0x00000000afb00000|100%| E|CS|TAMS 0x00000000afa00000| PB 0x00000000afa00000| Complete |  0
| 251|0x00000000afb00000, 0x00000000afc00000, 0x00000000afc00000|100%| E|CS|TAMS 0x00000000afb00000| PB 0x00000000afb00000| Complete |  0

Card table byte_map: [0x00000177e5050000,0x00000177e5350000] _byte_map_base: 0x00000177e4b50000

Marking Bits: (CMBitMap*) 0x00000177ccff1630
 Bits: [0x00000177e5350000, 0x00000177e6b50000)

Polling page: 0x00000177cafe0000

Metaspace:

Usage:
  Non-class:     15.96 MB used.
      Class:      2.73 MB used.
       Both:     18.70 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      16.25 MB ( 25%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.00 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      19.25 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  15.39 MB
       Class:  12.96 MB
        Both:  28.35 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 824.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 308.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1509.
num_chunk_merges: 0.
num_chunk_splits: 991.
num_chunks_enlarged: 665.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120064Kb used=1198Kb max_used=1198Kb free=118865Kb
 bounds [0x00000177dd1a0000, 0x00000177dd410000, 0x00000177e46e0000]
CodeHeap 'profiled nmethods': size=120000Kb used=4743Kb max_used=4743Kb free=115256Kb
 bounds [0x00000177d56e0000, 0x00000177d5b90000, 0x00000177dcc10000]
CodeHeap 'non-nmethods': size=5696Kb used=1503Kb max_used=1549Kb free=4192Kb
 bounds [0x00000177dcc10000, 0x00000177dce80000, 0x00000177dd1a0000]
CodeCache: size=245760Kb, used=7444Kb, max_used=7490Kb, free=238313Kb
 total_blobs=3735, nmethods=3143, adapters=497, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 7.999 Thread 0x00000177e92a74e0 nmethod 3083 0x00000177d5b67288 code [0x00000177d5b673a0, 0x00000177d5b675a8]
Event: 8.022 Thread 0x00000177e92a74e0 3084       3       org.gradle.internal.service.DefaultServiceRegistry$SingletonService::<init> (58 bytes)
Event: 8.023 Thread 0x00000177e92a74e0 nmethod 3084 0x00000177d5b67608 code [0x00000177d5b677e0, 0x00000177d5b68348]
Event: 8.089 Thread 0x00000177e92a74e0 3085       3       org.gradle.internal.service.DefaultServiceRegistry::validateImplementationForServiceTypes (84 bytes)
Event: 8.091 Thread 0x00000177e92a74e0 nmethod 3085 0x00000177d5b68408 code [0x00000177d5b686a0, 0x00000177d5b69848]
Event: 8.094 Thread 0x00000177e92a74e0 3086       3       jdk.internal.reflect.ReflectionFactory::copyConstructor (11 bytes)
Event: 8.094 Thread 0x00000177e92a74e0 nmethod 3086 0x00000177d5b69908 code [0x00000177d5b69a40, 0x00000177d5b69c38]
Event: 8.094 Thread 0x00000177e92a74e0 3087       3       java.lang.reflect.ReflectAccess::copyConstructor (5 bytes)
Event: 8.094 Thread 0x00000177e92a74e0 nmethod 3087 0x00000177d5b69c88 code [0x00000177d5b69da0, 0x00000177d5b69ee0]
Event: 8.096 Thread 0x00000177e92a74e0 3088       3       java.lang.Class::privateGetDeclaredConstructors (79 bytes)
Event: 8.097 Thread 0x00000177e92a74e0 nmethod 3088 0x00000177d5b69f88 code [0x00000177d5b6a100, 0x00000177d5b6a5e0]
Event: 8.210 Thread 0x00000177e92a74e0 3089       3       java.util.stream.StreamSupport::stream (19 bytes)
Event: 8.210 Thread 0x00000177e92a74e0 nmethod 3089 0x00000177d5b6a608 code [0x00000177d5b6a760, 0x00000177d5b6ab88]
Event: 8.371 Thread 0x00000177e92a74e0 3090       1       jdk.internal.event.ThreadSleepEvent::isEnabled (2 bytes)
Event: 8.371 Thread 0x00000177e92a74e0 nmethod 3090 0x00000177dd2bdd08 code [0x00000177dd2bde20, 0x00000177dd2bdee8]
Event: 8.399 Thread 0x00000177e92a74e0 3092       1       org.gradle.launcher.daemon.server.exec.DaemonConnectionBackedEventConsumer::access$000 (5 bytes)
Event: 8.400 Thread 0x00000177e92a74e0 nmethod 3092 0x00000177dd2be008 code [0x00000177dd2be120, 0x00000177dd2be1d0]
Event: 9.127 Thread 0x00000177e92a74e0 3094       3       java.util.concurrent.locks.ReentrantLock$NonfairSync::tryAcquire (27 bytes)
Event: 9.127 Thread 0x00000177e92a74e0 nmethod 3094 0x00000177d5b6ac08 code [0x00000177d5b6ad40, 0x00000177d5b6afb8]
Event: 9.252 Thread 0x00000177e92a74e0 3095       3       java.lang.invoke.LambdaForm::create (14 bytes)

GC Heap History (8 events):
Event: 1.437 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 23552K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 0 survivors (0K)
 Metaspace       used 1098K, committed 1216K, reserved 1114112K
  class space    used 78K, committed 128K, reserved 1048576K
}
Event: 1.444 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 4859K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 1098K, committed 1216K, reserved 1114112K
  class space    used 78K, committed 128K, reserved 1048576K
}
Event: 3.357 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 31483K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 3 survivors (3072K)
 Metaspace       used 4605K, committed 4800K, reserved 1114112K
  class space    used 595K, committed 704K, reserved 1048576K
}
Event: 3.364 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 11003K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 4605K, committed 4800K, reserved 1114112K
  class space    used 595K, committed 704K, reserved 1048576K
}
Event: 4.422 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 52987K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 44 young (45056K), 4 survivors (4096K)
 Metaspace       used 4728K, committed 4928K, reserved 1114112K
  class space    used 597K, committed 704K, reserved 1048576K
}
Event: 4.428 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 14926K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 4728K, committed 4928K, reserved 1114112K
  class space    used 597K, committed 704K, reserved 1048576K
}
Event: 7.518 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 84558K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 71 young (72704K), 3 survivors (3072K)
 Metaspace       used 16718K, committed 17152K, reserved 1114112K
  class space    used 2356K, committed 2560K, reserved 1048576K
}
Event: 7.532 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total reserved 1572864K, committed 258048K, used 21815K [0x00000000a0000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 16718K, committed 17152K, reserved 1114112K
  class space    used 2356K, committed 2560K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.029 Loaded shared library C:\Program Files\Java\jdk-23\bin\java.dll
Event: 0.118 Loaded shared library C:\Program Files\Java\jdk-23\bin\jsvml.dll
Event: 0.252 Loaded shared library C:\Program Files\Java\jdk-23\bin\zip.dll
Event: 0.265 Loaded shared library C:\Program Files\Java\jdk-23\bin\instrument.dll
Event: 0.276 Loaded shared library C:\Program Files\Java\jdk-23\bin\net.dll
Event: 0.284 Loaded shared library C:\Program Files\Java\jdk-23\bin\nio.dll
Event: 0.292 Loaded shared library C:\Program Files\Java\jdk-23\bin\zip.dll
Event: 1.112 Loaded shared library C:\Program Files\Java\jdk-23\bin\jimage.dll
Event: 1.565 Loaded shared library C:\Program Files\Java\jdk-23\bin\verify.dll
Event: 2.204 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 2.232 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 5.081 Loaded shared library C:\Program Files\Java\jdk-23\bin\management.dll
Event: 5.088 Loaded shared library C:\Program Files\Java\jdk-23\bin\management_ext.dll
Event: 5.588 Loaded shared library C:\Program Files\Java\jdk-23\bin\extnet.dll
Event: 5.913 Loaded shared library C:\Program Files\Java\jdk-23\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 7.298 Thread 0x00000177c6480e30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000177dd1c3460 relative=0x0000000000000b80
Event: 7.298 Thread 0x00000177c6480e30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000177dd1c3460 method=java.io.WinNTFileSystem.resolve(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; @ 42 c2
Event: 7.298 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177dd1c3460 sp=0x00000056cd3fb690
Event: 7.298 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64402 sp=0x00000056cd3fb628 mode 2
Event: 7.303 Thread 0x00000177c6480e30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000177dd1cc5dc relative=0x000000000000059c
Event: 7.303 Thread 0x00000177c6480e30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000177dd1cc5dc method=java.io.File.getName()Ljava/lang/String; @ 16 c2
Event: 7.303 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177dd1cc5dc sp=0x00000056cd3fb670
Event: 7.303 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64402 sp=0x00000056cd3fb640 mode 2
Event: 7.443 Thread 0x00000177c6480e30 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000177dd1c5e84 relative=0x00000000000000e4
Event: 7.443 Thread 0x00000177c6480e30 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000177dd1c5e84 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 7.443 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177dd1c5e84 sp=0x00000056cd3fc6d0
Event: 7.443 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64402 sp=0x00000056cd3fc610 mode 2
Event: 7.518 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177d5ae6232 sp=0x00000056cd3fc1e0
Event: 7.518 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64b22 sp=0x00000056cd3fb6f0 mode 0
Event: 7.961 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177d5ae6211 sp=0x00000056cd3fbce0
Event: 7.961 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64b22 sp=0x00000056cd3fb1f0 mode 0
Event: 8.147 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177d5ae6211 sp=0x00000056cd3fbca0
Event: 8.148 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64b22 sp=0x00000056cd3fb1b0 mode 0
Event: 8.191 Thread 0x00000177c6480e30 DEOPT PACKING pc=0x00000177d5ae6232 sp=0x00000056cd3fa700
Event: 8.191 Thread 0x00000177c6480e30 DEOPT UNPACKING pc=0x00000177dcc64b22 sp=0x00000056cd3f9c10 mode 0

Classes loaded (20 events):
Event: 7.644 Loading class java/lang/invoke/BoundMethodHandle$Species_LLLLLLLLLLLLLLLLLLLLLLL
Event: 7.644 Loading class java/lang/invoke/BoundMethodHandle$Species_LLLLLLLLLLLLLLLLLLLLLLL done
Event: 7.721 Loading class java/util/Collections$2
Event: 7.722 Loading class java/util/Collections$2 done
Event: 7.784 Loading class java/io/ObjectOutputStream$BlockDataOutputStream
Event: 7.786 Loading class java/io/ObjectOutputStream$BlockDataOutputStream done
Event: 7.786 Loading class java/io/ObjectOutputStream$HandleTable
Event: 7.788 Loading class java/io/ObjectOutputStream$HandleTable done
Event: 7.788 Loading class java/io/ObjectOutputStream$ReplaceTable
Event: 7.788 Loading class java/io/ObjectOutputStream$ReplaceTable done
Event: 7.793 Loading class java/io/UnsupportedEncodingException
Event: 7.793 Loading class java/io/UnsupportedEncodingException done
Event: 7.794 Loading class java/nio/charset/CharacterCodingException
Event: 7.794 Loading class java/nio/charset/CharacterCodingException done
Event: 7.794 Loading class java/util/stream/IntStream
Event: 7.795 Loading class java/util/stream/IntStream done
Event: 7.795 Loading class java/lang/foreign/MemorySegment
Event: 7.796 Loading class java/lang/foreign/MemorySegment done
Event: 8.082 Loading class java/util/function/LongFunction
Event: 8.082 Loading class java/util/function/LongFunction done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 7.395 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab72fc98}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ab72fc98) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 7.396 Thread 0x00000177c6480e30 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000ab7338b8}: Found class java.lang.Object, but interface was expected> (0x00000000ab7338b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]
Event: 7.443 Thread 0x00000177c6480e30 Implicit null exception at 0x00000177dd1c5dd6 to 0x00000177dd1c5e7a
Event: 7.482 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ab5265f8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ab5265f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 7.592 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000afaff478}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.O
Event: 7.603 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af96cf50}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000af96cf50) 
thrown [s\open\src\hotspot\share\interprete
Event: 7.609 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af995f38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000af995f38) 
thrown [s\open\src\hotspo
Event: 7.614 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af9bffe0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000af9bffe0) 
thrown 
Event: 7.618 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af9eb4e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000
Event: 7.623 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af8180d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 7.628 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af845908}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 7.632 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af873be0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 7.638 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af8a30c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 7.642 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af8d2ec0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 7.646 Thread 0x00000177c6480e30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000af704ad8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Obj
Event: 7.689 Thread 0x00000177c6480e30 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000af6355c8}: Found class java.lang.Object, but interface was expected> (0x00000000af6355c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]
Event: 7.790 Thread 0x00000177c64821e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000afb810b0}: static Lorg/gradle/internal/build/event/types/DefaultOperationStartedProgressEvent;.<clinit>()V> (0x00000000afb810b0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 7.791 Thread 0x00000177c64821e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000afb826d8}: static Lorg/gradle/internal/build/event/types/AbstractProgressEvent;.<clinit>()V> (0x00000000afb826d8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 7.792 Thread 0x00000177c64821e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000afb884b0}: static Lorg/gradle/internal/build/event/types/DefaultOperationDescriptor;.<clinit>()V> (0x00000000afb884b0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1111]
Event: 7.798 Thread 0x00000177c64821e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000afb9d7c0}: static Lorg/gradle/internal/operations/OperationIdentifier;.<clinit>()V> (0x00000000afb9d7c0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1111]

VM Operations (20 events):
Event: 5.157 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.157 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.232 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.232 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.300 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.300 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.404 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.404 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.526 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.526 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 5.881 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 5.881 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 7.093 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 7.093 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 7.518 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 7.532 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 7.660 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 7.660 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 7.661 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 7.661 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 1.620 Thread 0x00000177e92a74e0 Thread added: 0x00000177c6259410
Event: 1.896 Thread 0x00000177c6259410 Thread exited: 0x00000177c6259410
Event: 2.492 Thread 0x00000177e92a74e0 Thread added: 0x00000177c63bc5c0
Event: 3.700 Thread 0x00000177c63bc5c0 Thread exited: 0x00000177c63bc5c0
Event: 4.738 Thread 0x00000177e92a74e0 Thread added: 0x00000177c7052dd0
Event: 5.159 Thread 0x00000177ccfa6700 Thread added: 0x00000177e929b910
Event: 5.683 Thread 0x00000177ccfa6700 Thread added: 0x00000177e9298490
Event: 5.859 Thread 0x00000177ccfa6700 Thread added: 0x00000177e929abf0
Event: 5.972 Thread 0x00000177e9298490 Thread added: 0x00000177e9299840
Event: 5.979 Thread 0x00000177e9299840 Thread added: 0x00000177c6482870
Event: 6.061 Thread 0x00000177c7052dd0 Thread exited: 0x00000177c7052dd0
Event: 6.090 Thread 0x00000177e9299840 Thread added: 0x00000177c64842b0
Event: 6.104 Thread 0x00000177e9299840 Thread added: 0x00000177c6480e30
Event: 6.127 Thread 0x00000177c6480e30 Thread added: 0x00000177c6481b50
Event: 6.135 Thread 0x00000177c6480e30 Thread added: 0x00000177c6483590
Event: 6.140 Thread 0x00000177c6480e30 Thread added: 0x00000177c64821e0
Event: 7.093 Thread 0x00000177c6480e30 Thread added: 0x00000177c6484940
Event: 7.113 Thread 0x00000177c6480e30 Thread added: 0x00000177c64814c0
Event: 7.162 Thread 0x00000177e92a74e0 Thread added: 0x00000177c7446bd0
Event: 7.192 Thread 0x00000177c6480e30 Thread added: 0x00000177c6486380


Dynamic libraries:
0x00007ff640ec0000 - 0x00007ff640ed0000 	C:\Program Files\Java\jdk-23\bin\java.exe
0x00007ffbc7a80000 - 0x00007ffbc7ce7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffb86730000 - 0x00007ffb86750000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ffbc6860000 - 0x00007ffbc6929000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffbc5090000 - 0x00007ffbc5480000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffbab460000 - 0x00007ffbab477000 	C:\Program Files\Java\jdk-23\bin\jli.dll
0x00007ffbc6df0000 - 0x00007ffbc6fb5000 	C:\Windows\System32\USER32.dll
0x00007ffbc4fc0000 - 0x00007ffbc4fe7000 	C:\Windows\System32\win32u.dll
0x00007ffbc5490000 - 0x00007ffbc55db000 	C:\Windows\System32\ucrtbase.dll
0x00007ffbad6b0000 - 0x00007ffbad6cb000 	C:\Program Files\Java\jdk-23\bin\VCRUNTIME140.dll
0x00007ffb99790000 - 0x00007ffb99a2a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c\COMCTL32.dll
0x00007ffbc62c0000 - 0x00007ffbc6369000 	C:\Windows\System32\msvcrt.dll
0x00007ffbc6b70000 - 0x00007ffbc6b9b000 	C:\Windows\System32\GDI32.dll
0x00007ffbc4c40000 - 0x00007ffbc4d78000 	C:\Windows\System32\gdi32full.dll
0x00007ffbc55e0000 - 0x00007ffbc5683000 	C:\Windows\System32\msvcp_win.dll
0x00007ffbc5810000 - 0x00007ffbc583f000 	C:\Windows\System32\IMM32.DLL
0x00007ffbbcfb0000 - 0x00007ffbbcfbc000 	C:\Program Files\Java\jdk-23\bin\vcruntime140_1.dll
0x00007ffb7a330000 - 0x00007ffb7a3be000 	C:\Program Files\Java\jdk-23\bin\msvcp140.dll
0x00007ffb2f460000 - 0x00007ffb301b0000 	C:\Program Files\Java\jdk-23\bin\server\jvm.dll
0x00007ffbc6600000 - 0x00007ffbc66b4000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffbc6d40000 - 0x00007ffbc6de6000 	C:\Windows\System32\sechost.dll
0x00007ffbc6370000 - 0x00007ffbc6488000 	C:\Windows\System32\RPCRT4.dll
0x00007ffbc7530000 - 0x00007ffbc75a4000 	C:\Windows\System32\WS2_32.dll
0x00007ffba9580000 - 0x00007ffba95b5000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffbc4a30000 - 0x00007ffbc4a8e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffbb9630000 - 0x00007ffbb963b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffbc4a10000 - 0x00007ffbc4a24000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffbc3930000 - 0x00007ffbc394b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffbb9c10000 - 0x00007ffbb9c1a000 	C:\Program Files\Java\jdk-23\bin\jimage.dll
0x00007ffbc2290000 - 0x00007ffbc24d1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffbc70d0000 - 0x00007ffbc7455000 	C:\Windows\System32\combase.dll
0x00007ffbc61d0000 - 0x00007ffbc62b0000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffbb8bd0000 - 0x00007ffbb8c13000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffbc4ff0000 - 0x00007ffbc5089000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffb94f90000 - 0x00007ffb94f9f000 	C:\Program Files\Java\jdk-23\bin\instrument.dll
0x00007ffb94f10000 - 0x00007ffb94f2e000 	C:\Program Files\Java\jdk-23\bin\java.dll
0x00007ffbc6020000 - 0x00007ffbc61c0000 	C:\Windows\System32\ole32.dll
0x00007ffbc5860000 - 0x00007ffbc5fad000 	C:\Windows\System32\SHELL32.dll
0x00007ffbc5690000 - 0x00007ffbc5803000 	C:\Windows\System32\wintypes.dll
0x00007ffbc2750000 - 0x00007ffbc2faf000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffbc6fc0000 - 0x00007ffbc70b5000 	C:\Windows\System32\SHCORE.dll
0x00007ffbc5fb0000 - 0x00007ffbc601a000 	C:\Windows\System32\shlwapi.dll
0x00007ffbc4ad0000 - 0x00007ffbc4af9000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffb67fb0000 - 0x00007ffb68087000 	C:\Program Files\Java\jdk-23\bin\jsvml.dll
0x00007ffb8e410000 - 0x00007ffb8e427000 	C:\Program Files\Java\jdk-23\bin\zip.dll
0x00007ffb94cf0000 - 0x00007ffb94d00000 	C:\Program Files\Java\jdk-23\bin\net.dll
0x00007ffbc3ec0000 - 0x00007ffbc3f2b000 	C:\Windows\system32\mswsock.dll
0x00007ffb8d4c0000 - 0x00007ffb8d4d6000 	C:\Program Files\Java\jdk-23\bin\nio.dll
0x00007ffb8d040000 - 0x00007ffb8d050000 	C:\Program Files\Java\jdk-23\bin\verify.dll
0x00007ffb80120000 - 0x00007ffb80147000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffb1c6f0000 - 0x00007ffb1c834000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffb8d030000 - 0x00007ffb8d03a000 	C:\Program Files\Java\jdk-23\bin\management.dll
0x00007ffb8cf90000 - 0x00007ffb8cf9b000 	C:\Program Files\Java\jdk-23\bin\management_ext.dll
0x00007ffbc6ba0000 - 0x00007ffbc6ba8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffbc4190000 - 0x00007ffbc41ab000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffbc3890000 - 0x00007ffbc38cb000 	C:\Windows\system32\rsaenh.dll
0x00007ffbc3f60000 - 0x00007ffbc3f8b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffbc4aa0000 - 0x00007ffbc4ac6000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffbc4180000 - 0x00007ffbc418c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffbc32e0000 - 0x00007ffbc3313000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffbc70c0000 - 0x00007ffbc70ca000 	C:\Windows\System32\NSI.dll
0x00007ffb8cf20000 - 0x00007ffb8cf29000 	C:\Program Files\Java\jdk-23\bin\extnet.dll
0x00007ffb8cf10000 - 0x00007ffb8cf1e000 	C:\Program Files\Java\jdk-23\bin\sunmscapi.dll
0x00007ffbc4d80000 - 0x00007ffbc4ef7000 	C:\Windows\System32\CRYPT32.dll
0x00007ffbc43a0000 - 0x00007ffbc43d0000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffbc4350000 - 0x00007ffbc438f000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffb817a0000 - 0x00007ffb817a8000 	C:\Windows\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-23\bin;C:\Windows\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c;C:\Program Files\Java\jdk-23\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx1536m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-all\2qik7nd48slq1ooc2496ixf4i\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-all\2qik7nd48slq1ooc2496ixf4i\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MarkStackSizeMax                         = 536870912                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 1610612736                                {product} {command line}
   size_t MaxNewSize                               = 965738496                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832704                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122945536                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122880000                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1610612736                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-23
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk-23\bin;C:\Program Files\MongoDB\Server\8.0\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;C:\cygwin64\bin;C:\Program Files\Java\jdk-23\bin;C:\Program Files\Apache Software Foundation\Tomcat 10.01\bin;C:\Program Files\Redis\;C:\Program Files (x86)\cloudflared\;C:\Program Files\php-8.4.7-Win32-vs17-x64;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\apache-maven-3.9.10\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\mongosh\;C:\Program Files\nodejs;C:\Program Files\apache-ant-1.10.15\bin;set TA_LIBRARY_PATH=C:\ta-lib\lib;set TA_INCLUDE_PATH=C:\ta-lib\include;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\LiveKit.LiveKitCLI_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Roaming\npm;C:\flutter\bin;C:\apache-maven-3.9.10\bin;
USERNAME=Anshdeep
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
OS uptime: 0 days 19:17 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x435, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, gfni, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 1
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 2
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 3
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 4
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 5
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 6
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 7
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000
Processor Information for processor 8
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500
Processor Information for processor 9
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500
Processor Information for processor 10
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500
Processor Information for processor 11
  Max Mhz: 2000, Current Mhz: 1500, Mhz Limit: 1500

Memory: 4k page, system-wide physical 16076M (1131M free)
TotalPageFile size 32951M (AvailPageFile size 7M)
current process WorkingSet (physical memory assigned to process): 217M, peak: 223M
current process commit charge ("private bytes"): 428M, peak: 435M

vm_info: Java HotSpot(TM) 64-Bit Server VM (23.0.2+7-58) for windows-amd64 JRE (23.0.2+7-58), built on 2024-11-29T09:34:55Z with MS VC++ 17.6 (VS2022)

END.
