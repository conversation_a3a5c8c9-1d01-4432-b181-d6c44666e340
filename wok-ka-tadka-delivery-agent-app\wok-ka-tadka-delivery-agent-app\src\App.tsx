
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useAndroidBackButton } from "@/hooks/use-android-back-button";
import { useStatusBar } from "@/hooks/use-status-bar";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import DeliveryLogin from "./pages/delivery/Login";
import DeliveryDashboard from "./pages/delivery/Dashboard";
import DeliveryAssignments from "./pages/delivery/Assignments";
import DeliveryDetails from "./pages/delivery/DeliveryDetails";
import CurrentOrders from "./pages/delivery/CurrentOrders";
import ActiveOrders from "./pages/delivery/ActiveOrders";
import History from "./pages/delivery/History";
import Profile from "./pages/delivery/Profile";
import TableManagement from "./pages/delivery/TableManagement";
import TableOrder from "./pages/delivery/TableOrder";
import KOTGeneration from "./pages/delivery/KOTGeneration";
import KOTHistory from "./pages/delivery/KOTHistory";
import KOTDetails from "./pages/delivery/KOTDetails";
import TableDetails from "./pages/delivery/TableDetails";
import Attendance from "./pages/delivery/Attendance";
// Admin Components
import AdminDashboard from "./pages/admin/Dashboard";
import SalesAnalytics from "./pages/admin/SalesAnalytics";
import OrderManagement from "./pages/admin/OrderManagement";
import OrderDetails from "./pages/admin/OrderDetails";
import InventoryManagement from "./pages/admin/InventoryManagement";
import AdminTableManagement from "./pages/admin/AdminTableManagement";
import AdminTableDetails from "./pages/admin/AdminTableDetails";
import AdminKOTManagement from "./pages/admin/KOTManagement";
import AdminKOTGeneration from "./pages/admin/AdminKOTGeneration";
import BillGeneration from "./pages/admin/BillGeneration";
import StaffManagement from "./pages/admin/StaffManagement";
import AdminOrderTaking from "./pages/admin/AdminOrderTaking";
import Reports from "./pages/admin/Reports";
import StaffPinManagement from "./pages/admin/StaffPinManagement";
import AttendanceTracking from "./pages/admin/AttendanceTracking";
import MultiPlatformOrders from "./pages/admin/MultiPlatformOrders";
import MultiPlatformOrderDetails from "./pages/admin/MultiPlatformOrderDetails";
import MultiPlatformSettings from "./pages/admin/MultiPlatformSettings";
import StaffAttendanceDetails from "./pages/admin/StaffAttendanceDetails";
import SalaryManagement from "./pages/admin/SalaryManagement";
import SalaryDetails from "./pages/admin/SalaryDetails";
import MenuManagement from "./pages/admin/MenuManagement";
import SalaryGeneration from "./pages/admin/SalaryGeneration";
import AdvanceManagement from "./pages/admin/AdvanceManagement";
import Settings from "./pages/admin/Settings";
import Backup from "./pages/admin/Backup";
import OffersManagement from "./pages/admin/OffersManagement";
import Notifications from "./pages/admin/Notifications";

const queryClient = new QueryClient();

// Component that handles Android back button and status bar
const AppWithBackButton = () => {
  useAndroidBackButton();
  useStatusBar();

  return (
    <Routes>
      <Route path="/" element={<Index />} />
      {/* Delivery App Routes */}
      <Route path="/delivery/login" element={<DeliveryLogin />} />
      <Route path="/delivery/dashboard" element={<DeliveryDashboard />} />
      <Route path="/delivery/assignments" element={<DeliveryAssignments />} />
      <Route path="/delivery/details/:orderId" element={<DeliveryDetails />} />
      <Route path="/delivery/current" element={<CurrentOrders />} />
      <Route path="/delivery/active-orders" element={<ActiveOrders />} />
      <Route path="/delivery/history" element={<History />} />
      <Route path="/delivery/profile" element={<Profile />} />
      {/* Waiter/Table Management Routes */}
      <Route path="/delivery/tables" element={<TableManagement />} />
      <Route path="/delivery/table-order/:tableId" element={<TableOrder />} />
      <Route path="/delivery/table-details/:tableId" element={<TableDetails />} />
      <Route path="/delivery/kot/:tableId" element={<KOTGeneration />} />
      <Route path="/delivery/kot-history" element={<KOTHistory />} />
      <Route path="/delivery/kot-details/:kotId" element={<KOTDetails />} />
      <Route path="/delivery/attendance" element={<Attendance />} />
      {/* Admin Routes */}
      <Route path="/admin/dashboard" element={<AdminDashboard />} />
      <Route path="/admin/sales" element={<SalesAnalytics />} />
      <Route path="/admin/orders" element={<OrderManagement />} />
      <Route path="/admin/order-details/:orderId" element={<OrderDetails />} />
      <Route path="/admin/inventory" element={<InventoryManagement />} />
      <Route path="/admin/tables" element={<AdminTableManagement />} />
      <Route path="/admin/table-details/:tableId" element={<AdminTableDetails />} />
      <Route path="/admin/kot" element={<AdminKOTManagement />} />
      <Route path="/admin/bills" element={<BillGeneration />} />
      <Route path="/admin/staff" element={<StaffManagement />} />
      <Route path="/admin/take-order" element={<AdminOrderTaking />} />
      <Route path="/admin/kot/:tableId" element={<AdminKOTGeneration />} />
      <Route path="/admin/reports" element={<Reports />} />
      <Route path="/admin/staff-pins" element={<StaffPinManagement />} />
      <Route path="/admin/attendance" element={<AttendanceTracking />} />
      <Route path="/admin/staff-attendance/:staffId" element={<StaffAttendanceDetails />} />
      <Route path="/admin/salary-management" element={<SalaryManagement />} />
      <Route path="/admin/salary-details/:recordId" element={<SalaryDetails />} />
      <Route path="/admin/menu-management" element={<MenuManagement />} />
      <Route path="/admin/generate-salary" element={<SalaryGeneration />} />
      <Route path="/admin/advance-management" element={<AdvanceManagement />} />
      <Route path="/admin/offers" element={<OffersManagement />} />
      <Route path="/admin/multi-platform-orders" element={<MultiPlatformOrders />} />
      <Route path="/admin/multi-platform-order/:orderId" element={<MultiPlatformOrderDetails />} />
      <Route path="/admin/multi-platform-settings" element={<MultiPlatformSettings />} />
      <Route path="/admin/notifications" element={<Notifications />} />
      <Route path="/admin/settings" element={<Settings />} />
      <Route path="/admin/backup" element={<Backup />} />
      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <div className="capacitor-app">
          <AppWithBackButton />
        </div>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
