import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  Plus,
  Minus,
  ShoppingCart,
  Search,
  Filter,
  FileText,
  CheckCircle,
  Star,
  Clock,
  Leaf,
  X
} from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { completeMenuItems } from "@/data/completeMenuData";
import { kotStorage } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
  isVeg: boolean;
  isEgg?: boolean;
  isPopular?: boolean;
  isBestseller?: boolean;
}

const AdminOrderTaking = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();

  // Get table ID from URL params
  const tableId = searchParams.get('table') || '';

  const [searchQuery, setSearchQuery] = useState("");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [existingKOT, setExistingKOT] = useState<any>(null);

  // Simple filter state matching waiter dashboard exactly
  const [selectedFilters, setSelectedFilters] = useState({
    allItems: true,
    popular: false,
    bestseller: false,
    veg: false,
    nonVeg: false,
    egg: false,
  });

  // Check for existing KOT when component mounts
  useEffect(() => {
    if (tableId) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (activeKOT) {
        setExistingKOT(activeKOT);
      }
    }
  }, [tableId]);

  // Get all items from all categories and add missing properties - matching waiter dashboard exactly
  const allItems = Object.entries(completeMenuItems).flatMap(([categoryKey, items]) =>
    items.map(item => ({
      ...item,
      id: item.id || `${categoryKey}-${item.name.replace(/\s+/g, '-').toLowerCase()}`,
      category: categoryKey,
      image: item.image || `/Menu_Images/${item.name.replace(/\s+/g, '_')}.jpg`,
      isVeg: item.isVeg !== undefined ? item.isVeg : true,
      isEgg: item.isEgg || false,
      isPopular: item.isPopular || false,
      isBestseller: item.isBestseller || false,
      preparationTime: item.preparationTime || 15
    }))
  );

  // Filter items based on selected filters - exact same logic as waiter dashboard
  const filteredItems = allItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply filter logic based on selected filters
    if (selectedFilters.allItems) return matchesSearch;

    let matchesFilter = false;

    // Special categories - exact same as waiter dashboard
    if (selectedFilters.popular && item.isPopular) matchesFilter = true;
    if (selectedFilters.bestseller && item.isBestseller) matchesFilter = true;
    if (selectedFilters.veg && item.isVeg && !item.isEgg) matchesFilter = true;
    if (selectedFilters.nonVeg && !item.isVeg && !item.isEgg) matchesFilter = true;
    if (selectedFilters.egg && item.isEgg) matchesFilter = true;

    return matchesSearch && matchesFilter;
  });

  // Filter handling - exact same as waiter dashboard
  const handleFilterChange = (filterKey: string, checked: boolean) => {
    if (filterKey === 'allItems') {
      setSelectedFilters({
        allItems: true,
        popular: false,
        bestseller: false,
        veg: false,
        nonVeg: false,
        egg: false,
      });
    } else {
      setSelectedFilters(prev => ({
        ...prev,
        allItems: false,
        [filterKey]: checked
      }));
    }
  };

  const clearAllFilters = () => {
    setSelectedFilters({
      allItems: true,
      popular: false,
      bestseller: false,
      veg: false,
      nonVeg: false,
      egg: false,
    });
  };
  // Cart functions - exact same as waiter dashboard
  const addToCart = (item: any) => {
    const existingItem = cart.find(cartItem => cartItem.id === item.id);
    if (existingItem) {
      setCart(cart.map(cartItem =>
        cartItem.id === item.id
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      ));
    } else {
      setCart([...cart, { ...item, quantity: 1 }]);
    }
  };

  const removeFromCart = (itemId: string) => {
    const existingItem = cart.find(cartItem => cartItem.id === itemId);
    if (existingItem && existingItem.quantity > 1) {
      setCart(cart.map(cartItem =>
        cartItem.id === itemId
          ? { ...cartItem, quantity: cartItem.quantity - 1 }
          : cartItem
      ));
    } else {
      setCart(cart.filter(cartItem => cartItem.id !== itemId));
    }
  };

  const getItemQuantity = (itemId: string) => {
    const item = cart.find(cartItem => cartItem.id === itemId);
    return item ? item.quantity : 0;
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };





  return (
    <div className="min-h-screen bg-gray-50 mobile-viewport">
      {/* Header - matching waiter dashboard exactly */}
      <div className="bg-gradient-primary text-white p-4 mobile-safe-header">
        <div className="flex items-center gap-3 mb-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/20 back-button-highlight tap-target"
            onClick={() => navigate("/admin/tables")}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Table {tableId}</h1>
            <p className="text-white/80 text-sm">Take Order</p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 h-4 w-4" />
          <Input
            placeholder="Search menu items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60 focus:bg-white/20"
          />
        </div>

        {/* Filter Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsFilterOpen(true)}
          className="text-white hover:bg-white/20 border border-white/20"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filter Categories
        </Button>
      </div>

      {/* Menu Items - matching waiter dashboard exactly */}
      <div className="p-4 pb-20 mobile-safe-content">
        <div className="grid grid-cols-1 gap-4">{filteredItems.map((item) => (
            <Card key={item.id} className="shadow-card border-0 bg-white overflow-hidden">
              <CardContent className="p-0">
                <div className="flex gap-4">
                  <div className="w-20 h-20 sm:w-24 sm:h-24 flex-shrink-0">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover rounded-l-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/img/app.png';
                      }}
                    />
                  </div>
                  <div className="flex-1 p-3 sm:p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-gray-900 text-sm sm:text-base line-clamp-1">
                            {item.name}
                          </h3>
                          {item.isVeg ? (
                            <div className="w-4 h-4 border-2 border-green-500 flex items-center justify-center">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            </div>
                          ) : item.isEgg ? (
                            <div className="w-4 h-4 border-2 border-yellow-500 flex items-center justify-center">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            </div>
                          ) : (
                            <div className="w-4 h-4 border-2 border-red-500 flex items-center justify-center">
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            </div>
                          )}
                          {item.isPopular && (
                            <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs px-1 py-0">
                              Popular
                            </Badge>
                          )}
                          {item.isBestseller && (
                            <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs px-1 py-0">
                              Bestseller
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-2 text-xs text-gray-600 mb-2">
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            <span>{item.rating || 4.5}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{item.preparationTime} mins</span>
                          </div>
                        </div>
                        <div className="text-lg font-bold text-primary">₹{item.price}</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {getItemQuantity(item.id) === 0 ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addToCart(item)}
                          className="text-primary border-primary hover:bg-primary hover:text-white"
                        >
                          Add
                        </Button>
                      ) : (
                        <div className="flex items-center gap-2 bg-primary text-white rounded-md px-2 py-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFromCart(item.id)}
                            className="h-5 w-5 text-white hover:bg-white/20"
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="text-sm font-medium min-w-[20px] text-center">
                            {getItemQuantity(item.id)}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => addToCart(item)}
                            className="h-5 w-5 text-white hover:bg-white/20"
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No items found</h3>
            <p className="text-gray-600">Try searching with different keywords</p>
          </div>
        )}
      </div>

      {/* Existing KOT Alert - matching waiter dashboard exactly */}
      {existingKOT && (
        <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-50 to-blue-100 border-t-2 border-blue-300 p-3 sm:p-4 z-30 mobile-safe-content shadow-lg">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-blue-900">
              🍽️ Active Order for Table {tableId}
            </span>
            <Badge variant="secondary" className="bg-blue-200 text-blue-800 text-xs">
              KOT #{existingKOT.kotNumber}
            </Badge>
          </div>
          <div className="text-xs text-blue-700 mb-3 bg-white/50 rounded px-2 py-1">
            📋 {existingKOT.items.length} items ordered • 💰 ₹{existingKOT.totalAmount} total
            <br />
            🕒 Started: {new Date(existingKOT.createdAt).toLocaleString()}
            {existingKOT.versions.length > 1 && (
              <span className="text-blue-600"> • Updated {existingKOT.versions.length - 1} time(s)</span>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => {
                navigate(`/admin/kot/${tableId}`, {
                  state: {
                    existingKOT,
                    viewMode: true,
                    tableId
                  }
                });
              }}
            >
              <FileText className="h-3 w-3 mr-1" />
              View Full Order
            </Button>
            <Button
              variant="default"
              size="sm"
              className="flex-1 text-xs bg-green-600 hover:bg-green-700"
              onClick={() => {
                kotStorage.completeKOT(existingKOT.kotNumber);
                setExistingKOT(null);
                toast({
                  title: "Order Completed!",
                  description: `Table ${tableId} order has been completed successfully`,
                });
              }}
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Complete Order
            </Button>
          </div>
        </div>
      )}

      {/* Cart Footer - matching waiter dashboard exactly */}
      {cart.length > 0 && (
        <div className={`fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg p-3 sm:p-4 z-30 mobile-button-container ${existingKOT ? 'mb-20' : ''}`}>
          <div className="flex items-center justify-between mb-2 sm:mb-3">
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="font-semibold text-gray-900 text-sm sm:text-base">
                {getTotalItems()} items
              </span>
            </div>
            <div className="text-lg sm:text-xl font-bold text-primary">
              ₹{getTotalAmount()}
            </div>
          </div>

          {existingKOT ? (
            // Show options when there's an existing KOT
            <div className="space-y-2">
              <Button
                variant="delivery"
                size="lg"
                className="w-full h-10 sm:h-12 text-sm sm:text-base"
                onClick={() => navigate(`/admin/kot/${tableId}`, {
                  state: {
                    cart,
                    tableId,
                    existingKOT,
                    addToExisting: true
                  }
                })}
              >
                Add to Existing KOT #{existingKOT.kotNumber}
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="w-full h-8 sm:h-10 text-xs sm:text-sm"
                onClick={() => navigate(`/admin/kot/${tableId}`, {
                  state: {
                    cart,
                    tableId,
                    createNew: true
                  }
                })}
              >
                Create New KOT
              </Button>
            </div>
          ) : (
            // Show normal KOT generation when no existing KOT
            <Button
              variant="delivery"
              size="lg"
              className="w-full h-10 sm:h-12 text-sm sm:text-base"
              onClick={() => navigate(`/admin/kot/${tableId}`, { state: { cart, tableId } })}
            >
              Generate KOT and Send to Admin
            </Button>
          )}
        </div>
      )}

      {/* Filter Modal - matching waiter dashboard exactly */}
      <Dialog open={isFilterOpen} onOpenChange={setIsFilterOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              Filter Categories
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-red-600 hover:text-red-700"
              >
                Clear All
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* All Items */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allItems"
                  checked={selectedFilters.allItems}
                  onCheckedChange={(checked) => handleFilterChange('allItems', checked as boolean)}
                />
                <Label htmlFor="allItems" className="font-medium">All Items</Label>
              </div>
            </div>

            {/* Special Categories */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">Special Categories</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="popular"
                    checked={selectedFilters.popular}
                    onCheckedChange={(checked) => handleFilterChange('popular', checked as boolean)}
                  />
                  <Label htmlFor="popular">Popular</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="bestseller"
                    checked={selectedFilters.bestseller}
                    onCheckedChange={(checked) => handleFilterChange('bestseller', checked as boolean)}
                  />
                  <Label htmlFor="bestseller">Best Selling</Label>
                </div>
              </div>
            </div>
            {/* Dietary Categories */}
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <Leaf className="h-3 w-3 text-white" />
                </div>
                Dietary Preferences
              </h3>
              <div className="space-y-2 pl-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="veg"
                    checked={selectedFilters.veg}
                    onCheckedChange={(checked) => handleFilterChange('veg', checked as boolean)}
                  />
                  <Label htmlFor="veg">Vegetarian</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="nonVeg"
                    checked={selectedFilters.nonVeg}
                    onCheckedChange={(checked) => handleFilterChange('nonVeg', checked as boolean)}
                  />
                  <Label htmlFor="nonVeg">Non-Vegetarian</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="egg"
                    checked={selectedFilters.egg}
                    onCheckedChange={(checked) => handleFilterChange('egg', checked as boolean)}
                  />
                  <Label htmlFor="egg">Egg</Label>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminOrderTaking;
