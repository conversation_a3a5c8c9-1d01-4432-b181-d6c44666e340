// Restaurant Order Management System
// Handles orders from Restaurant's own app

export type OrderPlatform = 'own-app';

export type OrderStatus = 
  | 'new' 
  | 'accepted' 
  | 'preparing' 
  | 'ready' 
  | 'picked-up' 
  | 'delivered' 
  | 'cancelled' 
  | 'rejected';

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
  customizations?: string[];
  specialInstructions?: string;
}

export interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
  address?: {
    street: string;
    area: string;
    city: string;
    pincode: string;
    landmark?: string;
  };
}

export interface DeliveryInfo {
  type: 'delivery' | 'pickup' | 'dine-in';
  estimatedTime?: number; // in minutes
  deliveryFee?: number;
  address?: CustomerInfo['address'];
  instructions?: string;
}

// Base order interface
export interface BaseOrder {
  id: string;
  platform: OrderPlatform;
  platformOrderId: string; // Original order ID from the platform
  status: OrderStatus;
  customer: CustomerInfo;
  items: OrderItem[];
  delivery: DeliveryInfo;
  pricing: {
    subtotal: number;
    taxes: number;
    deliveryFee: number;
    platformFee?: number;
    discount?: number;
    total: number;
  };
  timestamps: {
    orderPlaced: string;
    accepted?: string;
    preparing?: string;
    ready?: string;
    pickedUp?: string;
    delivered?: string;
    cancelled?: string;
  };
  specialInstructions?: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
}

// Platform-specific extensions

export interface OwnAppOrder extends BaseOrder {
  platform: 'own-app';
  ownAppData: {
    tableNumber?: string;
    waiterAssigned?: string;
    kotNumber?: string;
    loyaltyPoints?: number;
    isRegularCustomer?: boolean;
  };
}

export type MultiPlatformOrder = OwnAppOrder;

// Platform configuration
export interface PlatformConfig {
  platform: OrderPlatform;
  isEnabled: boolean;
  credentials: {
    apiKey?: string;
    secretKey?: string;
    restaurantId?: string;
    webhookUrl?: string;
  };
  notifications: {
    newOrder: boolean;
    statusUpdate: boolean;
    soundAlert: boolean;
  };
  autoAccept: boolean;
  preparationTime: number; // default preparation time in minutes
}

// Notification types
export interface OrderNotification {
  id: string;
  orderId: string;
  platform: OrderPlatform;
  type: 'new-order' | 'status-update' | 'cancellation' | 'payment-update';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
}

// Statistics interface
export interface PlatformStats {
  platform: OrderPlatform;
  today: {
    orders: number;
    revenue: number;
    avgOrderValue: number;
    completionRate: number;
  };
  thisWeek: {
    orders: number;
    revenue: number;
    avgOrderValue: number;
    completionRate: number;
  };
  thisMonth: {
    orders: number;
    revenue: number;
    avgOrderValue: number;
    completionRate: number;
  };
}

// Multi-Platform Order Manager Class
export class MultiPlatformOrderManager {
  private static instance: MultiPlatformOrderManager;
  private orders: MultiPlatformOrder[] = [];
  private notifications: OrderNotification[] = [];
  private configs: PlatformConfig[] = [];

  private constructor() {
    this.loadFromStorage();
  }

  static getInstance(): MultiPlatformOrderManager {
    if (!MultiPlatformOrderManager.instance) {
      MultiPlatformOrderManager.instance = new MultiPlatformOrderManager();
    }
    return MultiPlatformOrderManager.instance;
  }

  // Load data from localStorage
  private loadFromStorage(): void {
    try {
      const ordersData = localStorage.getItem('multiPlatformOrders');
      const notificationsData = localStorage.getItem('orderNotifications');
      const configsData = localStorage.getItem('platformConfigs');

      if (ordersData) {
        this.orders = JSON.parse(ordersData);
      }
      if (notificationsData) {
        this.notifications = JSON.parse(notificationsData);
        // Clean up any duplicate notification IDs
        this.cleanupDuplicateNotifications();
      }
      if (configsData) {
        this.configs = JSON.parse(configsData);
      } else {
        // Initialize default configs
        this.initializeDefaultConfigs();
      }
    } catch (error) {
      console.error('Error loading multi-platform order data:', error);
      this.initializeDefaultConfigs();
    }
  }

  // Clean up duplicate notification IDs
  private cleanupDuplicateNotifications(): void {
    const seenIds = new Set<string>();
    const uniqueNotifications: OrderNotification[] = [];

    for (const notification of this.notifications) {
      if (!seenIds.has(notification.id)) {
        seenIds.add(notification.id);
        uniqueNotifications.push(notification);
      } else {
        // Regenerate ID for duplicate
        const newNotification = {
          ...notification,
          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        uniqueNotifications.push(newNotification);
      }
    }

    this.notifications = uniqueNotifications;
  }

  // Save data to localStorage
  private saveToStorage(): void {
    try {
      localStorage.setItem('multiPlatformOrders', JSON.stringify(this.orders));
      localStorage.setItem('orderNotifications', JSON.stringify(this.notifications));
      localStorage.setItem('platformConfigs', JSON.stringify(this.configs));
    } catch (error) {
      console.error('Error saving multi-platform order data:', error);
    }
  }

  // Initialize default platform configurations
  private initializeDefaultConfigs(): void {
    this.configs = [
      {
        platform: 'own-app',
        isEnabled: true,
        credentials: {},
        notifications: {
          newOrder: true,
          statusUpdate: true,
          soundAlert: true
        },
        autoAccept: true,
        preparationTime: 15
      }
    ];
    this.saveToStorage();
  }

  // Get all orders
  getAllOrders(): MultiPlatformOrder[] {
    return [...this.orders];
  }

  // Get orders by platform
  getOrdersByPlatform(platform: OrderPlatform): MultiPlatformOrder[] {
    return this.orders.filter(order => order.platform === platform);
  }

  // Get orders by status
  getOrdersByStatus(status: OrderStatus): MultiPlatformOrder[] {
    return this.orders.filter(order => order.status === status);
  }

  // Get active orders (not completed or cancelled)
  getActiveOrders(): MultiPlatformOrder[] {
    return this.orders.filter(order => 
      !['delivered', 'cancelled', 'rejected'].includes(order.status)
    );
  }

  // Add new order
  addOrder(order: MultiPlatformOrder): void {
    this.orders.unshift(order); // Add to beginning for latest first
    this.saveToStorage();
    
    // Create notification for new order
    this.createNotification({
      orderId: order.id,
      platform: order.platform,
      type: 'new-order',
      title: `New ${order.platform.toUpperCase()} Order`,
      message: `Order #${order.platformOrderId} - ₹${order.pricing.total}`,
      priority: 'high'
    });
  }

  // Update order status
  updateOrderStatus(orderId: string, status: OrderStatus): boolean {
    const orderIndex = this.orders.findIndex(order => order.id === orderId);
    if (orderIndex === -1) return false;

    const order = this.orders[orderIndex];
    const oldStatus = order.status;
    order.status = status;
    order.timestamps[status] = new Date().toISOString();

    this.orders[orderIndex] = order;
    this.saveToStorage();

    // Create notification for status update
    if (oldStatus !== status) {
      this.createNotification({
        orderId: order.id,
        platform: order.platform,
        type: 'status-update',
        title: `Order Status Updated`,
        message: `Order #${order.platformOrderId} is now ${status}`,
        priority: 'medium'
      });
    }

    return true;
  }

  // Create notification
  private createNotification(notificationData: Omit<OrderNotification, 'id' | 'timestamp' | 'isRead'>): void {
    const notification: OrderNotification = {
      ...notificationData,
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      isRead: false
    };

    this.notifications.unshift(notification);
    
    // Keep only last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }
    
    this.saveToStorage();
  }

  // Get notifications
  getNotifications(): OrderNotification[] {
    return [...this.notifications];
  }

  // Get unread notifications count
  getUnreadNotificationsCount(): number {
    return this.notifications.filter(n => !n.isRead).length;
  }

  // Mark notification as read
  markNotificationAsRead(notificationId: string): boolean {
    const notificationIndex = this.notifications.findIndex(n => n.id === notificationId);
    if (notificationIndex === -1) return false;

    this.notifications[notificationIndex].isRead = true;
    this.saveToStorage();
    return true;
  }

  // Mark all notifications as read
  markAllNotificationsAsRead(): void {
    this.notifications.forEach(notification => {
      notification.isRead = true;
    });
    this.saveToStorage();
  }

  // Get platform configurations
  getPlatformConfigs(): PlatformConfig[] {
    return [...this.configs];
  }

  // Update platform configuration
  updatePlatformConfig(platform: OrderPlatform, config: Partial<PlatformConfig>): boolean {
    const configIndex = this.configs.findIndex(c => c.platform === platform);
    if (configIndex === -1) return false;

    this.configs[configIndex] = { ...this.configs[configIndex], ...config };
    this.saveToStorage();
    return true;
  }

  // Get platform statistics
  getPlatformStats(): PlatformStats[] {
    const stats: PlatformStats[] = [];
    const platforms: OrderPlatform[] = ['own-app'];

    platforms.forEach(platform => {
      const platformOrders = this.getOrdersByPlatform(platform);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      const todayOrders = platformOrders.filter(order => 
        new Date(order.timestamps.orderPlaced) >= today
      );
      const weekOrders = platformOrders.filter(order => 
        new Date(order.timestamps.orderPlaced) >= weekStart
      );
      const monthOrders = platformOrders.filter(order => 
        new Date(order.timestamps.orderPlaced) >= monthStart
      );

      const calculateStats = (orders: MultiPlatformOrder[]) => {
        const completedOrders = orders.filter(order => order.status === 'delivered');
        const totalRevenue = completedOrders.reduce((sum, order) => sum + order.pricing.total, 0);
        const avgOrderValue = completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0;
        const completionRate = orders.length > 0 ? (completedOrders.length / orders.length) * 100 : 0;

        return {
          orders: orders.length,
          revenue: totalRevenue,
          avgOrderValue,
          completionRate
        };
      };

      stats.push({
        platform,
        today: calculateStats(todayOrders),
        thisWeek: calculateStats(weekOrders),
        thisMonth: calculateStats(monthOrders)
      });
    });

    return stats;
  }

  // Clear all data (for debugging)
  clearAllData(): void {
    this.orders = [];
    this.notifications = [];
    this.saveToStorage();
  }
}

// Export singleton instance
export const multiPlatformOrderManager = MultiPlatformOrderManager.getInstance();
