// GST Settings Management Utility

export interface GSTSettings {
  cgstRate: number; // CGST percentage
  sgstRate: number; // SGST percentage
  totalGSTRate: number; // Total GST percentage (CGST + SGST)
  enabled: boolean; // Whether GST is enabled by default
  lastUpdated: string;
}

class GSTSettingsManager {
  private readonly STORAGE_KEY = 'restaurant_gst_settings';
  private readonly DEFAULT_SETTINGS: GSTSettings = {
    cgstRate: 2.5,
    sgstRate: 2.5,
    totalGSTRate: 5.0,
    enabled: true,
    lastUpdated: new Date().toISOString()
  };

  // Get GST settings from localStorage
  getGSTSettings(): GSTSettings {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const settings = JSON.parse(stored);
        // Ensure totalGSTRate is calculated correctly
        settings.totalGSTRate = settings.cgstRate + settings.sgstRate;
        return settings;
      }
    } catch (error) {
      console.error('Error reading GST settings from storage:', error);
    }
    return { ...this.DEFAULT_SETTINGS };
  }

  // Save GST settings to localStorage
  saveGSTSettings(settings: Partial<GSTSettings>): boolean {
    try {
      const currentSettings = this.getGSTSettings();
      const updatedSettings: GSTSettings = {
        ...currentSettings,
        ...settings,
        totalGSTRate: (settings.cgstRate || currentSettings.cgstRate) + (settings.sgstRate || currentSettings.sgstRate),
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedSettings));
      
      // Trigger a custom event to notify all components about GST settings change
      window.dispatchEvent(new CustomEvent('gstSettingsChanged', { 
        detail: updatedSettings 
      }));
      
      return true;
    } catch (error) {
      console.error('Error saving GST settings to storage:', error);
      return false;
    }
  }

  // Update GST rates
  updateGSTRates(cgstRate: number, sgstRate: number): boolean {
    if (cgstRate < 0 || cgstRate > 50 || sgstRate < 0 || sgstRate > 50) {
      throw new Error('GST rates must be between 0% and 50%');
    }

    return this.saveGSTSettings({
      cgstRate,
      sgstRate
    });
  }

  // Set total GST rate (automatically splits into CGST and SGST equally)
  setTotalGSTRate(totalRate: number): boolean {
    if (totalRate < 0 || totalRate > 100) {
      throw new Error('Total GST rate must be between 0% and 100%');
    }

    const halfRate = totalRate / 2;
    return this.updateGSTRates(halfRate, halfRate);
  }

  // Toggle GST enabled/disabled
  toggleGSTEnabled(enabled: boolean): boolean {
    return this.saveGSTSettings({ enabled });
  }

  // Reset to default settings
  resetToDefaults(): boolean {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      window.dispatchEvent(new CustomEvent('gstSettingsChanged', { 
        detail: this.DEFAULT_SETTINGS 
      }));
      return true;
    } catch (error) {
      console.error('Error resetting GST settings:', error);
      return false;
    }
  }

  // Calculate GST amounts
  calculateGST(amount: number, includeGST: boolean = true): {
    subtotal: number;
    cgstAmount: number;
    sgstAmount: number;
    totalGSTAmount: number;
    grandTotal: number;
  } {
    const settings = this.getGSTSettings();
    
    if (!includeGST) {
      return {
        subtotal: amount,
        cgstAmount: 0,
        sgstAmount: 0,
        totalGSTAmount: 0,
        grandTotal: amount
      };
    }

    const cgstAmount = (amount * settings.cgstRate) / 100;
    const sgstAmount = (amount * settings.sgstRate) / 100;
    const totalGSTAmount = cgstAmount + sgstAmount;
    const grandTotal = amount + totalGSTAmount;

    return {
      subtotal: amount,
      cgstAmount,
      sgstAmount,
      totalGSTAmount,
      grandTotal
    };
  }

  // Get formatted GST display text
  getGSTDisplayText(): string {
    const settings = this.getGSTSettings();
    return `GST (CGST ${settings.cgstRate}% + SGST ${settings.sgstRate}%)`;
  }

  // Get total GST percentage as string
  getTotalGSTPercentage(): string {
    const settings = this.getGSTSettings();
    return `${settings.totalGSTRate}%`;
  }
}

// Export singleton instance
export const gstSettingsManager = new GSTSettingsManager();

// Hook for React components to use GST settings
export const useGSTSettings = () => {
  const [gstSettings, setGSTSettings] = React.useState<GSTSettings>(
    gstSettingsManager.getGSTSettings()
  );

  React.useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent<GSTSettings>) => {
      setGSTSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    
    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    };
  }, []);

  return {
    gstSettings,
    updateGSTRates: gstSettingsManager.updateGSTRates.bind(gstSettingsManager),
    setTotalGSTRate: gstSettingsManager.setTotalGSTRate.bind(gstSettingsManager),
    toggleGSTEnabled: gstSettingsManager.toggleGSTEnabled.bind(gstSettingsManager),
    calculateGST: gstSettingsManager.calculateGST.bind(gstSettingsManager),
    getGSTDisplayText: gstSettingsManager.getGSTDisplayText.bind(gstSettingsManager),
    getTotalGSTPercentage: gstSettingsManager.getTotalGSTPercentage.bind(gstSettingsManager)
  };
};

// Import React for the hook
import React from 'react';
