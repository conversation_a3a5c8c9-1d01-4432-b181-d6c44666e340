import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { ThermalPrintFormatter } from '../utils/thermalPrintFormatter';
import { KOT } from '../utils/kotStorage';
import { Customer } from '../utils/customerStorage';

interface ThermalPrintPreviewProps {
  kot: KOT;
  customer: Customer | null;
  includeGST?: boolean;
  type: 'bill' | 'kot';
}

const ThermalPrintPreview: React.FC<ThermalPrintPreviewProps> = ({
  kot,
  customer,
  includeGST = true,
  type
}) => {
  const formatter = new ThermalPrintFormatter();
  
  const getFormattedContent = () => {
    if (type === 'bill') {
      return formatter.formatBill(kot, customer, includeGST);
    } else {
      return formatter.formatKOT(kot);
    }
  };

  const content = getFormattedContent();
  const lines = content.split('\n');

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">
          Thermal Print Preview ({type.toUpperCase()})
        </CardTitle>
        <p className="text-sm text-gray-600 text-center">
          24 characters wide (58mm thermal paper)
        </p>
      </CardHeader>
      <CardContent>
        <div className="bg-white border-2 border-gray-300 p-4 font-mono text-xs leading-tight">
          <div className="bg-gray-100 p-2 mb-2 text-center text-xs">
            ← 24 characters →
          </div>
          <pre className="whitespace-pre-wrap">
            {lines.map((line, index) => (
              <div key={index} className={`${line.length > 24 ? 'bg-red-100' : ''}`}>
                {line}
                {line.length > 24 && (
                  <span className="text-red-500 text-xs ml-1">
                    ({line.length} chars - TOO LONG!)
                  </span>
                )}
              </div>
            ))}
          </pre>
        </div>
        
        <div className="mt-4 text-sm">
          <p><strong>Total lines:</strong> {lines.length}</p>
          <p><strong>Max line length:</strong> {Math.max(...lines.map(l => l.length))} characters</p>
          <p><strong>Lines over 24 chars:</strong> {lines.filter(l => l.length > 24).length}</p>
        </div>

        <Button 
          className="w-full mt-4" 
          onClick={() => {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `thermal-${type}-preview.txt`;
            a.click();
            URL.revokeObjectURL(url);
          }}
        >
          Download Preview
        </Button>
      </CardContent>
    </Card>
  );
};

export default ThermalPrintPreview;
