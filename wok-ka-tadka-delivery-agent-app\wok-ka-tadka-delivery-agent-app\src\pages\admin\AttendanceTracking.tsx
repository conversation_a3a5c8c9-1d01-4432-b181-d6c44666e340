import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  ArrowLeft, 
  Calendar,
  Clock,
  Users,
  CheckCircle,
  XCircle,
  Timer,
  Search,
  Filter,
  Download,
  Eye
} from "lucide-react";
import {
  getAllAttendanceRecords,
  getStaffAttendanceStats,
  formatTime,
  formatDate,
  type AttendanceRecord
} from "@/utils/attendanceStorage";
import { getAllStaffPins } from "@/utils/staffPinStorage";

const AttendanceTracking = () => {
  const navigate = useNavigate();
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<AttendanceRecord[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedStaff, setSelectedStaff] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [staffList, setStaffList] = useState<any[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterRecords();
  }, [attendanceRecords, searchTerm, selectedDate, selectedStaff, selectedStatus]);

  const loadData = () => {
    // Load attendance records
    const records = getAllAttendanceRecords();
    setAttendanceRecords(records);

    // Load staff list
    const staff = getAllStaffPins();
    setStaffList(staff);
  };

  const filterRecords = () => {
    let filtered = attendanceRecords;

    // Filter by date
    if (selectedDate) {
      filtered = filtered.filter(record => record.date === selectedDate);
    }

    // Filter by staff
    if (selectedStaff !== "all") {
      filtered = filtered.filter(record => record.staffId === selectedStaff);
    }

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(record => record.status === selectedStatus);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(record => 
        record.staffName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.staffPhone.includes(searchTerm)
      );
    }

    // Sort by date and check-in time
    filtered.sort((a, b) => {
      const dateCompare = new Date(b.date).getTime() - new Date(a.date).getTime();
      if (dateCompare !== 0) return dateCompare;
      
      if (a.checkInTime && b.checkInTime) {
        return new Date(b.checkInTime).getTime() - new Date(a.checkInTime).getTime();
      }
      return 0;
    });

    setFilteredRecords(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'checked-in':
        return 'bg-blue-100 text-blue-800';
      case 'checked-out':
        return 'bg-green-100 text-green-800';
      case 'absent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'checked-in':
        return <Clock className="h-4 w-4" />;
      case 'checked-out':
        return <CheckCircle className="h-4 w-4" />;
      case 'absent':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Timer className="h-4 w-4" />;
    }
  };

  const getTodayStats = () => {
    const today = new Date().toISOString().split('T')[0];
    const todayRecords = attendanceRecords.filter(record => record.date === today);
    
    return {
      total: staffList.filter(s => s.isActive).length,
      present: todayRecords.filter(r => r.status !== 'absent').length,
      checkedIn: todayRecords.filter(r => r.status === 'checked-in').length,
      checkedOut: todayRecords.filter(r => r.status === 'checked-out').length,
      absent: staffList.filter(s => s.isActive).length - todayRecords.length
    };
  };

  const handleViewStaffDetails = (staffId: string) => {
    // Navigate to staff attendance details
    navigate(`/admin/staff-attendance/${staffId}`);
  };

  const stats = getTodayStats();

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary shadow-lg border-b apk-header-fixed">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-white truncate">Attendance Tracking</h1>
              <p className="text-white/80 text-xs sm:text-sm truncate">Monitor staff attendance</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20 shrink-0 px-2 sm:px-3"
          >
            <Download className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Export</span>
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-6 apk-content-with-header">
        {/* Today's Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-blue-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                <p className="text-sm text-gray-500">Total Staff</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.present}</p>
                <p className="text-sm text-gray-500">Present</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-amber-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Clock className="h-6 w-6 text-amber-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.checkedIn}</p>
                <p className="text-sm text-gray-500">Checked In</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-purple-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Timer className="h-6 w-6 text-purple-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.checkedOut}</p>
                <p className="text-sm text-gray-500">Checked Out</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-red-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.absent}</p>
                <p className="text-sm text-gray-500">Absent</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="shadow-sm border-0 bg-white">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search staff..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full"
              />
              
              <select
                value={selectedStaff}
                onChange={(e) => setSelectedStaff(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Staff</option>
                {staffList.map(staff => (
                  <option key={staff.id} value={staff.id}>
                    {staff.name} ({staff.role})
                  </option>
                ))}
              </select>
              
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="checked-in">Checked In</option>
                <option value="checked-out">Checked Out</option>
                <option value="absent">Absent</option>
              </select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedDate(new Date().toISOString().split('T')[0]);
                  setSelectedStaff("all");
                  setSelectedStatus("all");
                }}
                className="w-full"
              >
                <Filter className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Records */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Attendance Records ({filteredRecords.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {filteredRecords.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No attendance records found</p>
                <p className="text-sm text-gray-400">Try adjusting your filters</p>
              </div>
            ) : (
              filteredRecords.map((record) => (
                <div
                  key={record.id}
                  className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2 flex-wrap">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(record.status)}
                          <span className="font-semibold text-gray-900">
                            {record.staffName}
                          </span>
                        </div>
                        <Badge className={`${getStatusColor(record.status)} text-xs px-2 py-1`}>
                          {record.status.replace('-', ' ').toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {formatDate(record.date)}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Check In:</span>
                          <span className="font-medium ml-2">
                            {record.checkInTime ? formatTime(record.checkInTime) : 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Check Out:</span>
                          <span className="font-medium ml-2">
                            {record.checkOutTime ? formatTime(record.checkOutTime) : 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Hours:</span>
                          <span className="font-medium ml-2">
                            {record.totalHours ? `${record.totalHours}h` : 'N/A'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Role:</span>
                          <span className="font-medium ml-2 capitalize">
                            {staffList.find(s => s.id === record.staffId)?.role || 'N/A'}
                          </span>
                        </div>
                      </div>

                      {record.notes && (
                        <div className="mt-2 text-sm">
                          <span className="text-gray-500">Notes:</span>
                          <span className="ml-2 text-gray-700">{record.notes}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/staff-attendance/${record.staffId}`)}
                        className="w-full lg:w-auto"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AttendanceTracking;
