
import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Package,
  MapPin,
  Clock,
  Truck,
  Bell,
  RefreshCw,
  TrendingUp,
  DollarSign,
  CheckCircle,
  XCircle,
  User,
  Menu,
  Utensils,
  Users,
  Coffee,
  ClipboardList,
  Calendar,
  LogIn,
  LogOut,
  Timer
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { useToast } from "@/hooks/use-toast";
import {
  getTodayAttendance,
  markCheckIn,
  markCheckOut,
  canCheckIn,
  canCheckOut,
  formatTime,
  type AttendanceRecord
} from "@/utils/attendanceStorage";
import {
  getCurrentWorkingHours,
  getWorkingHoursDisplay,
  isEarlyCheckout,
  isWorkingDay,
  getNextWorkingDay,
  type WorkingHours
} from "@/utils/workingHoursStorage";
import {
  getStaffNotifications,
  formatNotificationTime,
  getTimeAgo,
  createEarlyCheckoutNotification,
  type EarlyCheckoutNotification,
  createCheckInNotification
} from "@/utils/notificationStorage";

const Dashboard = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [mode, setMode] = useState<"delivery" | "waiter">("delivery");
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);
  const [isProcessingAttendance, setIsProcessingAttendance] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [workingHours, setWorkingHours] = useState<WorkingHours>(getCurrentWorkingHours());
  const [staffNotifications, setStaffNotifications] = useState<EarlyCheckoutNotification[]>([]);
  const [showEarlyCheckoutDialog, setShowEarlyCheckoutDialog] = useState(false);
  const [earlyCheckoutReason, setEarlyCheckoutReason] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  // Safe navigation helper
  const safeNavigate = (path: string, options?: { replace?: boolean }) => {
    try {
      navigate(path, options);
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to window.location
      if (options?.replace) {
        window.location.replace(path);
      } else {
        window.location.href = path;
      }
    }
  };

  // Get current staff info from localStorage
  const currentStaff = JSON.parse(localStorage.getItem('currentStaff') || '{}');
  const staffName = currentStaff.name || 'Staff Member';
  const staffRole = currentStaff.role || 'waiter';
  const staffId = currentStaff.id || 'default-staff';
  const staffPhone = currentStaff.phone || '1234567890';

  // Load today's attendance and notifications on component mount
  useEffect(() => {
    loadTodayAttendance();
    loadStaffNotifications();
  }, [staffId]);

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Listen for working hours changes
  useEffect(() => {
    const handleWorkingHoursChange = (event: CustomEvent) => {
      setWorkingHours(event.detail.workingHours);
    };

    window.addEventListener('workingHoursChanged', handleWorkingHoursChange as EventListener);

    return () => {
      window.removeEventListener('workingHoursChanged', handleWorkingHoursChange as EventListener);
    };
  }, []);

  // Listen for notification changes
  useEffect(() => {
    const handleNotificationsChange = () => {
      loadStaffNotifications();
    };

    window.addEventListener('notificationsChanged', handleNotificationsChange);

    return () => {
      window.removeEventListener('notificationsChanged', handleNotificationsChange);
    };
  }, [staffId]);

  const loadTodayAttendance = () => {
    const attendance = getTodayAttendance(staffId);
    setTodayAttendance(attendance);
  };

  const loadStaffNotifications = () => {
    const notifications = getStaffNotifications(staffId, 5); // Get latest 5 notifications
    setStaffNotifications(notifications);
  };

  const handleEarlyCheckoutRequest = async () => {
    setIsProcessingAttendance(true);
    try {
      const now = new Date();
      const workingHours = getCurrentWorkingHours();

      // Calculate scheduled end time for today
      const [endHour, endMinute] = workingHours.endTime.split(':').map(Number);
      const scheduledEndTime = new Date();
      scheduledEndTime.setHours(endHour, endMinute, 0, 0);

      // Create early checkout notification
      createEarlyCheckoutNotification(
        staffId,
        staffName,
        staffPhone,
        scheduledEndTime,
        now,
        earlyCheckoutReason.trim() || undefined
      );

      toast({
        title: "Early Checkout Request Sent! 📤",
        description: `Your request has been sent to admin for approval. You'll be notified once it's reviewed.`,
      });

      // Reset dialog state
      setShowEarlyCheckoutDialog(false);
      setEarlyCheckoutReason('');

      // Refresh notifications to show the new request
      loadStaffNotifications();

    } catch (error) {
      toast({
        title: "Request Failed",
        description: error instanceof Error ? error.message : 'Failed to send early checkout request',
        variant: "destructive",
      });
    } finally {
      setIsProcessingAttendance(false);
    }
  };

  const handleCheckIn = async () => {
    if (!canCheckIn(staffId)) {
      toast({
        title: "Already Checked In",
        description: "You have already checked in today!",
        variant: "destructive",
      });
      return;
    }

    // Check if today is a working day
    const today = new Date();
    if (!isWorkingDay(today)) {
      const nextWorkingDay = getNextWorkingDay(today);
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const nextWorkingDayName = dayNames[nextWorkingDay.getDay()];

      toast({
        title: "Non-Working Day 📅",
        description: `Today is not a scheduled working day. Next working day is ${nextWorkingDayName}.`,
        variant: "destructive",
      });
      return;
    }

    setIsProcessingAttendance(true);
    try {
      const record = markCheckIn(staffId, staffName, staffPhone);
      setTodayAttendance(record);

      const checkInTime = new Date(record.checkInTime!).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

      toast({
        title: "Check-in Successful! ✅",
        description: `Welcome ${staffName}! Checked in at ${checkInTime}`,
      });

      createCheckInNotification(staffId, staffName, staffPhone, record.checkInTime!);

    } catch (error) {
      toast({
        title: "Check-in Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsProcessingAttendance(false);
    }
  };

  const handleCheckOut = async () => {
    if (!canCheckOut(staffId)) {
      toast({
        title: "Cannot Check Out",
        description: "You need to check in first or have already checked out today!",
        variant: "destructive",
      });
      return;
    }

    // Check if today is a working day (though they might have checked in on a working day)
    const today = new Date();
    if (!isWorkingDay(today)) {
      toast({
        title: "Non-Working Day Checkout ⚠️",
        description: "You're checking out on a non-working day. This will be noted in your attendance record.",
      });
    }

    // Check if this is an early checkout
    const now = new Date();
    const isEarly = isEarlyCheckout(now, staffId);

    if (isEarly) {
      // Show early checkout dialog for approval request
      setShowEarlyCheckoutDialog(true);
      return;
    }

    setIsProcessingAttendance(true);
    try {
      const record = markCheckOut(staffId);
      setTodayAttendance(record);

      const checkOutTime = new Date(record.checkOutTime!).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

      toast({
        title: "Check-out Successful! 👋",
        description: `Goodbye ${staffName}! Checked out at ${checkOutTime}. Total hours: ${record.totalHours}h`,
      });

    } catch (error) {
      toast({
        title: "Check-out Failed",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setIsProcessingAttendance(false);
    }
  };

  const handleLogout = () => {
    // Clear current staff data
    localStorage.removeItem('currentStaff');
    // Navigate to login page
    safeNavigate('/delivery/login', { replace: true });
  };

  const stats = {
    totalDeliveries: 12,
    earnings: 850,
    pendingDeliveries: 3,
    completedToday: 9
  };

  const recentDeliveries = [
    {
      id: "#56970",
      customerName: "Priya Sharma",
      address: "Sector 18, Noida",
      amount: 345,
      status: "ready",
      time: "2 mins ago"
    },
    {
      id: "#56969",
      customerName: "Rahul Gupta",
      address: "Connaught Place, Delhi",
      amount: 520,
      status: "picked",
      time: "15 mins ago"
    },
    {
      id: "#56968",
      customerName: "Anjali Singh",
      address: "Gurgaon Sector 47",
      amount: 280,
      status: "delivered",
      time: "1 hour ago"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ready": return "bg-warning text-warning-foreground";
      case "picked": return "bg-primary text-primary-foreground";
      case "delivered": return "bg-success text-success-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ready": return "Ready for Pickup";
      case "picked": return "Picked Up";
      case "delivered": return "Delivered";
      default: return status;
    }
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b apk-header-fixed z-0">
        <div className="flex items-center justify-between p-4">
          <Logo size="sm" variant="white" showText={true} />
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600"
            >
              <RefreshCw className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => safeNavigate("/delivery/profile")}
              className="text-gray-600"
            >
              <User className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleLogout}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LogOut className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Online Status */}
        <div className="px-4 pb-4">
          <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="font-medium text-gray-700">
                {isOnline ? "Online & Available" : "Offline"}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOnline(!isOnline)}
              className="text-primary hover:bg-primary hover:text-white"
            >
              {isOnline ? "Go Offline" : "Go Online"}
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6" style={{ paddingTop: 'calc(env(safe-area-inset-top) + 140px)' }}>
        {/* Welcome Message */}
        <div className="text-center py-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Good Morning, {staffName}!</h2>
          <p className="text-gray-600">Ready for {mode === "delivery" ? "deliveries" : "table service"} today?</p>
          <p className="text-sm text-gray-500 capitalize">Role: {staffRole}</p>
        </div>

        {/* Enhanced Attendance Section */}
        <Card className="shadow-sm border-0 bg-white">
          <CardContent className="p-4">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className={`p-2 rounded-full ${
                    todayAttendance?.status === 'checked-in' ? 'bg-green-100' :
                    todayAttendance?.status === 'checked-out' ? 'bg-blue-100' :
                    'bg-gray-100'
                  }`}>
                    <Calendar className={`h-5 w-5 ${
                      todayAttendance?.status === 'checked-in' ? 'text-green-600' :
                      todayAttendance?.status === 'checked-out' ? 'text-blue-600' :
                      'text-gray-600'
                    }`} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-gray-900">Today's Attendance</h3>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => safeNavigate("/delivery/attendance")}
                  className="text-primary hover:bg-primary hover:text-white flex-shrink-0 ml-2"
                >
                  <Timer className="h-4 w-4 mr-1" />
                  View History
                </Button>
              </div>
              <div className="flex items-center gap-2 ml-14">
                <p className="text-sm text-gray-600">
                  {todayAttendance
                    ? `Status: ${todayAttendance.status.replace('-', ' ').toUpperCase()}`
                    : 'Not marked yet'
                  }
                </p>
                {todayAttendance?.status === 'checked-in' && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                    Active
                  </Badge>
                )}
                {todayAttendance?.status === 'checked-out' && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">
                    Completed
                  </Badge>
                )}
              </div>
            </div>

            {/* Current Date and Time Display */}
            <div className="bg-blue-50 rounded-lg p-3 mb-4 border border-blue-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    {currentTime.toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                <span className="text-lg font-bold text-blue-900">
                  {currentTime.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  })}
                </span>
              </div>
            </div>

            {/* Working Hours Display */}
            <div className="bg-green-50 rounded-lg p-3 mb-4 border border-green-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-900">Working Hours</span>
                </div>
                <span className="text-sm font-bold text-green-900">
                  {getWorkingHoursDisplay()}
                </span>
              </div>
              <div className="mt-2 text-xs text-green-700">
                Total: {workingHours.totalHours}h per day • Break: {workingHours.breakDuration} min
              </div>
            </div>

            {/* Staff Notifications */}
            {staffNotifications.length > 0 && (
              <div className="bg-blue-50 rounded-lg p-3 mb-4 border border-blue-200">
                <div className="flex items-center gap-2 mb-3">
                  <Bell className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">Recent Notifications</span>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {staffNotifications.length}
                  </Badge>
                </div>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {staffNotifications.map((notification) => (
                    <div key={notification.id} className="bg-white rounded p-2 border border-blue-100">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {notification.status === 'pending' && (
                            <Clock className="h-3 w-3 text-yellow-600" />
                          )}
                          {notification.status === 'approved' && (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          )}
                          {notification.status === 'rejected' && (
                            <XCircle className="h-3 w-3 text-red-600" />
                          )}
                          <span className="text-xs font-medium">
                            Early Checkout Request
                          </span>
                        </div>
                        <Badge
                          variant="secondary"
                          className={`text-xs ${
                            notification.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            notification.status === 'approved' ? 'bg-green-100 text-green-800' :
                            'bg-red-100 text-red-800'
                          }`}
                        >
                          {notification.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        {notification.status === 'pending'
                          ? `Requested ${getTimeAgo(notification.requestTime)} • ${notification.minutesEarly} min early`
                          : notification.adminResponse?.comments || 'No comments provided'
                        }
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {todayAttendance && (
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 mb-4 border border-gray-200">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-green-100 p-2 rounded-full">
                      <LogIn className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Check In</p>
                      <p className="font-semibold text-gray-900">
                        {todayAttendance.checkInTime ? formatTime(todayAttendance.checkInTime) : 'Not yet'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <LogOut className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">Check Out</p>
                      <p className="font-semibold text-gray-900">
                        {todayAttendance.checkOutTime ? formatTime(todayAttendance.checkOutTime) : 'Not yet'}
                      </p>
                    </div>
                  </div>
                </div>
                {todayAttendance.totalHours && (
                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Timer className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-gray-600">Total Hours Worked:</span>
                      </div>
                      <span className="font-bold text-lg text-green-600">{todayAttendance.totalHours}h</span>
                    </div>
                  </div>
                )}
                {todayAttendance.status === 'checked-in' && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Currently working...</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-3">
              <Button
                onClick={handleCheckIn}
                disabled={isProcessingAttendance || !canCheckIn(staffId)}
                className={`flex-1 h-12 font-semibold transition-all duration-200 ${
                  canCheckIn(staffId)
                    ? "bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl"
                    : "bg-gray-100 text-gray-400 cursor-not-allowed"
                }`}
                variant={canCheckIn(staffId) ? "default" : "outline"}
              >
                {isProcessingAttendance ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <LogIn className="h-4 w-4 mr-2" />
                    Check In
                  </>
                )}
              </Button>
              <Button
                onClick={handleCheckOut}
                disabled={isProcessingAttendance || !canCheckOut(staffId)}
                className={`flex-1 h-12 font-semibold transition-all duration-200 ${
                  canCheckOut(staffId)
                    ? "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl"
                    : "bg-gray-100 text-gray-400 cursor-not-allowed"
                }`}
                variant={canCheckOut(staffId) ? "default" : "outline"}
              >
                {isProcessingAttendance ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <LogOut className="h-4 w-4 mr-2" />
                    Check Out
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Mode Toggle */}
        <Card className="shadow-card border-0 bg-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h3 className="font-semibold text-gray-900">Work Mode</h3>
                <p className="text-sm text-gray-600">Switch between delivery and waiter duties</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={mode === "delivery" ? "delivery" : "outline"}
                onClick={() => setMode("delivery")}
                className="flex-1 h-10"
              >
                <Truck className="h-4 w-4 mr-2" />
                Delivery
              </Button>
              <Button
                variant={mode === "waiter" ? "delivery" : "outline"}
                onClick={() => setMode("waiter")}
                className="flex-1 h-10"
              >
                <Utensils className="h-4 w-4 mr-2" />
                Waiter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-amber-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <Package className="h-6 w-6 text-amber-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingDeliveries}</p>
                <p className="text-sm text-gray-500">Pending Orders</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-green-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.completedToday}</p>
                <p className="text-sm text-gray-500">Completed</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-primary/10 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-primary" />
                </div>
                <p className="text-2xl font-bold text-gray-900">₹{stats.earnings}</p>
                <p className="text-sm text-gray-500">Today's Earnings</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardContent className="p-4">
              <div className="text-center">
                <div className="bg-blue-100 p-3 rounded-full w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{stats.totalDeliveries}</p>
                <p className="text-sm text-gray-500">Total Deliveries</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="space-y-3">
          {mode === "delivery" ? (
            <>
              <Button
                variant="delivery"
                size="lg"
                className="w-full h-14"
                onClick={() => safeNavigate("/delivery/assignments")}
              >
                <Package className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <div className="font-semibold">View Assigned Deliveries</div>
                  <div className="text-sm opacity-90">{stats.pendingDeliveries} pending orders</div>
                </div>
              </Button>

              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => safeNavigate("/delivery/current")}
                  className="h-12 border-gray-200 hover:border-primary hover:bg-primary hover:text-white"
                >
                  <Truck className="h-4 w-4 mr-2" />
                  Current Orders
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => safeNavigate("/delivery/history")}
                  className="h-12 border-gray-200 hover:border-primary hover:bg-primary hover:text-white"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  History
                </Button>
              </div>
            </>
          ) : (
            <>
              <Button
                variant="delivery"
                size="lg"
                className="w-full h-14"
                onClick={() => safeNavigate("/delivery/tables")}
              >
                <Users className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <div className="font-semibold">Manage Tables</div>
                  <div className="text-sm opacity-90">View table status & take orders</div>
                </div>
              </Button>

              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => safeNavigate("/delivery/active-orders")}
                  className="h-12 border-gray-200 hover:border-primary hover:bg-primary hover:text-white"
                >
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Active Orders
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => safeNavigate("/delivery/kot-history")}
                  className="h-12 border-gray-200 hover:border-primary hover:bg-primary hover:text-white"
                >
                  <Coffee className="h-4 w-4 mr-2" />
                  KOT History
                </Button>
              </div>
            </>
          )}
        </div>

        {/* Recent Deliveries */}
        <Card className="shadow-sm border-0 bg-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-gray-900">Recent Deliveries</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {recentDeliveries.map((delivery) => (
              <div
                key={delivery.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => safeNavigate(`/delivery/details/${delivery.id.replace('#', '')}`)}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-semibold text-gray-900">{delivery.id}</span>
                    <Badge className={`${getStatusColor(delivery.status)} text-xs px-2 py-1`}>
                      {getStatusText(delivery.status)}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 font-medium">{delivery.customerName}</p>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <MapPin className="h-3 w-3" />
                    <span>{delivery.address}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">₹{delivery.amount}</p>
                  <p className="text-xs text-gray-500">{delivery.time}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Early Checkout Request Dialog */}
      {showEarlyCheckoutDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 z-[99999] pointer-events-auto">
          <Card className="w-full max-w-md mt-8 sm:mt-16">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                Early Checkout Request
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 mb-2">
                  You're requesting to checkout <strong>{workingHours.earlyCheckoutThreshold} minutes early</strong>.
                  This requires admin approval.
                </p>
                <p className="text-xs text-gray-500">
                  Your request will be sent to the admin for review. You'll be notified once it's approved or rejected.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Reason for Early Checkout (Optional)
                </label>
                <Textarea
                  value={earlyCheckoutReason}
                  onChange={(e) => setEarlyCheckoutReason(e.target.value)}
                  placeholder="Please provide a reason for your early checkout request..."
                  rows={3}
                  className="resize-none"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleEarlyCheckoutRequest}
                  disabled={isProcessingAttendance}
                  className="flex-1 bg-blue-600 hover:bg-blue-700"
                >
                  {isProcessingAttendance ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Bell className="h-4 w-4 mr-2" />
                      Send Request
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => {
                    setShowEarlyCheckoutDialog(false);
                    setEarlyCheckoutReason('');
                  }}
                  variant="outline"
                  className="flex-1"
                  disabled={isProcessingAttendance}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
