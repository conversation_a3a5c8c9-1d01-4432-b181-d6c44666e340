import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  ArrowLeft,
  Settings,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  Eye,
  EyeOff,
  Clock,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Users,
  Smartphone,
  RotateCcw,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { useToast } from "@/hooks/use-toast";
import { completeMenuItems } from "@/data/completeMenuData";
import { menuStatusManager, MenuItemStatus, CategoryStatus } from "@/utils/menuStatusManager";

// Types are now imported from menuStatusManager

const DISABLE_REASONS = [
  "Chef Absent",
  "Out of Stock", 
  "Equipment Issue",
  "Maintenance",
  "Ingredient Shortage",
  "Quality Issue",
  "Other"
];

const MenuManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all"); // all, disabled, enabled
  const [appFilter, setAppFilter] = useState("all"); // all, customer-app, waiter-app, both
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  
  // Menu status data
  const [menuItemStatuses, setMenuItemStatuses] = useState<MenuItemStatus[]>([]);
  const [categoryStatuses, setCategoryStatuses] = useState<CategoryStatus[]>([]);
  
  // Dialog states
  const [isItemDialogOpen, setIsItemDialogOpen] = useState(false);
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false);
  const [isEditItemDialogOpen, setIsEditItemDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [selectedCategoryName, setSelectedCategoryName] = useState("");

  // Menu item form states
  const [itemForm, setItemForm] = useState({
    name: "",
    price: "",
    category: "",
    veg: true,
    spicy: 0,
    popular: false,
    bestseller: false,
    egg: false,
    preparationTime: "15"
  });
  
  // Form states
  const [disableScope, setDisableScope] = useState<'customer-app' | 'waiter-app' | 'both'>('both');
  const [disableReason, setDisableReason] = useState("");
  const [customReason, setCustomReason] = useState("");
  const [useSchedule, setUseSchedule] = useState(false);
  const [scheduleFromDate, setScheduleFromDate] = useState("");
  const [scheduleFromTime, setScheduleFromTime] = useState("");
  const [scheduleToDate, setScheduleToDate] = useState("");
  const [scheduleToTime, setScheduleToTime] = useState("");

  // Initialize data
  useEffect(() => {
    initializeMenuStatuses();
  }, []);

  const initializeMenuStatuses = () => {
    // Load statuses from the manager
    const statuses = menuStatusManager.getAllStatuses();
    setMenuItemStatuses(statuses);

    // Calculate category statuses
    updateCategoryStatuses();
  };

  const updateCategoryStatuses = () => {
    const categories = Object.keys(completeMenuItems);
    const categoryStats: CategoryStatus[] = categories.map(categoryName => {
      const categoryItems = completeMenuItems[categoryName];
      const disabledItems = menuItemStatuses.filter(
        status => status.category === categoryName && status.isDisabled
      );

      return {
        categoryName,
        isDisabled: disabledItems.length === categoryItems.length,
        disabledOn: 'both', // Default
        reason: '',
        disabledItemsCount: disabledItems.length,
        totalItemsCount: categoryItems.length
      };
    });

    setCategoryStatuses(categoryStats);
  };

  // Refresh statuses from manager
  const refreshMenuStatuses = () => {
    const statuses = menuStatusManager.getAllStatuses();
    setMenuItemStatuses(statuses);
    updateCategoryStatuses();
  };

  // Toggle category expansion
  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryName) 
        ? prev.filter(cat => cat !== categoryName)
        : [...prev, categoryName]
    );
  };

  // Get item status
  const getItemStatus = (itemId: string) => {
    return menuStatusManager.getItemStatus(itemId);
  };

  // Handle item disable/enable
  const handleItemToggle = (item: any, categoryName: string) => {
    const currentStatus = getItemStatus(item.id.toString());

    if (currentStatus?.isDisabled) {
      // Enable item
      const success = menuStatusManager.enableItem(item.id.toString());
      if (success) {
        refreshMenuStatuses();
        toast({
          title: "Item Enabled",
          description: `${item.name} is now available`,
        });
      }
    } else {
      // Open disable dialog
      setSelectedItem({ ...item, categoryName });
      setIsItemDialogOpen(true);
    }
  };

  // Handle category disable/enable
  const handleCategoryToggle = (categoryName: string) => {
    const categoryItems = completeMenuItems[categoryName];
    const disabledItems = menuItemStatuses.filter(
      status => status.category === categoryName && status.isDisabled
    );

    if (disabledItems.length === categoryItems.length) {
      // Enable entire category
      const success = menuStatusManager.enableCategory(categoryName);
      if (success) {
        refreshMenuStatuses();
        toast({
          title: "Category Enabled",
          description: `All items in ${categoryName} are now available`,
        });
      }
    } else {
      // Open category disable dialog
      setSelectedCategoryName(categoryName);
      setIsCategoryDialogOpen(true);
    }
  };

  // Apply item disable
  const applyItemDisable = () => {
    if (!selectedItem || !disableReason) return;

    const scheduleFromDateTime = useSchedule && scheduleFromDate && scheduleFromTime
      ? `${scheduleFromDate}T${scheduleFromTime}:00`
      : undefined;
    const scheduleToDateTime = useSchedule && scheduleToDate && scheduleToTime
      ? `${scheduleToDate}T${scheduleToTime}:00`
      : undefined;

    const success = menuStatusManager.disableItem(
      selectedItem.id.toString(),
      selectedItem.name,
      selectedItem.categoryName,
      disableScope,
      disableReason === "Other" ? customReason : disableReason,
      scheduleFromDateTime,
      scheduleToDateTime,
      disableReason === "Other" ? customReason : undefined
    );

    if (success) {
      refreshMenuStatuses();

      // Reset form
      resetForm();
      setIsItemDialogOpen(false);

      toast({
        title: "Item Disabled",
        description: `${selectedItem.name} has been disabled`,
      });
    }
  };

  // Apply category disable
  const applyCategoryDisable = () => {
    if (!selectedCategoryName || !disableReason) return;

    const scheduleFromDateTime = useSchedule && scheduleFromDate && scheduleFromTime
      ? `${scheduleFromDate}T${scheduleFromTime}:00`
      : undefined;
    const scheduleToDateTime = useSchedule && scheduleToDate && scheduleToTime
      ? `${scheduleToDate}T${scheduleToTime}:00`
      : undefined;

    const categoryItems = completeMenuItems[selectedCategoryName];
    const success = menuStatusManager.disableCategory(
      selectedCategoryName,
      categoryItems,
      disableScope,
      disableReason === "Other" ? customReason : disableReason,
      scheduleFromDateTime,
      scheduleToDateTime,
      disableReason === "Other" ? customReason : undefined
    );

    if (success) {
      refreshMenuStatuses();

      // Reset form
      resetForm();
      setIsCategoryDialogOpen(false);

      toast({
        title: "Category Disabled",
        description: `All items in ${selectedCategoryName} have been disabled`,
      });
    }
  };

  const resetForm = () => {
    setDisableScope('both');
    setDisableReason("");
    setCustomReason("");
    setUseSchedule(false);
    setScheduleFromDate("");
    setScheduleFromTime("");
    setScheduleToDate("");
    setScheduleToTime("");
  };

  const resetItemForm = () => {
    setItemForm({
      name: "",
      price: "",
      category: "",
      veg: true,
      spicy: 0,
      popular: false,
      bestseller: false,
      egg: false,
      preparationTime: "15"
    });
  };

  // Add new menu item
  const handleAddItem = () => {
    setSelectedCategoryName("");
    resetItemForm();
    setIsAddItemDialogOpen(true);
  };

  // Edit existing menu item
  const handleEditItem = (item: any, categoryName: string) => {
    setSelectedItem(item);
    setSelectedCategoryName(categoryName);
    setItemForm({
      name: item.name,
      price: item.price.toString(),
      category: categoryName,
      veg: item.veg || false,
      spicy: item.spicy || 0,
      popular: item.popular || false,
      bestseller: item.bestseller || false,
      egg: item.egg || false,
      preparationTime: item.preparationTime?.toString() || "15"
    });
    setIsEditItemDialogOpen(true);
  };

  // Delete menu item
  const handleDeleteItem = (item: any, categoryName: string) => {
    if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
      // In a real app, this would call an API to delete the item
      toast({
        title: "Item Deleted",
        description: `${item.name} has been deleted successfully.`,
      });
      // Refresh the menu data
      initializeMenuStatuses();
    }
  };

  // Save menu item (add or edit)
  const handleSaveItem = () => {
    if (!itemForm.name || !itemForm.price || !itemForm.category) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const itemData = {
      ...itemForm,
      price: parseFloat(itemForm.price),
      spicy: parseInt(itemForm.spicy.toString()),
      preparationTime: parseInt(itemForm.preparationTime)
    };

    if (isEditItemDialogOpen) {
      // Edit existing item
      toast({
        title: "Item Updated",
        description: `${itemForm.name} has been updated successfully.`,
      });
    } else {
      // Add new item
      toast({
        title: "Item Added",
        description: `${itemForm.name} has been added successfully.`,
      });
    }

    setIsAddItemDialogOpen(false);
    setIsEditItemDialogOpen(false);
    resetItemForm();
    initializeMenuStatuses();
  };

  // Set default date/time values when schedule is enabled
  useEffect(() => {
    if (useSchedule && !scheduleFromDate) {
      const now = new Date();
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60000);

      setScheduleFromDate(now.toISOString().split('T')[0]);
      setScheduleFromTime(now.toTimeString().slice(0, 5));
      setScheduleToDate(tomorrow.toISOString().split('T')[0]);
      setScheduleToTime(tomorrow.toTimeString().slice(0, 5));
    }
  }, [useSchedule]);

  // Filter menu data
  const getFilteredCategories = () => {
    let categories = Object.keys(completeMenuItems);

    if (selectedCategory !== "all") {
      categories = categories.filter(cat => cat === selectedCategory);
    }

    return categories;
  };

  const getFilteredItems = (categoryName: string) => {
    let items = completeMenuItems[categoryName];
    
    if (searchTerm) {
      items = items.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (statusFilter !== "all") {
      items = items.filter(item => {
        const status = getItemStatus(item.id.toString());
        if (statusFilter === "disabled") {
          return status?.isDisabled;
        } else {
          return !status?.isDisabled;
        }
      });
    }
    
    if (appFilter !== "all") {
      items = items.filter(item => {
        const status = getItemStatus(item.id.toString());
        return status?.disabledOn === appFilter;
      });
    }
    
    return items;
  };

  const getAppIcon = (scope: string) => {
    switch (scope) {
      case 'customer-app': return <Smartphone className="h-4 w-4" />;
      case 'waiter-app': return <Users className="h-4 w-4" />;
      case 'both': return <div className="flex gap-1"><Smartphone className="h-3 w-3" /><Users className="h-3 w-3" /></div>;
      default: return null;
    }
  };

  const getAppBadgeColor = (scope: string) => {
    switch (scope) {
      case 'customer-app': return 'bg-blue-100 text-blue-700';
      case 'waiter-app': return 'bg-green-100 text-green-700';
      case 'both': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="apk-page-container bg-gray-50" style={{paddingTop: '70px'}}>
      {/* Header */}
      <div className="bg-gradient-primary text-white shadow-lg apk-header-fixed" style={{zIndex: 1000}}>
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-white hover:bg-white/20 transition-all duration-200 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-white truncate">Menu Management</h1>
              <p className="text-white/80 text-xs sm:text-sm truncate">Control menu availability across apps</p>
            </div>
          </div>
          <Logo size="sm" variant="white" />
        </div>
      </div>

      <div className="p-4 space-y-4" style={{paddingTop: '1rem'}}>
        {/* Add New Item Button */}
        <div className="flex justify-end mb-4">
          <Button
            onClick={handleAddItem}
            className="flex items-center gap-2 apk-button-safe"
          >
            <span className="text-lg">+</span>
            Add New Item
          </Button>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="search">Search Items</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Search menu items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label>Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {Object.keys(completeMenuItems).map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Items</SelectItem>
                    <SelectItem value="enabled">Available</SelectItem>
                    <SelectItem value="disabled">Disabled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>App Scope</Label>
                <Select value={appFilter} onValueChange={setAppFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Apps" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Apps</SelectItem>
                    <SelectItem value="customer-app">Customer App</SelectItem>
                    <SelectItem value="waiter-app">This App</SelectItem>
                    <SelectItem value="both">Both Apps</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedCategory("all");
                    setStatusFilter("all");
                    setAppFilter("all");
                  }}
                  className="w-full"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Menu Categories */}
        <div className="space-y-4">
          {getFilteredCategories().map(categoryName => {
            const categoryStatus = categoryStatuses.find(cat => cat.categoryName === categoryName);
            const filteredItems = getFilteredItems(categoryName);
            const isExpanded = expandedCategories.includes(categoryName);

            if (filteredItems.length === 0) return null;

            return (
              <Card key={categoryName} className="overflow-hidden">
                <Collapsible open={isExpanded} onOpenChange={() => toggleCategory(categoryName)}>
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                      <div className="flex flex-col gap-3">
                        <div className="flex items-center gap-3">
                          {isExpanded ? (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-gray-500" />
                          )}
                          <CardTitle className="text-lg">{categoryName}</CardTitle>
                          <Badge variant="outline" className="text-xs">
                            {filteredItems.length} items
                          </Badge>
                          {categoryStatus && categoryStatus.disabledItemsCount > 0 && (
                            <Badge className="bg-red-100 text-red-700 text-xs">
                              {categoryStatus.disabledItemsCount} disabled
                            </Badge>
                          )}
                        </div>

                        <div className="flex justify-end">
                          <Button
                            variant={categoryStatus?.isDisabled ? "default" : "destructive"}
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCategoryToggle(categoryName);
                            }}
                          >
                            {categoryStatus?.isDisabled ? (
                              <>
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Enable Category
                              </>
                            ) : (
                              <>
                                <EyeOff className="h-4 w-4 mr-1" />
                                Disable Category
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>

                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        {filteredItems.map(item => {
                          const itemStatus = getItemStatus(item.id.toString());
                          const isDisabled = itemStatus?.isDisabled || false;

                          return (
                            <div key={item.id} className={`p-3 rounded-lg border ${isDisabled ? 'bg-red-50 border-red-200' : 'bg-white border-gray-200'}`}>
                              <div className="flex flex-col gap-2">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <h4 className={`font-medium text-sm ${isDisabled ? 'text-red-700' : 'text-gray-900'}`}>
                                      {item.name}
                                    </h4>
                                    <p className="text-xs text-gray-600">₹{item.price}</p>
                                  </div>
                                </div>

                                {isDisabled && itemStatus && (
                                  <div className="space-y-1">
                                    <div className="flex items-center gap-1">
                                      {getAppIcon(itemStatus.disabledOn)}
                                      <Badge className={`text-xs ${getAppBadgeColor(itemStatus.disabledOn)}`}>
                                        {itemStatus.disabledOn === 'customer-app' ? 'Customer App' :
                                         itemStatus.disabledOn === 'waiter-app' ? 'This App' : 'Both Apps'}
                                      </Badge>
                                    </div>
                                    <p className="text-xs text-red-600">
                                      <AlertTriangle className="h-3 w-3 inline mr-1" />
                                      {itemStatus.reason}
                                    </p>
                                    {itemStatus.scheduledFrom && itemStatus.scheduleTo && (
                                      <p className="text-xs text-orange-600">
                                        <Clock className="h-3 w-3 inline mr-1" />
                                        Until {new Date(itemStatus.scheduledTo).toLocaleString()}
                                      </p>
                                    )}
                                  </div>
                                )}

                                <div className="flex justify-between items-center">
                                  <div className="flex gap-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-xs"
                                      onClick={() => handleEditItem(item, categoryName)}
                                    >
                                      <span className="text-blue-600">✏️</span>
                                      Edit
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="text-xs text-red-600 hover:text-red-700"
                                      onClick={() => handleDeleteItem(item, categoryName)}
                                    >
                                      <span>🗑️</span>
                                      Delete
                                    </Button>
                                  </div>
                                  <Button
                                    variant={isDisabled ? "default" : "destructive"}
                                    size="sm"
                                    className="text-xs"
                                    onClick={() => handleItemToggle(item, categoryName)}
                                  >
                                    {isDisabled ? (
                                      <>
                                        <Eye className="h-3 w-3 mr-1" />
                                        Enable
                                      </>
                                    ) : (
                                      <>
                                        <EyeOff className="h-3 w-3 mr-1" />
                                        Disable
                                      </>
                                    )}
                                  </Button>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Item Disable Dialog */}
        <Dialog open={isItemDialogOpen} onOpenChange={setIsItemDialogOpen}>
          <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto" style={{zIndex: 9999}}>
            <DialogHeader>
              <DialogTitle>Disable Menu Item</DialogTitle>
              <DialogDescription>
                Configure how to disable "{selectedItem?.name}"
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label>Disable On</Label>
                <Select value={disableScope} onValueChange={(value: any) => setDisableScope(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent style={{zIndex: 10000}}>
                    <SelectItem value="customer-app">Customer App Only</SelectItem>
                    <SelectItem value="waiter-app">This App Only</SelectItem>
                    <SelectItem value="both">Both Apps</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Reason</Label>
                <Select value={disableReason} onValueChange={setDisableReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent style={{zIndex: 10000}}>
                    {DISABLE_REASONS.map(reason => (
                      <SelectItem key={reason} value={reason}>{reason}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {disableReason === "Other" && (
                <div>
                  <Label>Custom Reason</Label>
                  <Input
                    placeholder="Enter custom reason"
                    value={customReason}
                    onChange={(e) => setCustomReason(e.target.value)}
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="schedule"
                  checked={useSchedule}
                  onCheckedChange={setUseSchedule}
                />
                <Label htmlFor="schedule">Schedule disable period</Label>
              </div>

              {useSchedule && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>From Date</Label>
                      <div className="relative">
                        <Input
                          type="date"
                          value={scheduleFromDate}
                          onChange={(e) => setScheduleFromDate(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>From Time</Label>
                      <div className="relative">
                        <Input
                          type="time"
                          value={scheduleFromTime}
                          onChange={(e) => setScheduleFromTime(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>To Date</Label>
                      <div className="relative">
                        <Input
                          type="date"
                          value={scheduleToDate}
                          onChange={(e) => setScheduleToDate(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>To Time</Label>
                      <div className="relative">
                        <Input
                          type="time"
                          value={scheduleToTime}
                          onChange={(e) => setScheduleToTime(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsItemDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={applyItemDisable}
                disabled={!disableReason || (disableReason === "Other" && !customReason)}
              >
                Disable Item
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Category Disable Dialog */}
        <Dialog open={isCategoryDialogOpen} onOpenChange={setIsCategoryDialogOpen}>
          <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto" style={{zIndex: 9999}}>
            <DialogHeader>
              <DialogTitle>Disable Category</DialogTitle>
              <DialogDescription>
                Configure how to disable all items in "{selectedCategoryName}"
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label>Disable On</Label>
                <Select value={disableScope} onValueChange={(value: any) => setDisableScope(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent style={{zIndex: 10000}}>
                    <SelectItem value="customer-app">Customer App Only</SelectItem>
                    <SelectItem value="waiter-app">This App Only</SelectItem>
                    <SelectItem value="both">Both Apps</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Reason</Label>
                <Select value={disableReason} onValueChange={setDisableReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent style={{zIndex: 10000}}>
                    {DISABLE_REASONS.map(reason => (
                      <SelectItem key={reason} value={reason}>{reason}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {disableReason === "Other" && (
                <div>
                  <Label>Custom Reason</Label>
                  <Input
                    placeholder="Enter custom reason"
                    value={customReason}
                    onChange={(e) => setCustomReason(e.target.value)}
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="category-schedule"
                  checked={useSchedule}
                  onCheckedChange={setUseSchedule}
                />
                <Label htmlFor="category-schedule">Schedule disable period</Label>
              </div>

              {useSchedule && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>From Date</Label>
                      <div className="relative">
                        <Input
                          type="date"
                          value={scheduleFromDate}
                          onChange={(e) => setScheduleFromDate(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>From Time</Label>
                      <div className="relative">
                        <Input
                          type="time"
                          value={scheduleFromTime}
                          onChange={(e) => setScheduleFromTime(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>To Date</Label>
                      <div className="relative">
                        <Input
                          type="date"
                          value={scheduleToDate}
                          onChange={(e) => setScheduleToDate(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>To Time</Label>
                      <div className="relative">
                        <Input
                          type="time"
                          value={scheduleToTime}
                          onChange={(e) => setScheduleToTime(e.target.value)}
                          className="w-full"
                          style={{paddingRight: '3rem'}}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCategoryDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={applyCategoryDisable}
                disabled={!disableReason || (disableReason === "Other" && !customReason)}
              >
                Disable Category
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Item Dialog */}
        <Dialog open={isAddItemDialogOpen} onOpenChange={setIsAddItemDialogOpen}>
          <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto" style={{zIndex: 9999}}>
            <DialogHeader>
              <DialogTitle>Add New Menu Item</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Item Name *</Label>
                <Input
                  value={itemForm.name}
                  onChange={(e) => setItemForm({...itemForm, name: e.target.value})}
                  placeholder="Enter item name"
                />
              </div>

              <div>
                <Label>Price *</Label>
                <Input
                  type="number"
                  value={itemForm.price}
                  onChange={(e) => setItemForm({...itemForm, price: e.target.value})}
                  placeholder="Enter price"
                />
              </div>

              <div>
                <Label>Category *</Label>
                <Select value={itemForm.category} onValueChange={(value) => setItemForm({...itemForm, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent style={{zIndex: 10000}}>
                    {Object.keys(completeMenuItems).map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="veg"
                    checked={itemForm.veg}
                    onChange={(e) => setItemForm({...itemForm, veg: e.target.checked})}
                  />
                  <Label htmlFor="veg">Vegetarian</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="egg"
                    checked={itemForm.egg}
                    onChange={(e) => setItemForm({...itemForm, egg: e.target.checked})}
                  />
                  <Label htmlFor="egg">Contains Egg</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="popular"
                    checked={itemForm.popular}
                    onChange={(e) => setItemForm({...itemForm, popular: e.target.checked})}
                  />
                  <Label htmlFor="popular">Popular</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="bestseller"
                    checked={itemForm.bestseller}
                    onChange={(e) => setItemForm({...itemForm, bestseller: e.target.checked})}
                  />
                  <Label htmlFor="bestseller">Bestseller</Label>
                </div>
              </div>

              <div>
                <Label>Spice Level (0-3)</Label>
                <Input
                  type="number"
                  min="0"
                  max="3"
                  value={itemForm.spicy}
                  onChange={(e) => setItemForm({...itemForm, spicy: parseInt(e.target.value) || 0})}
                />
              </div>

              <div>
                <Label>Preparation Time (minutes)</Label>
                <Input
                  type="number"
                  value={itemForm.preparationTime}
                  onChange={(e) => setItemForm({...itemForm, preparationTime: e.target.value})}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={() => setIsAddItemDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveItem}>
                Add Item
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Item Dialog */}
        <Dialog open={isEditItemDialogOpen} onOpenChange={setIsEditItemDialogOpen}>
          <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto" style={{zIndex: 9999}}>
            <DialogHeader>
              <DialogTitle>Edit Menu Item</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Item Name *</Label>
                <Input
                  value={itemForm.name}
                  onChange={(e) => setItemForm({...itemForm, name: e.target.value})}
                  placeholder="Enter item name"
                />
              </div>

              <div>
                <Label>Price *</Label>
                <Input
                  type="number"
                  value={itemForm.price}
                  onChange={(e) => setItemForm({...itemForm, price: e.target.value})}
                  placeholder="Enter price"
                />
              </div>

              <div>
                <Label>Category *</Label>
                <Select value={itemForm.category} onValueChange={(value) => setItemForm({...itemForm, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent style={{zIndex: 10000}}>
                    {Object.keys(completeMenuItems).map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="edit-veg"
                    checked={itemForm.veg}
                    onChange={(e) => setItemForm({...itemForm, veg: e.target.checked})}
                  />
                  <Label htmlFor="edit-veg">Vegetarian</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="edit-egg"
                    checked={itemForm.egg}
                    onChange={(e) => setItemForm({...itemForm, egg: e.target.checked})}
                  />
                  <Label htmlFor="edit-egg">Contains Egg</Label>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="edit-popular"
                    checked={itemForm.popular}
                    onChange={(e) => setItemForm({...itemForm, popular: e.target.checked})}
                  />
                  <Label htmlFor="edit-popular">Popular</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="edit-bestseller"
                    checked={itemForm.bestseller}
                    onChange={(e) => setItemForm({...itemForm, bestseller: e.target.checked})}
                  />
                  <Label htmlFor="edit-bestseller">Bestseller</Label>
                </div>
              </div>

              <div>
                <Label>Spice Level (0-3)</Label>
                <Input
                  type="number"
                  min="0"
                  max="3"
                  value={itemForm.spicy}
                  onChange={(e) => setItemForm({...itemForm, spicy: parseInt(e.target.value) || 0})}
                />
              </div>

              <div>
                <Label>Preparation Time (minutes)</Label>
                <Input
                  type="number"
                  value={itemForm.preparationTime}
                  onChange={(e) => setItemForm({...itemForm, preparationTime: e.target.value})}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button variant="outline" onClick={() => setIsEditItemDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveItem}>
                Update Item
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default MenuManagement;
