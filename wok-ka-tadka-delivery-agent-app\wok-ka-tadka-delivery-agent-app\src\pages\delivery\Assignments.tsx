import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  ArrowLeft, 
  MapPin, 
  Clock, 
  Phone, 
  Package, 
  Search,
  Filter,
  CheckCircle,
  Truck
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const Assignments = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState("all");

  const assignments = [
    {
      id: "56970",
      customerName: "<PERSON><PERSON> Sharma",
      customerPhone: "+91 98765 43210",
      address: "B-204, Sector 18, Noida, UP 201301",
      totalAmount: 345,
      paymentMode: "COD",
      status: "ready",
      orderTime: "12:30 PM",
      distance: "2.3 km",
      duration: "8 mins",
      items: ["Hakka Noodles", "Manchurian Dry", "Fried Rice"],
      specialInstructions: "Ring the bell twice. No contact delivery preferred."
    },
    {
      id: "56969",
      customerName: "<PERSON>hul Gupta",
      customerPhone: "+91 87654 32109",
      address: "45, Connaught Place, New Delhi 110001",
      totalAmount: 520,
      paymentMode: "Prepaid",
      status: "ready",
      orderTime: "12:45 PM",
      distance: "1.8 km",
      duration: "6 mins",
      items: ["Chilli Chicken", "Schezwan Fried Rice", "Hot & Sour Soup"],
      specialInstructions: "Call before arriving. Office building - Gate 2."
    },
    {
      id: "56968",
      customerName: "Anjali Singh",
      customerPhone: "+91 76543 21098",
      address: "Tower C, DLF Phase 2, Gurgaon, HR 122002",
      totalAmount: 280,
      paymentMode: "COD",
      status: "picked",
      orderTime: "11:15 AM",
      distance: "4.2 km",
      duration: "12 mins",
      items: ["Veg Momos", "Schezwan Chutney"],
      specialInstructions: ""
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ready": return "bg-warning text-warning-foreground";
      case "picked": return "bg-primary text-primary-foreground";
      case "delivered": return "bg-success text-success-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ready": return "Ready for Pickup";
      case "picked": return "Picked Up";
      case "delivered": return "Delivered";
      default: return status;
    }
  };

  const handleAcceptDelivery = (assignmentId: string) => {
    navigate(`/delivery/details/${assignmentId}`);
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = assignment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         assignment.id.includes(searchQuery);
    const matchesFilter = filter === "all" || assignment.status === filter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-6">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/20"
            onClick={() => navigate("/delivery/dashboard")}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-semibold">Assigned Deliveries</h1>
            <p className="text-white/90">{filteredAssignments.length} orders waiting</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-white/70" />
            <Input
              placeholder="Search by order ID or customer name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/70"
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant={filter === "all" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setFilter("all")}
              className={filter === "all" ? "" : "text-white hover:bg-white/20"}
            >
              All
            </Button>
            <Button
              variant={filter === "ready" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setFilter("ready")}
              className={filter === "ready" ? "" : "text-white hover:bg-white/20"}
            >
              Ready
            </Button>
            <Button
              variant={filter === "picked" ? "secondary" : "ghost"}
              size="sm"
              onClick={() => setFilter("picked")}
              className={filter === "picked" ? "" : "text-white hover:bg-white/20"}
            >
              Picked Up
            </Button>
          </div>
        </div>
      </div>

      {/* Assignments List */}
      <div className="p-6 -mt-4 pb-20">
        <div className="space-y-4">
          {filteredAssignments.map((assignment) => (
            <Card key={assignment.id} className="shadow-card border-0 overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-lg">#{assignment.id}</CardTitle>
                    <Badge className={getStatusColor(assignment.status)} variant="secondary">
                      {getStatusText(assignment.status)}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-foreground">₹{assignment.totalAmount}</p>
                    <p className="text-sm text-muted-foreground">{assignment.paymentMode}</p>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Customer Info */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-foreground">{assignment.customerName}</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(`tel:${assignment.customerPhone}`)}
                    >
                      <Phone className="h-4 w-4 mr-1" />
                      Call
                    </Button>
                  </div>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-muted-foreground">{assignment.address}</p>
                  </div>
                </div>

                {/* Distance and Time */}
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span>{assignment.distance}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4 text-primary" />
                    <span>{assignment.duration}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Package className="h-4 w-4 text-primary" />
                    <span>{assignment.items.length} items</span>
                  </div>
                </div>

                {/* Order Items */}
                <div className="space-y-1">
                  <p className="text-sm font-medium text-foreground">Order Items:</p>
                  <p className="text-sm text-muted-foreground">{assignment.items.join(", ")}</p>
                </div>

                {/* Special Instructions */}
                {assignment.specialInstructions && (
                  <div className="bg-accent p-3 rounded-lg">
                    <p className="text-sm font-medium text-foreground mb-1">Special Instructions:</p>
                    <p className="text-sm text-muted-foreground">{assignment.specialInstructions}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col gap-3 pt-4 border-t border-gray-100 mt-4">
                  {assignment.status === "ready" && (
                    <Button
                      variant="delivery"
                      size="lg"
                      className="w-full h-12"
                      onClick={() => handleAcceptDelivery(assignment.id)}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Accept Delivery
                    </Button>
                  )}

                  {assignment.status === "picked" && (
                    <Button
                      variant="delivery"
                      size="lg"
                      className="w-full h-12"
                      onClick={() => handleAcceptDelivery(assignment.id)}
                    >
                      <Truck className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full h-12"
                    onClick={() => window.open(`https://maps.google.com/?q=${encodeURIComponent(assignment.address)}`)}
                  >
                    <MapPin className="h-4 w-4 mr-2" />
                    Navigate
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredAssignments.length === 0 && (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">No deliveries found</h3>
            <p className="text-muted-foreground">
              {searchQuery ? "Try adjusting your search terms" : "Check back later for new assignments"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Assignments;