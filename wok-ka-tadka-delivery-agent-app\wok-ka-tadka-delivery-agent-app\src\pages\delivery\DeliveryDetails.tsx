import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  MapPin, 
  Clock, 
  Phone, 
  Package, 
  Navigation,
  CheckCircle,
  Truck,
  User,
  CreditCard,
  AlertCircle,
  QrCode,
  Wallet,
  Receipt
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import QRCodeModal from "@/components/delivery/QRCodeModal";

const DeliveryDetails = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentStatus, setCurrentStatus] = useState("ready");
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);

  // Decode the order ID from URL parameter
  const decodedOrderId = orderId ? decodeURIComponent(orderId) : "56970";

  // Mock data
  const deliveryData = {
    id: decodedOrderId,
    customerName: "Priya Sharma",
    customerPhone: "+91 98765 43210",
    address: "B-204, Sector 18, Noida, UP 201301",
    landmark: "Near Metro Station, Blue Line",
    totalAmount: 345,
    paymentMode: "COD",
    status: currentStatus,
    orderTime: "12:30 PM",
    estimatedDelivery: "1:15 PM",
    distance: "2.3 km",
    duration: "8 mins",
    items: [
      { name: "Hakka Noodles", quantity: 1, price: 120 },
      { name: "Manchurian Dry", quantity: 1, price: 150 },
      { name: "Fried Rice", quantity: 1, price: 75 }
    ],
    specialInstructions: "Ring the bell twice. No contact delivery preferred.",
    restaurantAddress: "Wok Ka Tadka, Sector 15, Noida"
  };

  const handleStatusUpdate = (newStatus: string, message: string) => {
    setCurrentStatus(newStatus);
    toast({
      title: "Status Updated",
      description: message,
    });
  };

  const handlePickup = () => {
    handleStatusUpdate("picked", "Order marked as picked up from restaurant");
  };

  const handleDelivered = () => {
    handleStatusUpdate("delivered", "Order successfully delivered to customer");
    setTimeout(() => {
      navigate("/delivery/dashboard");
    }, 2000);
  };

  const handleOnlinePayment = () => {
    setShowQRCode(true);
    setShowPaymentOptions(false);
  };

  const handleCashCollection = () => {
    toast({
      title: "Cash Collected",
      description: `₹${deliveryData.totalAmount} collected from customer`,
    });
    setShowPaymentOptions(false);
    handleDelivered();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ready": return "bg-warning text-warning-foreground";
      case "picked": return "bg-primary text-primary-foreground";
      case "delivered": return "bg-success text-success-foreground";
      default: return "bg-muted text-muted-foreground";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "ready": return "Ready for Pickup";
      case "picked": return "Picked Up";
      case "delivered": return "Delivered";
      default: return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header with animations */}
      <div className="bg-gradient-primary shadow-lg border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/delivery/assignments")}
              className="text-white hover:bg-white/20 transition-all duration-200 hover:scale-105"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold text-white">Order #{deliveryData.id}</h1>
              <p className="text-sm text-white/80">Delivery Details</p>
            </div>
          </div>
          <Badge className={`${getStatusColor(deliveryData.status)} border-0 px-3 py-1 animate-pulse-soft`}>
            {getStatusText(deliveryData.status)}
          </Badge>
        </div>
      </div>

      <div className="p-4 space-y-4 pb-32">
        {/* Customer Information Card */}
        <Card className="shadow-card border-0 bg-white hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <User className="h-5 w-5 text-primary" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-900">{deliveryData.customerName}</h3>
                <p className="text-sm text-gray-600">{deliveryData.customerPhone}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`tel:${deliveryData.customerPhone}`)}
                className="border-primary text-primary hover:bg-primary hover:text-white hover:scale-105 transition-all duration-200"
              >
                <Phone className="h-4 w-4 mr-1" />
                Call
              </Button>
            </div>

            <Separator className="bg-gray-100" />

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-gray-900 mb-1">Delivery Address</p>
                  <p className="text-sm text-gray-600 leading-relaxed break-words">{deliveryData.address}</p>
                  {deliveryData.landmark && (
                    <p className="text-xs text-gray-500 mt-1">📍 {deliveryData.landmark}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-6 text-sm bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-primary" />
                  <span className="font-medium">{deliveryData.distance}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-primary" />
                  <span className="font-medium">{deliveryData.duration}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Order Details Card */}
        <Card className="shadow-card border-0 bg-white hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-gray-900">
              <Package className="h-5 w-5 text-primary" />
              Order Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {deliveryData.items.map((item, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{item.name}</p>
                    <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                  </div>
                  <p className="font-semibold text-gray-900">₹{item.price}</p>
                </div>
              ))}
            </div>

            <Separator className="bg-gray-100" />

            <div className="bg-primary/5 p-4 rounded-lg">
              <div className="flex items-center justify-between text-lg font-bold">
                <span className="text-gray-900">Total Amount</span>
                <span className="text-primary">₹{deliveryData.totalAmount}</span>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <CreditCard className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-primary">{deliveryData.paymentMode}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Special Instructions */}
        {deliveryData.specialInstructions && (
          <Card className="shadow-card border-0 bg-amber-50 border-l-4 border-l-amber-400">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-amber-800 mb-1">Special Instructions</p>
                  <p className="text-sm text-amber-700 break-words">{deliveryData.specialInstructions}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Time Information */}
        <Card className="shadow-card border-0 bg-white">
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">Order Time</p>
                <p className="font-semibold text-gray-900">{deliveryData.orderTime}</p>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-500 mb-1">Est. Delivery</p>
                <p className="font-semibold text-gray-900">{deliveryData.estimatedDelivery}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Fixed Bottom Action Bar - Improved Layout */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-20">
        <div className="p-4 max-w-lg mx-auto space-y-3">
          {/* Navigation Button */}
          <Button
            variant="outline"
            size="lg"
            className="w-full border-primary text-primary hover:bg-primary hover:text-white hover:scale-[1.02] transition-all duration-200"
            onClick={() => window.open(`https://maps.google.com/?q=${encodeURIComponent(deliveryData.address)}`)}
          >
            <Navigation className="h-5 w-5 mr-2" />
            Start Navigation
          </Button>

          {/* Status Action Buttons */}
          {deliveryData.status === "ready" && (
            <Button
              variant="delivery"
              size="lg"
              className="w-full hover:scale-[1.02] transition-all duration-200"
              onClick={handlePickup}
            >
              <CheckCircle className="h-5 w-5 mr-2" />
              Mark as Picked Up
            </Button>
          )}

          {deliveryData.status === "picked" && (
            <div className="space-y-3">
              {deliveryData.paymentMode === "COD" && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-amber-800 mb-2">
                    <CreditCard className="h-5 w-5" />
                    <span className="font-semibold">Collect ₹{deliveryData.totalAmount}</span>
                  </div>
                  
                  {!showPaymentOptions ? (
                    <Button
                      variant="warning"
                      size="sm"
                      className="w-full"
                      onClick={() => setShowPaymentOptions(true)}
                    >
                      <Wallet className="h-4 w-4 mr-2" />
                      Choose Payment Method
                    </Button>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleOnlinePayment}
                        className="border-primary text-primary hover:bg-primary hover:text-white"
                      >
                        <QrCode className="h-4 w-4 mr-1" />
                        Scan & Pay
                      </Button>
                      <Button
                        variant="success"
                        size="sm"
                        onClick={handleCashCollection}
                      >
                        <Receipt className="h-4 w-4 mr-1" />
                        Cash Collected
                      </Button>
                    </div>
                  )}
                </div>
              )}

              <Button
                variant="success"
                size="lg"
                className="w-full hover:scale-[1.02] transition-all duration-200"
                onClick={handleDelivered}
              >
                <Truck className="h-5 w-5 mr-2" />
                Mark as Delivered
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* QR Code Modal */}
      <QRCodeModal
        isOpen={showQRCode}
        onClose={() => setShowQRCode(false)}
        amount={deliveryData.totalAmount}
      />
    </div>
  );
};

export default DeliveryDetails;
