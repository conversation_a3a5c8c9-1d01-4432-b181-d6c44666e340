import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Search,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  Key,
  Eye,
  EyeOff,
  Copy,
  RotateCcw,
  UserPlus,
  Shield,
  ShieldCheck,
  ShieldX
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  getAllStaffPins,
  createStaffPin,
  updateStaffPin,
  deleteStaffPin,
  resetStaffPin,
  getStaffStatistics,
  initializeDefaultPins,
  type StaffPin
} from "@/utils/staffPinStorage";

const StaffPinManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [staffPins, setStaffPins] = useState<StaffPin[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffPin | null>(null);
  const [showPins, setShowPins] = useState<{[key: string]: boolean}>({});
  const [newStaffData, setNewStaffData] = useState({
    name: "",
    phone: "",
    role: "waiter" as StaffPin['role']
  });

  const roles = ["all", "waiter", "delivery", "kitchen", "manager"];

  useEffect(() => {
    initializeDefaultPins();
    loadStaffPins();
  }, []);

  const loadStaffPins = () => {
    const pins = getAllStaffPins();
    setStaffPins(pins);
  };

  const filteredStaff = staffPins.filter(staff => {
    const matchesSearch = staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         staff.phone.includes(searchTerm);
    const matchesRole = selectedRole === "all" || staff.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  const stats = getStaffStatistics();

  const handleCreateStaff = () => {
    if (!newStaffData.name || !newStaffData.phone) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Check if phone already exists
    const existingStaff = staffPins.find(staff => staff.phone === newStaffData.phone);
    if (existingStaff) {
      toast({
        title: "Error",
        description: "Staff member with this phone number already exists",
        variant: "destructive",
      });
      return;
    }

    try {
      const newStaff = createStaffPin({
        ...newStaffData,
        createdBy: "admin" // In real app, this would be the logged-in admin's ID
      });



      loadStaffPins();
      setIsCreateDialogOpen(false);
      setNewStaffData({ name: "", phone: "", role: "waiter" });

      toast({
        title: "Success",
        description: `Staff PIN created successfully. PIN: ${newStaff.pin}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create staff PIN",
        variant: "destructive",
      });
    }
  };

  const handleEditStaff = () => {
    if (!selectedStaff) return;

    try {
      updateStaffPin(selectedStaff.id, {
        name: selectedStaff.name,
        phone: selectedStaff.phone,
        role: selectedStaff.role
      });

      loadStaffPins();
      setIsEditDialogOpen(false);
      setSelectedStaff(null);

      toast({
        title: "Success",
        description: "Staff information updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update staff information",
        variant: "destructive",
      });
    }
  };

  const handleDeleteStaff = (staff: StaffPin) => {
    if (window.confirm(`Are you sure you want to delete ${staff.name}'s PIN?`)) {
      try {
        deleteStaffPin(staff.id);
        loadStaffPins();

        toast({
          title: "Success",
          description: "Staff PIN deleted successfully",
        });
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete staff PIN",
          variant: "destructive",
        });
      }
    }
  };

  const handleResetPin = (staff: StaffPin) => {
    if (window.confirm(`Are you sure you want to reset ${staff.name}'s PIN?`)) {
      try {
        const newPin = resetStaffPin(staff.id);
        if (newPin) {
          loadStaffPins();
          toast({
            title: "Success",
            description: `PIN reset successfully. New PIN: ${newPin}`,
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to reset PIN",
          variant: "destructive",
        });
      }
    }
  };

  const handleToggleStatus = (staff: StaffPin) => {
    try {
      updateStaffPin(staff.id, { isActive: !staff.isActive });
      loadStaffPins();

      toast({
        title: "Success",
        description: `Staff ${staff.isActive ? 'deactivated' : 'activated'} successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update staff status",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "PIN copied to clipboard",
    });
  };

  const togglePinVisibility = (staffId: string) => {
    setShowPins(prev => ({
      ...prev,
      [staffId]: !prev[staffId]
    }));
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "waiter": return "bg-blue-100 text-blue-800";
      case "delivery": return "bg-green-100 text-green-800";
      case "kitchen": return "bg-orange-100 text-orange-800";
      case "manager": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? "bg-green-100 text-green-800 border-green-200"
      : "bg-red-100 text-red-800 border-red-200";
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Staff PIN Management</h1>
              <p className="text-sm text-gray-500">Manage staff login PINs and access</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCreateDialogOpen(true)}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Add Staff PIN
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={loadStaffPins}
              className="text-gray-600"
            >
              <RefreshCw className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{stats.total}</p>
              <p className="text-sm text-blue-100">Total PINs</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{stats.active}</p>
              <p className="text-sm text-green-100">Active</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{stats.byRole.waiter}</p>
              <p className="text-sm text-purple-100">Waiters</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{stats.byRole.delivery}</p>
              <p className="text-sm text-orange-100">Delivery</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{stats.inactive}</p>
              <p className="text-sm text-red-100">Inactive</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div>
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                >
                  {roles.map(role => (
                    <option key={role} value={role}>
                      {role === "all" ? "All Roles" : role.charAt(0).toUpperCase() + role.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Staff PINs List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredStaff.map((staff) => (
            <Card key={staff.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-bold text-lg">{staff.name}</h3>
                      <Badge className={getRoleColor(staff.role)}>
                        {staff.role}
                      </Badge>
                      <Badge className={getStatusColor(staff.isActive)}>
                        {staff.isActive ? (
                          <><ShieldCheck className="h-3 w-3 mr-1" />Active</>
                        ) : (
                          <><ShieldX className="h-3 w-3 mr-1" />Inactive</>
                        )}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{staff.phone}</p>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Key className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium">PIN:</span>
                      <span className="font-mono text-lg">
                        {showPins[staff.id] ? staff.pin : "••••"}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => togglePinVisibility(staff.id)}
                        className="h-8 w-8 p-0"
                      >
                        {showPins[staff.id] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(staff.pin)}
                        className="h-8 w-8 p-0"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                  <div>
                    <p className="text-gray-500">Created</p>
                    <p className="font-medium">{new Date(staff.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Last Used</p>
                    <p className="font-medium">
                      {staff.lastUsed 
                        ? new Date(staff.lastUsed).toLocaleDateString()
                        : "Never"
                      }
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3">
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedStaff(staff);
                        setIsEditDialogOpen(true);
                      }}
                      className="flex items-center justify-center gap-1 w-full sm:w-auto"
                    >
                      <Edit className="h-4 w-4" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleResetPin(staff)}
                      className="flex items-center justify-center gap-1 w-full sm:w-auto"
                    >
                      <RotateCcw className="h-4 w-4" />
                      Reset PIN
                    </Button>
                  </div>

                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleStatus(staff)}
                      className={`flex items-center justify-center gap-1 w-full sm:w-auto ${
                        staff.isActive
                          ? "text-red-600 border-red-600 hover:bg-red-50"
                          : "text-green-600 border-green-600 hover:bg-green-50"
                      }`}
                    >
                      <Shield className="h-4 w-4" />
                      {staff.isActive ? "Deactivate" : "Activate"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteStaff(staff)}
                      className="text-red-600 border-red-600 hover:bg-red-50 flex items-center justify-center w-full sm:w-auto"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredStaff.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No staff PINs found matching your criteria</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Create Staff Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Staff PIN</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Staff Name</Label>
              <Input
                id="name"
                value={newStaffData.name}
                onChange={(e) => setNewStaffData({...newStaffData, name: e.target.value})}
                placeholder="Enter staff name"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={newStaffData.phone}
                onChange={(e) => setNewStaffData({...newStaffData, phone: e.target.value})}
                placeholder="Enter phone number"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <select
                id="role"
                value={newStaffData.role}
                onChange={(e) => setNewStaffData({...newStaffData, role: e.target.value as StaffPin['role']})}
                className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm mt-1"
              >
                <option value="waiter">Waiter</option>
                <option value="delivery">Delivery</option>
                <option value="kitchen">Kitchen</option>
                <option value="manager">Manager</option>
              </select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateStaff}>
                Create PIN
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Staff Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Staff Information</DialogTitle>
          </DialogHeader>
          {selectedStaff && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="editName">Staff Name</Label>
                <Input
                  id="editName"
                  value={selectedStaff.name}
                  onChange={(e) => setSelectedStaff({...selectedStaff, name: e.target.value})}
                  placeholder="Enter staff name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="editPhone">Phone Number</Label>
                <Input
                  id="editPhone"
                  value={selectedStaff.phone}
                  onChange={(e) => setSelectedStaff({...selectedStaff, phone: e.target.value})}
                  placeholder="Enter phone number"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="editRole">Role</Label>
                <select
                  id="editRole"
                  value={selectedStaff.role}
                  onChange={(e) => setSelectedStaff({...selectedStaff, role: e.target.value as StaffPin['role']})}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm mt-1"
                >
                  <option value="waiter">Waiter</option>
                  <option value="delivery">Delivery</option>
                  <option value="kitchen">Kitchen</option>
                  <option value="manager">Manager</option>
                </select>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditStaff}>
                  Update
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StaffPinManagement;
