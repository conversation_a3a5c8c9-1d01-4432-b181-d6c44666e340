# Everycom EC58 Thermal Printer Integration Guide

This guide will help you set up your Everycom EC58 thermal printer with your restaurant management system.

## 🖨️ What's Been Added

### New Features
- **Thermal Printer Service**: Complete integration with Everycom EC58 printer
- **Unified Print Service**: Automatically uses thermal printer when available, falls back to browser printing
- **Printer Setup Component**: Easy configuration interface in Admin Settings
- **Multiple Connection Types**: USB, Bluetooth, and Network support
- **Thermal Print Formatting**: Optimized 58mm thermal receipt format

### Updated Components
- **Admin Settings**: Added printer configuration section
- **KOT Printing**: Now uses thermal printer when available
- **Bill Printing**: Enhanced with thermal printer support
- **Print Service**: Unified service handles both thermal and browser printing

## 🔧 Setup Instructions

### Step 1: Access Printer Settings
1. Go to **Admin Dashboard** → **Settings**
2. Scroll down to **"Thermal Printer Configuration"** section
3. You'll see the printer setup interface

### Step 2: Choose Connection Type

#### Option A: USB Connection (Recommended)
1. Connect your EC58 printer to your computer via USB cable
2. Select **"Everycom EC58 USB"** from the dropdown
3. Click **"Connect Printer"**
4. Your browser will ask for USB device permission - grant it
5. Select your EC58 printer from the list

#### Option B: Bluetooth Connection
1. First, pair your EC58 printer in your device's Bluetooth settings
2. Select **"Everycom EC58 Bluetooth"** from the dropdown
3. Click **"Connect Printer"**
4. Your browser will ask for Bluetooth permission - grant it
5. Select your EC58 printer from the list

#### Option C: Network Connection (If supported)
1. Connect your printer to the same network as your computer
2. Find your printer's IP address (usually printed on a test page)
3. Select **"Network Printer"** from the dropdown
4. Click **"Advanced"** to configure IP address and port
5. Enter your printer's IP address (e.g., *************)
6. Set port to 9100 (default for most thermal printers)
7. Click **"Use Custom"** then **"Connect Printer"**

### Step 3: Test the Connection
1. Once connected, you'll see a green "Connected" badge
2. Click **"Test Print"** to print a test page
3. If the test page prints successfully, your setup is complete!

## 🎯 How It Works

### Automatic Fallback
- When thermal printer is connected: Uses thermal printer for all printing
- When thermal printer is not available: Falls back to browser printing
- No changes needed in your workflow - it's automatic!

### Print Formats
- **KOT (Kitchen Order Ticket)**: Optimized for kitchen use with clear item listing
- **Bills**: Professional format with GST calculations, customer details, and totals
- **58mm Width**: Perfectly formatted for thermal paper width

### Print Locations
All existing print buttons now use the thermal printer:
- **KOT Generation**: "Print KOT" button
- **Bill Generation**: "Print Bill" button  
- **Table Management**: Print buttons in table details
- **Order Management**: Print functions throughout the system

## 🔍 Troubleshooting

### Printer Not Connecting
1. **USB**: Ensure cable is properly connected and printer is powered on
2. **Bluetooth**: Make sure printer is paired in device settings first
3. **Network**: Verify printer and computer are on same network
4. **Browser**: Use Chrome/Edge for best compatibility (Safari has limited support)

### Print Quality Issues
1. Check thermal paper is loaded correctly
2. Ensure paper is not faded or old
3. Clean printer head if prints are faint
4. Verify paper width is 58mm

### Permission Issues
1. **USB**: Browser needs permission to access USB devices
2. **Bluetooth**: Browser needs Bluetooth permission
3. **HTTPS**: Some features require HTTPS connection

### Fallback Printing
If thermal printer fails, the system automatically falls back to browser printing:
1. A print dialog will open in your browser
2. Select your regular printer or save as PDF
3. The format will be optimized for A4 paper

## 📱 Browser Compatibility

### Fully Supported
- **Chrome**: All features supported
- **Edge**: All features supported

### Limited Support
- **Firefox**: USB and Network only (no Bluetooth)
- **Safari**: Network only (limited USB/Bluetooth support)

### Mobile Browsers
- Limited support for direct printer connection
- Falls back to browser printing

## 🛠️ Advanced Configuration

### Custom Printer Settings
1. Click **"Advanced"** in the printer setup
2. Configure:
   - Printer name
   - Connection type
   - IP address (for network)
   - Port number
   - Paper width

### Multiple Printers
- Configure different printers for different purposes
- Switch between configurations as needed
- Save multiple printer profiles

## 📋 Print Content

### KOT Format
```
WOK KA TADKA
KITCHEN ORDER TICKET
--------------------------------
KOT No: KOT123456
Table: T01
Date: 29/07/2025
Time: 2:30 PM
--------------------------------
ITEMS:
--------------------------------
Chicken Biryani
Qty: 2        ₹250.00

Paneer Butter Masala  
Qty: 1        ₹180.00

Special Instructions:
Extra spicy, no onions
--------------------------------
KITCHEN COPY
```

### Bill Format
```
WOK KA TADKA
RESTAURANT BILL
GST: 12ABCDE1234F1Z5
--------------------------------
Bill No: KOT123456
Table: T01
Date: 29/07/2025
Time: 2:30 PM
--------------------------------
CUSTOMER DETAILS:
Name: John Doe
Phone: +91 9876543210
Status: Regular Customer ⭐
--------------------------------
ITEMS:
--------------------------------
Chicken Biryani
2 x ₹250.00           ₹500.00
Paneer Butter Masala
1 x ₹180.00           ₹180.00
--------------------------------
Subtotal:             ₹680.00
CGST (9%):            ₹61.20
SGST (9%):            ₹61.20
--------------------------------
GRAND TOTAL:          ₹802.40
--------------------------------
Payment: CASH
--------------------------------
Thank you for dining!
Visit us again soon!
www.wokkatadka.com
```

## 🎉 You're All Set!

Your Everycom EC58 thermal printer is now integrated with your restaurant management system. All print operations will automatically use the thermal printer when available, providing fast, professional receipts for your customers and clear KOTs for your kitchen staff.

For any issues or questions, the system includes comprehensive error handling and will always fall back to browser printing to ensure your operations continue smoothly.
