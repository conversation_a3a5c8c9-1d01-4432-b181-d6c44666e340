// Attendance Storage Utility for managing staff attendance tracking
import { getCurrentWorkingHours } from './workingHoursStorage';
import { getStaffById } from './staffPinStorage';

export interface AttendanceRecord {
  id: string;
  staffId: string;
  staffName: string;
  staffPhone: string;
  date: string; // YYYY-MM-DD format
  checkInTime?: string; // ISO string
  checkOutTime?: string; // ISO string
  status: 'checked-in' | 'checked-out' | 'absent';
  totalHours?: number;
  regularHours?: number; // Hours within scheduled working hours
  overtimeHours?: number; // Hours beyond scheduled working hours
  notes?: string;
  location?: string; // Optional: GPS location or manual location
  isEarlyCheckIn?: boolean; // Checked in before scheduled start time
  isLateCheckIn?: boolean; // Checked in after scheduled start time
  isEarlyCheckOut?: boolean; // Checked out before scheduled end time
  isOvertimeCheckOut?: boolean; // Checked out after scheduled end time + overtime threshold
}

const STORAGE_KEY = 'wok_ka_tadka_attendance';

// Get all attendance records from localStorage
export const getAllAttendanceRecords = (): AttendanceRecord[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading attendance records:', error);
    return [];
  }
};

// Save attendance records to localStorage
export const saveAttendanceRecords = (records: AttendanceRecord[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(records));
  } catch (error) {
    console.error('Error saving attendance records:', error);
  }
};

// Get today's date in YYYY-MM-DD format
export const getTodayDate = (): string => {
  return new Date().toISOString().split('T')[0];
};

// Get current time in ISO format
export const getCurrentTime = (): string => {
  return new Date().toISOString();
};

// Calculate hours between two times
export const calculateHours = (checkIn: string, checkOut: string): number => {
  const checkInTime = new Date(checkIn);
  const checkOutTime = new Date(checkOut);
  const diffMs = checkOutTime.getTime() - checkInTime.getTime();
  return Math.round((diffMs / (1000 * 60 * 60)) * 100) / 100; // Round to 2 decimal places
};

// Update calculateDetailedHours to accept staffId and use per-staff working hours if set
export const calculateDetailedHours = (checkIn: string, checkOut: string, date: string, staffId?: string) => {
  const checkInTime = new Date(checkIn);
  const checkOutTime = new Date(checkOut);
  let workingHours;
  if (staffId) {
    const staff = getStaffById(staffId);
    workingHours = staff?.workingHours || getCurrentWorkingHours();
  } else {
    workingHours = getCurrentWorkingHours();
  }

  // Get scheduled times for the date
  const workDate = new Date(date);
  const [startHour, startMinute] = workingHours.startTime.split(':').map(Number);
  const [endHour, endMinute] = workingHours.endTime.split(':').map(Number);

  const scheduledStart = new Date(workDate);
  scheduledStart.setHours(startHour, startMinute, 0, 0);

  const scheduledEnd = new Date(workDate);
  scheduledEnd.setHours(endHour, endMinute, 0, 0);

  const overtimeThreshold = new Date(scheduledEnd);
  overtimeThreshold.setMinutes(overtimeThreshold.getMinutes() + workingHours.overtimeThreshold);

  // Calculate total hours
  const totalHours = calculateHours(checkIn, checkOut);

  // Determine check-in status
  const isEarlyCheckIn = checkInTime < scheduledStart;
  const isLateCheckIn = checkInTime > scheduledStart;

  // Determine check-out status
  const isEarlyCheckOut = checkOutTime < scheduledEnd;
  const isOvertimeCheckOut = checkOutTime > overtimeThreshold;

  // Calculate regular and overtime hours
  let regularHours = 0;
  let overtimeHours = 0;

  // Effective work start time (later of actual check-in or scheduled start)
  const effectiveStart = new Date(Math.max(checkInTime.getTime(), scheduledStart.getTime()));

  // Effective work end time for regular hours (earlier of actual check-out or scheduled end)
  const effectiveRegularEnd = new Date(Math.min(checkOutTime.getTime(), scheduledEnd.getTime()));

  // Calculate regular hours (within scheduled time)
  if (effectiveRegularEnd > effectiveStart) {
    regularHours = (effectiveRegularEnd.getTime() - effectiveStart.getTime()) / (1000 * 60 * 60);
  }

  // Calculate overtime hours (beyond scheduled end time + threshold)
  if (checkOutTime > overtimeThreshold) {
    overtimeHours = (checkOutTime.getTime() - overtimeThreshold.getTime()) / (1000 * 60 * 60);
  }

  return {
    totalHours: Math.round(totalHours * 100) / 100,
    regularHours: Math.round(regularHours * 100) / 100,
    overtimeHours: Math.round(overtimeHours * 100) / 100,
    isEarlyCheckIn,
    isLateCheckIn,
    isEarlyCheckOut,
    isOvertimeCheckOut
  };
};

// Check if staff has already checked in today
export const getTodayAttendance = (staffId: string): AttendanceRecord | null => {
  const records = getAllAttendanceRecords();
  const today = getTodayDate();
  return records.find(record => 
    record.staffId === staffId && 
    record.date === today
  ) || null;
};

// Mark check-in for staff
export const markCheckIn = (staffId: string, staffName: string, staffPhone: string, notes?: string): AttendanceRecord => {
  const records = getAllAttendanceRecords();
  const today = getTodayDate();
  const currentTime = getCurrentTime();
  
  // Check if already checked in today
  const existingRecord = getTodayAttendance(staffId);
  if (existingRecord) {
    throw new Error('Already checked in today');
  }
  
  const newRecord: AttendanceRecord = {
    id: `${staffId}-${today}`,
    staffId,
    staffName,
    staffPhone,
    date: today,
    checkInTime: currentTime,
    status: 'checked-in',
    notes
  };
  
  records.push(newRecord);
  saveAttendanceRecords(records);
  return newRecord;
};

// Mark check-out for staff
export const markCheckOut = (staffId: string, notes?: string): AttendanceRecord => {
  const records = getAllAttendanceRecords();
  const today = getTodayDate();
  const currentTime = getCurrentTime();

  const recordIndex = records.findIndex(record =>
    record.staffId === staffId &&
    record.date === today
  );

  if (recordIndex === -1) {
    throw new Error('No check-in record found for today');
  }

  const record = records[recordIndex];
  if (record.status === 'checked-out') {
    throw new Error('Already checked out today');
  }

  // Update the record with detailed hours calculation
  record.checkOutTime = currentTime;
  record.status = 'checked-out';

  if (record.checkInTime) {
    const detailedHours = calculateDetailedHours(record.checkInTime, currentTime, record.date, staffId);
    record.totalHours = detailedHours.totalHours;
    record.regularHours = detailedHours.regularHours;
    record.overtimeHours = detailedHours.overtimeHours;
    record.isEarlyCheckIn = detailedHours.isEarlyCheckIn;
    record.isLateCheckIn = detailedHours.isLateCheckIn;
    record.isEarlyCheckOut = detailedHours.isEarlyCheckOut;
    record.isOvertimeCheckOut = detailedHours.isOvertimeCheckOut;
  } else {
    record.totalHours = 0;
    record.regularHours = 0;
    record.overtimeHours = 0;
  }

  if (notes) {
    record.notes = record.notes ? `${record.notes}; ${notes}` : notes;
  }

  records[recordIndex] = record;
  saveAttendanceRecords(records);
  return record;
};

// Mark check-out for staff with custom time (for admin approvals)
export const markCheckOutWithTime = (staffId: string, checkOutTime: string, notes?: string): AttendanceRecord => {
  const records = getAllAttendanceRecords();
  const today = getTodayDate();

  const recordIndex = records.findIndex(record =>
    record.staffId === staffId &&
    record.date === today
  );

  if (recordIndex === -1) {
    throw new Error('No check-in record found for today');
  }

  const record = records[recordIndex];
  if (record.status === 'checked-out') {
    throw new Error('Already checked out today');
  }

  // Update the record with detailed hours calculation using custom time
  record.checkOutTime = checkOutTime;
  record.status = 'checked-out';

  if (record.checkInTime) {
    const detailedHours = calculateDetailedHours(record.checkInTime, checkOutTime, record.date, staffId);
    record.totalHours = detailedHours.totalHours;
    record.regularHours = detailedHours.regularHours;
    record.overtimeHours = detailedHours.overtimeHours;
    record.isEarlyCheckIn = detailedHours.isEarlyCheckIn;
    record.isLateCheckIn = detailedHours.isLateCheckIn;
    record.isEarlyCheckOut = detailedHours.isEarlyCheckOut;
    record.isOvertimeCheckOut = detailedHours.isOvertimeCheckOut;
  } else {
    record.totalHours = 0;
    record.regularHours = 0;
    record.overtimeHours = 0;
  }

  if (notes) {
    record.notes = notes;
  }

  records[recordIndex] = record;
  saveAttendanceRecords(records);
  return record;
};

// Get attendance records for a specific staff member
export const getStaffAttendanceRecords = (staffId: string, limit?: number): AttendanceRecord[] => {
  const records = getAllAttendanceRecords();
  const staffRecords = records
    .filter(record => record.staffId === staffId)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  return limit ? staffRecords.slice(0, limit) : staffRecords;
};

// Get attendance records for a specific date range
export const getAttendanceByDateRange = (startDate: string, endDate: string): AttendanceRecord[] => {
  const records = getAllAttendanceRecords();
  return records.filter(record => 
    record.date >= startDate && record.date <= endDate
  ).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

// Get attendance statistics for a staff member
export const getStaffAttendanceStats = (staffId: string, days: number = 30): {
  totalDays: number;
  presentDays: number;
  absentDays: number;
  averageHours: number;
  totalHours: number;
} => {
  const endDate = getTodayDate();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  const startDateStr = startDate.toISOString().split('T')[0];
  
  const records = getAttendanceByDateRange(startDateStr, endDate)
    .filter(record => record.staffId === staffId);
  
  const presentDays = records.filter(record => record.status !== 'absent').length;
  const totalHours = records.reduce((sum, record) => sum + (record.totalHours || 0), 0);
  
  return {
    totalDays: days,
    presentDays,
    absentDays: days - presentDays,
    averageHours: presentDays > 0 ? Math.round((totalHours / presentDays) * 100) / 100 : 0,
    totalHours: Math.round(totalHours * 100) / 100
  };
};

// Format time for display
export const formatTime = (isoString: string): string => {
  return new Date(isoString).toLocaleTimeString('en-IN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

// Format date for display
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Check if staff can check out (must be checked in first)
export const canCheckOut = (staffId: string): boolean => {
  const todayRecord = getTodayAttendance(staffId);
  return todayRecord !== null && todayRecord.status === 'checked-in';
};

// Check if staff can check in (not already checked in today)
export const canCheckIn = (staffId: string): boolean => {
  const todayRecord = getTodayAttendance(staffId);
  return todayRecord === null;
};
