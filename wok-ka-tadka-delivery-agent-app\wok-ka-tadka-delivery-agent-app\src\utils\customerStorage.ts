// Customer Management System for Table Orders

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  notes?: string;
  createdAt: string;
  lastVisit: string;
  totalOrders: number;
  totalSpent: number;
  isRegular: boolean;
}

export interface CustomerOrder {
  customerId: string;
  orderId: string;
  tableId: string;
  kotNumber: string;
  amount: number;
  date: string;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
}

// Customer Storage Manager Class
export class CustomerManager {
  private static instance: CustomerManager;
  private customers: Customer[] = [];
  private customerOrders: CustomerOrder[] = [];

  private constructor() {
    this.loadFromStorage();
  }

  static getInstance(): CustomerManager {
    if (!CustomerManager.instance) {
      CustomerManager.instance = new CustomerManager();
    }
    return CustomerManager.instance;
  }

  // Load data from localStorage
  private loadFromStorage(): void {
    try {
      const customersData = localStorage.getItem('restaurant_customers');
      const ordersData = localStorage.getItem('customer_orders');

      if (customersData) {
        this.customers = JSON.parse(customersData);
      }
      if (ordersData) {
        this.customerOrders = JSON.parse(ordersData);
      }
    } catch (error) {
      console.error('Error loading customer data:', error);
      this.customers = [];
      this.customerOrders = [];
    }
  }

  // Save data to localStorage
  private saveToStorage(): void {
    try {
      localStorage.setItem('restaurant_customers', JSON.stringify(this.customers));
      localStorage.setItem('customer_orders', JSON.stringify(this.customerOrders));
    } catch (error) {
      console.error('Error saving customer data:', error);
    }
  }

  // Generate unique customer ID
  private generateCustomerId(): string {
    return `CUST${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  // Add new customer
  addCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'lastVisit' | 'totalOrders' | 'totalSpent' | 'isRegular'>): Customer {
    const newCustomer: Customer = {
      ...customerData,
      id: this.generateCustomerId(),
      createdAt: new Date().toISOString(),
      lastVisit: new Date().toISOString(),
      totalOrders: 0,
      totalSpent: 0,
      isRegular: false
    };

    this.customers.push(newCustomer);
    this.saveToStorage();
    return newCustomer;
  }

  // Update existing customer
  updateCustomer(customerId: string, updates: Partial<Customer>): boolean {
    const customerIndex = this.customers.findIndex(c => c.id === customerId);
    if (customerIndex === -1) return false;

    this.customers[customerIndex] = {
      ...this.customers[customerIndex],
      ...updates,
      lastVisit: new Date().toISOString()
    };

    this.saveToStorage();
    return true;
  }

  // Get all customers
  getAllCustomers(): Customer[] {
    return [...this.customers].sort((a, b) => 
      new Date(b.lastVisit).getTime() - new Date(a.lastVisit).getTime()
    );
  }

  // Search customers by name or phone
  searchCustomers(query: string): Customer[] {
    if (!query.trim()) return this.getAllCustomers();

    const searchTerm = query.toLowerCase().trim();
    return this.customers.filter(customer =>
      customer.name.toLowerCase().includes(searchTerm) ||
      (customer.phone && customer.phone.includes(searchTerm)) ||
      (customer.email && customer.email.toLowerCase().includes(searchTerm))
    ).sort((a, b) => 
      new Date(b.lastVisit).getTime() - new Date(a.lastVisit).getTime()
    );
  }

  // Get customer by ID
  getCustomerById(customerId: string): Customer | null {
    return this.customers.find(c => c.id === customerId) || null;
  }

  // Get customer by name (exact match)
  getCustomerByName(name: string): Customer | null {
    return this.customers.find(c => 
      c.name.toLowerCase() === name.toLowerCase().trim()
    ) || null;
  }

  // Add customer order
  addCustomerOrder(orderData: Omit<CustomerOrder, 'date'>): void {
    const customerOrder: CustomerOrder = {
      ...orderData,
      date: new Date().toISOString()
    };

    this.customerOrders.push(customerOrder);

    // Update customer statistics
    const customer = this.getCustomerById(orderData.customerId);
    if (customer) {
      customer.totalOrders += 1;
      customer.totalSpent += orderData.amount;
      customer.lastVisit = new Date().toISOString();
      
      // Mark as regular customer if they have 3+ orders
      if (customer.totalOrders >= 3) {
        customer.isRegular = true;
      }

      this.updateCustomer(customer.id, customer);
    }

    this.saveToStorage();
  }

  // Get customer orders
  getCustomerOrders(customerId: string): CustomerOrder[] {
    return this.customerOrders
      .filter(order => order.customerId === customerId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  // Get recent customers (last 10)
  getRecentCustomers(): Customer[] {
    return this.getAllCustomers().slice(0, 10);
  }

  // Get regular customers
  getRegularCustomers(): Customer[] {
    return this.customers.filter(c => c.isRegular)
      .sort((a, b) => b.totalOrders - a.totalOrders);
  }

  // Delete customer
  deleteCustomer(customerId: string): boolean {
    const customerIndex = this.customers.findIndex(c => c.id === customerId);
    if (customerIndex === -1) return false;

    this.customers.splice(customerIndex, 1);
    
    // Also remove customer orders
    this.customerOrders = this.customerOrders.filter(order => order.customerId !== customerId);
    
    this.saveToStorage();
    return true;
  }

  // Get customer statistics
  getCustomerStats(): {
    totalCustomers: number;
    regularCustomers: number;
    newCustomersThisMonth: number;
    averageOrderValue: number;
  } {
    const now = new Date();
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    const newCustomersThisMonth = this.customers.filter(c => 
      new Date(c.createdAt) >= monthStart
    ).length;

    const totalRevenue = this.customerOrders.reduce((sum, order) => sum + order.amount, 0);
    const averageOrderValue = this.customerOrders.length > 0 
      ? totalRevenue / this.customerOrders.length 
      : 0;

    return {
      totalCustomers: this.customers.length,
      regularCustomers: this.getRegularCustomers().length,
      newCustomersThisMonth,
      averageOrderValue: Math.round(averageOrderValue)
    };
  }

  // Export customer data
  exportCustomers(): string {
    const exportData = {
      customers: this.customers,
      orders: this.customerOrders,
      exportDate: new Date().toISOString()
    };
    return JSON.stringify(exportData, null, 2);
  }

  // Import customer data
  importCustomers(jsonData: string): boolean {
    try {
      const importData = JSON.parse(jsonData);
      if (importData.customers && Array.isArray(importData.customers)) {
        this.customers = importData.customers;
        if (importData.orders && Array.isArray(importData.orders)) {
          this.customerOrders = importData.orders;
        }
        this.saveToStorage();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error importing customer data:', error);
      return false;
    }
  }

  // Clear all customer data
  clearAllData(): void {
    this.customers = [];
    this.customerOrders = [];
    localStorage.removeItem('restaurant_customers');
    localStorage.removeItem('customer_orders');
  }
}

// Export singleton instance
export const customerManager = CustomerManager.getInstance();

// Utility functions
export const formatCustomerName = (customer: Customer): string => {
  return customer.name;
};

export const formatCustomerInfo = (customer: Customer): string => {
  const parts = [customer.name];
  if (customer.phone) parts.push(customer.phone);
  return parts.join(' • ');
};

export const getCustomerDisplayName = (customer: Customer): string => {
  let displayName = customer.name;
  if (customer.isRegular) {
    displayName += ' ⭐';
  }
  return displayName;
};
