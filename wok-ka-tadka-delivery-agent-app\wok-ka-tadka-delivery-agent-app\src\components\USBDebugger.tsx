import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { 
  Usb, 
  Info, 
  Play, 
  CheckCircle, 
  XCircle,
  AlertTriangle
} from 'lucide-react';

const USBDebugger: React.FC = () => {
  const [device, setDevice] = useState<USBDevice | null>(null);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const connectUSB = async () => {
    setIsConnecting(true);
    setLogs([]);
    setTestResult(null);
    
    try {
      addLog('Requesting USB device...');
      
      const selectedDevice = await navigator.usb.requestDevice({
        filters: [
          { vendorId: 0x0416 }, // Common thermal printer vendor ID
          { vendorId: 0x04b8 }, // Epson
          { vendorId: 0x0519 }, // Another common vendor
          { vendorId: 0x1a86 }, // QinHeng Electronics
          { vendorId: 0x067b }, // Prolific Technology Inc
          { vendorId: 0x0483 }, // STMicroelectronics
          { vendorId: 0x1fc9 }, // NXP Semiconductors
          { vendorId: 0x28e9 }, // GigaDevice Semiconductor Inc
          { vendorId: 0x0403 }, // Future Technology Devices International
          { classCode: 7 }, // Printer class
        ]
      });

      addLog(`Device selected: ${selectedDevice.productName || 'Unknown'}`);
      
      const info = {
        productName: selectedDevice.productName,
        manufacturerName: selectedDevice.manufacturerName,
        vendorId: `0x${selectedDevice.vendorId.toString(16).padStart(4, '0')}`,
        productId: `0x${selectedDevice.productId.toString(16).padStart(4, '0')}`,
        serialNumber: selectedDevice.serialNumber,
        usbVersionMajor: selectedDevice.usbVersionMajor,
        usbVersionMinor: selectedDevice.usbVersionMinor,
        deviceClass: selectedDevice.deviceClass,
        deviceSubclass: selectedDevice.deviceSubclass,
        deviceProtocol: selectedDevice.deviceProtocol,
        configurations: selectedDevice.configurations.length
      };
      
      setDeviceInfo(info);
      addLog(`Device info: ${JSON.stringify(info, null, 2)}`);

      addLog('Opening device...');
      await selectedDevice.open();
      addLog('Device opened successfully');

      // Log configuration details
      selectedDevice.configurations.forEach((config, configIndex) => {
        addLog(`Configuration ${configIndex}: ${config.configurationName || 'Unnamed'}`);
        addLog(`  Configuration Value: ${config.configurationValue}`);
        addLog(`  Interfaces: ${config.interfaces.length}`);
        
        config.interfaces.forEach((iface, ifaceIndex) => {
          addLog(`  Interface ${ifaceIndex}:`);
          addLog(`    Interface Number: ${iface.interfaceNumber}`);
          addLog(`    Alternates: ${iface.alternates.length}`);
          
          iface.alternates.forEach((alt, altIndex) => {
            addLog(`    Alternate ${altIndex}:`);
            addLog(`      Interface Class: ${alt.interfaceClass}`);
            addLog(`      Interface Subclass: ${alt.interfaceSubclass}`);
            addLog(`      Interface Protocol: ${alt.interfaceProtocol}`);
            addLog(`      Endpoints: ${alt.endpoints.length}`);
            
            alt.endpoints.forEach((endpoint, epIndex) => {
              addLog(`      Endpoint ${epIndex}:`);
              addLog(`        Endpoint Number: ${endpoint.endpointNumber}`);
              addLog(`        Direction: ${endpoint.direction}`);
              addLog(`        Type: ${endpoint.type}`);
              addLog(`        Packet Size: ${endpoint.packetSize}`);
            });
          });
        });
      });

      setDevice(selectedDevice);
      setTestResult('success');
      addLog('USB connection successful!');
      
    } catch (error) {
      addLog(`Error: ${error.message}`);
      setTestResult('error');
    } finally {
      setIsConnecting(false);
    }
  };

  const testPrint = async () => {
    if (!device) {
      addLog('No device connected');
      return;
    }

    try {
      addLog('Testing print...');
      
      // Try to select configuration and claim interface
      if (device.configurations.length > 0) {
        const config = device.configurations[0];
        addLog(`Selecting configuration ${config.configurationValue}...`);
        
        try {
          await device.selectConfiguration(config.configurationValue);
          addLog('Configuration selected');
        } catch (configError) {
          addLog(`Configuration selection failed: ${configError.message}`);
        }
        
        if (config.interfaces.length > 0) {
          const interfaceNumber = config.interfaces[0].interfaceNumber;
          addLog(`Claiming interface ${interfaceNumber}...`);
          
          try {
            await device.claimInterface(interfaceNumber);
            addLog('Interface claimed');
          } catch (interfaceError) {
            addLog(`Interface claim failed: ${interfaceError.message}`);
          }
        }
      }

      // Test data
      const testData = new TextEncoder().encode('TEST PRINT\nHello from USB!\n\n\n');
      
      // Try different endpoints
      const endpoints = [1, 2, 3];
      let success = false;
      
      for (const endpointNum of endpoints) {
        try {
          addLog(`Trying endpoint ${endpointNum}...`);
          const result = await device.transferOut(endpointNum, testData);
          addLog(`Endpoint ${endpointNum} result: ${result.status}, bytes: ${result.bytesWritten}`);
          
          if (result.status === 'ok') {
            addLog(`SUCCESS: Data sent to endpoint ${endpointNum}`);
            success = true;
            break;
          }
        } catch (endpointError) {
          addLog(`Endpoint ${endpointNum} failed: ${endpointError.message}`);
        }
      }
      
      if (success) {
        setTestResult('success');
        addLog('Test print completed successfully!');
      } else {
        setTestResult('error');
        addLog('All endpoints failed');
      }
      
    } catch (error) {
      addLog(`Test print error: ${error.message}`);
      setTestResult('error');
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Usb className="h-5 w-5" />
            USB Printer Debugger
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={connectUSB} 
              disabled={isConnecting}
              className="flex items-center gap-2"
            >
              <Usb className="h-4 w-4" />
              {isConnecting ? 'Connecting...' : 'Connect USB Device'}
            </Button>
            
            {device && (
              <Button 
                onClick={testPrint}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Test Print
              </Button>
            )}
          </div>

          {testResult && (
            <Alert className={testResult === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              {testResult === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <AlertDescription className={testResult === 'success' ? 'text-green-700' : 'text-red-700'}>
                {testResult === 'success' 
                  ? 'USB connection and test successful!' 
                  : 'USB connection or test failed. Check the logs below.'}
              </AlertDescription>
            </Alert>
          )}

          {deviceInfo && (
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-sm">Device Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div><strong>Product:</strong> {deviceInfo.productName || 'Unknown'}</div>
                  <div><strong>Manufacturer:</strong> {deviceInfo.manufacturerName || 'Unknown'}</div>
                  <div><strong>Vendor ID:</strong> {deviceInfo.vendorId}</div>
                  <div><strong>Product ID:</strong> {deviceInfo.productId}</div>
                  <div><strong>Serial:</strong> {deviceInfo.serialNumber || 'N/A'}</div>
                  <div><strong>USB Version:</strong> {deviceInfo.usbVersionMajor}.{deviceInfo.usbVersionMinor}</div>
                  <div><strong>Device Class:</strong> {deviceInfo.deviceClass}</div>
                  <div><strong>Configurations:</strong> {deviceInfo.configurations}</div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              <Info className="h-4 w-4" />
              Debug Logs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-xs max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertTriangle className="h-4 w-4 text-yellow-500" />
        <AlertDescription className="text-yellow-700">
          <strong>Debug Tool:</strong> This tool helps diagnose USB printer connection issues. 
          Use it to see detailed information about your printer and test basic communication.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default USBDebugger;
