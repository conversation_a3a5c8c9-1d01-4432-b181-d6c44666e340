import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Package, 
  Clock, 
  User, 
  Phone, 
  MapPin,
  Utensils,
  CheckCircle,
  AlertCircle,
  Printer,
  Edit
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import PrintService from "@/services/printService";
import CustomerSelectionDialog from "@/components/CustomerSelectionDialog";
import PaymentMethodDialog, { PaymentMethod } from "@/components/PaymentMethodDialog";
import { Customer, customerManager } from "@/utils/customerStorage";
import { useToast } from "@/hooks/use-toast";

const OrderDetails = () => {
  const navigate = useNavigate();
  const { orderId } = useParams();
  const { toast } = useToast();

  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showPaymentMethodDialog, setShowPaymentMethodDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('cash');

  // Decode the order ID from URL parameter
  const decodedOrderId = orderId ? decodeURIComponent(orderId) : "#ORD001";

  // Mock order data - in real app, this would come from API/storage
  const orderData = {
    id: decodedOrderId,
    type: "dine-in",
    table: "Table 3",
    customer: "Walk-in Customer",
    phone: "+91 98765 43210",
    items: [
      { name: "Chicken Biryani", quantity: 2, price: 300, total: 600 },
      { name: "Paneer Butter Masala", quantity: 1, price: 250, total: 250 },
      { name: "Garlic Naan", quantity: 3, price: 60, total: 180 }
    ],
    totalAmount: 1030,
    status: "preparing",
    orderTime: "12:30 PM",
    estimatedTime: "15 mins",
    waiter: "Raj Kumar",
    specialInstructions: "Less spicy",
    createdAt: new Date().toLocaleDateString(),
    paymentStatus: "pending"
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "preparing":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "ready":
        return "bg-green-100 text-green-800 border-green-200";
      case "completed":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "preparing":
        return <Clock className="h-3 w-3" />;
      case "ready":
        return <CheckCircle className="h-3 w-3" />;
      case "completed":
        return <CheckCircle className="h-3 w-3" />;
      case "cancelled":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const handleStatusUpdate = (newStatus: string) => {
    // Mock status update
    alert(`Order ${orderId} status updated to ${newStatus}`);
  };

  // Generate HTML content for KOT printing
  const generateKOTHTML = (order: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>KOT - ${order.id}</title>
      <style>
        body {
          font-family: 'Courier New', monospace;
          margin: 0;
          padding: 20px;
          font-size: 12px;
          line-height: 1.4;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #000;
          padding-bottom: 10px;
          margin-bottom: 15px;
        }
        .restaurant-name {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .kot-title {
          font-size: 16px;
          font-weight: bold;
          margin: 10px 0;
        }
        .order-info {
          margin-bottom: 15px;
        }
        .order-info div {
          margin-bottom: 3px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 15px;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 5px;
          text-align: left;
        }
        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        .footer {
          border-top: 1px solid #000;
          padding-top: 10px;
          text-align: center;
          font-size: 10px;
        }
        .special-instructions {
          background-color: #fffacd;
          border: 1px solid #ddd;
          padding: 8px;
          margin: 10px 0;
          border-radius: 3px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="restaurant-name">WOK KA TADKA</div>
        <div class="kot-title">KITCHEN ORDER TICKET (KOT)</div>
      </div>

      <div class="order-info">
        <div><strong>Order ID:</strong> ${order.id}</div>
        <div><strong>Table:</strong> ${order.table}</div>
        <div><strong>Order Type:</strong> ${order.type}</div>
        <div><strong>Waiter:</strong> ${order.waiter}</div>
        <div><strong>Order Time:</strong> ${order.orderTime}</div>
        <div><strong>Date:</strong> ${currentDate}</div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Qty</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
          ${order.items.map((item: any) => `
            <tr>
              <td>${item.name}</td>
              <td>${item.quantity}</td>
              <td>-</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      ${order.specialInstructions ? `
        <div class="special-instructions">
          <strong>Special Instructions:</strong><br>
          ${order.specialInstructions}
        </div>
      ` : ''}

      <div class="footer">
        <p>Generated on ${currentDate} at ${currentTime}</p>
        <p>Please prepare items as per order specifications</p>
      </div>
    </body>
    </html>`;
  };

  // Generate HTML content for order receipt printing
  const generateOrderReceiptHTML = (order: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Order Receipt - ${order.id}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          font-size: 12px;
          line-height: 1.4;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #000;
          padding-bottom: 10px;
          margin-bottom: 15px;
        }
        .restaurant-name {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .receipt-title {
          font-size: 16px;
          font-weight: bold;
          margin: 10px 0;
        }
        .order-info {
          margin-bottom: 15px;
        }
        .order-info div {
          margin-bottom: 3px;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 15px;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #000;
          padding: 8px;
          text-align: left;
        }
        .items-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        .items-table .text-right {
          text-align: right;
        }
        .total-row {
          font-weight: bold;
          background-color: #f9f9f9;
        }
        .footer {
          border-top: 1px solid #000;
          padding-top: 10px;
          text-align: center;
          font-size: 10px;
        }
        .special-instructions {
          background-color: #fffacd;
          border: 1px solid #ddd;
          padding: 8px;
          margin: 10px 0;
          border-radius: 3px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="restaurant-name">WOK KA TADKA</div>
        <div class="receipt-title">ORDER RECEIPT</div>
      </div>

      <div class="order-info">
        <div><strong>Order ID:</strong> ${order.id}</div>
        <div><strong>Customer:</strong> ${order.customer}</div>
        <div><strong>Phone:</strong> ${order.phone}</div>
        <div><strong>Table:</strong> ${order.table}</div>
        <div><strong>Order Type:</strong> ${order.type}</div>
        <div><strong>Waiter:</strong> ${order.waiter}</div>
        <div><strong>Order Time:</strong> ${order.orderTime}</div>
        <div><strong>Status:</strong> ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}</div>
        <div><strong>Date:</strong> ${currentDate}</div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th class="text-right">Price</th>
            <th class="text-right">Qty</th>
            <th class="text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          ${order.items.map((item: any) => `
            <tr>
              <td>${item.name}</td>
              <td class="text-right">₹${item.price.toFixed(2)}</td>
              <td class="text-right">${item.quantity}</td>
              <td class="text-right">₹${item.total.toFixed(2)}</td>
            </tr>
          `).join('')}
          <tr class="total-row">
            <td colspan="3"><strong>Grand Total</strong></td>
            <td class="text-right"><strong>₹${order.totalAmount.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>

      ${order.specialInstructions ? `
        <div class="special-instructions">
          <strong>Special Instructions:</strong><br>
          ${order.specialInstructions}
        </div>
      ` : ''}

      <div class="footer">
        <p>Thank you for dining with us!</p>
        <p>Visit us again soon!</p>
        <p>Generated on ${currentDate} at ${currentTime}</p>
      </div>
    </body>
    </html>`;
  };

  const handlePrintKOT = async () => {
    try {
      // Convert orderData to KOT format for thermal printing
      const kotData = {
        id: orderData.id,
        kotNumber: orderData.id,
        tableId: orderData.tableNumber || '1',
        items: orderData.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          specialInstructions: item.specialInstructions || ''
        })),
        createdAt: new Date().toISOString(),
        status: 'active'
      };

      // Print the KOT using unified print service
      const success = await PrintService.printKOT(kotData);

      if (!success) {
        throw new Error("Print service failed");
      }
    } catch (error) {
      console.error('Failed to print KOT:', error);
      // Fallback to browser printing if thermal printing fails
      const kotHTML = generateKOTHTML(orderData);
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(kotHTML);
        printWindow.document.close();
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
          }, 500);
        };
      }
    }
  };

  const handlePrintReceipt = () => {
    setShowCustomerDialog(true);
  };

  const handleCustomerSelection = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setShowCustomerDialog(false);
    setShowPaymentMethodDialog(true);
  };

  const handlePaymentMethodSelection = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setShowPaymentMethodDialog(false);
    printReceipt(selectedCustomer, paymentMethod);
  };

  const printReceipt = async (customer: Customer | null, paymentMethod: PaymentMethod) => {
    try {
      // Convert orderData to KOT format for unified print service
      const kotData = {
        kotNumber: orderData.id,
        tableId: orderData.tableNumber || 'N/A',
        items: orderData.items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          specialInstructions: item.specialInstructions || ''
        })),
        totalAmount: orderData.totalAmount,
        createdAt: new Date().toISOString(),
        status: 'active'
      };

      // Use unified print service for bill printing
      const success = await PrintService.printBill(kotData, customer, {
        includeGST: true,
        paymentMethod
      });

      if (success) {
        toast({
          title: "Receipt Printed Successfully!",
          description: customer
            ? `Receipt printed for ${customer.name}`
            : "Receipt printed successfully",
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      console.error('Print receipt error:', error);
      toast({
        title: "Print Error",
        description: "Failed to print receipt. Please try again.",
        variant: "destructive"
      });

      // Fallback to browser printing
      const receiptHTML = generateOrderReceiptHTML(orderData);
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(receiptHTML);
        printWindow.document.close();
        printWindow.onload = () => {
          setTimeout(() => {
            printWindow.print();
            printWindow.close();
          }, 500);
        };
      }
    }
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b apk-header-fixed">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center gap-2 sm:gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/admin/orders")}
              className="flex items-center gap-1 sm:gap-2 shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back</span>
            </Button>
            <Logo className="h-6 sm:h-8 shrink-0" />
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Order Details</h1>
              <p className="text-xs sm:text-sm text-gray-600 truncate">{orderData.id}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 apk-content-with-header space-y-4 sm:space-y-6">
        {/* Order Status Card */}
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <Package className="h-6 w-6 text-blue-600" />
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{orderData.id}</h2>
                  <p className="text-sm text-gray-600">{orderData.type} • {orderData.table}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Badge className={`${getStatusColor(orderData.status)} flex items-center gap-1`}>
                  {getStatusIcon(orderData.status)}
                  {orderData.status.charAt(0).toUpperCase() + orderData.status.slice(1)}
                </Badge>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrintKOT}
                    className="flex items-center gap-1"
                  >
                    <Printer className="h-4 w-4" />
                    Print KOT
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrintReceipt}
                    className="flex items-center gap-1"
                  >
                    <Printer className="h-4 w-4" />
                    Print Receipt
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Order Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Order Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Customer</p>
                  <p className="text-base font-semibold text-gray-900">{orderData.customer}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Phone</p>
                  <p className="text-base font-semibold text-gray-900">{orderData.phone}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Order Time</p>
                  <p className="text-base font-semibold text-gray-900">{orderData.orderTime}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Estimated Time</p>
                  <p className="text-base font-semibold text-gray-900">{orderData.estimatedTime}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Waiter</p>
                  <p className="text-base font-semibold text-gray-900">{orderData.waiter}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Payment Status</p>
                  <Badge variant="outline" className="text-orange-600 border-orange-200">
                    {orderData.paymentStatus}
                  </Badge>
                </div>
              </div>
              
              {orderData.specialInstructions && (
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Special Instructions</p>
                  <p className="text-sm text-gray-700 bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                    {orderData.specialInstructions}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Utensils className="h-5 w-5" />
                Order Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {orderData.items.map((item, index) => (
                  <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 truncate">{item.name}</h4>
                      <p className="text-sm text-gray-600">₹{item.price} × {item.quantity}</p>
                    </div>
                    <div className="text-right shrink-0">
                      <p className="font-semibold text-gray-900">₹{item.total}</p>
                    </div>
                  </div>
                ))}
                
                <div className="pt-3 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-bold text-gray-900">Total Amount</p>
                    <p className="text-lg font-bold text-blue-600">₹{orderData.totalAmount}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        {orderData.status !== "completed" && orderData.status !== "cancelled" && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row gap-3">
                {orderData.status === "preparing" && (
                  <Button
                    onClick={() => handleStatusUpdate("ready")}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark as Ready
                  </Button>
                )}
                
                {orderData.status === "ready" && (
                  <Button
                    onClick={() => handleStatusUpdate("completed")}
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark as Completed
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  onClick={() => handleStatusUpdate("cancelled")}
                  className="flex-1 text-red-600 border-red-600 hover:bg-red-50"
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Cancel Order
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Customer Selection Dialog */}
      <CustomerSelectionDialog
        isOpen={showCustomerDialog}
        onClose={() => setShowCustomerDialog(false)}
        onCustomerSelect={handleCustomerSelection}
        title="Select Customer for Receipt"
        description="Choose a customer for this receipt or skip to print without customer details"
      />

      {/* Payment Method Selection Dialog */}
      <PaymentMethodDialog
        open={showPaymentMethodDialog}
        onClose={() => setShowPaymentMethodDialog(false)}
        onSelect={handlePaymentMethodSelection}
        title="Select Payment Method"
      />
    </div>
  );
};

export default OrderDetails;
