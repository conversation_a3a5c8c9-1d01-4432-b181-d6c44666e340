// Inventory Storage Utility for managing restaurant inventory and waste tracking

export interface InventoryItem {
  id: string;
  name: string;
  category: 'vegetables' | 'meat' | 'dairy' | 'grains' | 'spices' | 'beverages' | 'other';
  currentStock: number;
  unit: 'kg' | 'grams' | 'liters' | 'ml' | 'pieces' | 'packets' | 'bottles';
  minStockLevel: number; // Minimum stock before reorder alert
  maxStockLevel: number; // Maximum recommended stock
  costPerUnit: number; // Cost in rupees
  supplier?: string;
  expiryDate?: string; // ISO string for perishable items
  purchaseDate: string; // ISO string
  lastUpdated: string; // ISO string
  notes?: string;
}

export interface StockTransaction {
  id: string;
  itemId: string;
  itemName: string;
  type: 'purchase' | 'usage' | 'waste' | 'adjustment';
  quantity: number; // Positive for purchase/adjustment in, negative for usage/waste/adjustment out
  reason: string;
  date: string; // ISO string
  performedBy: string; // Staff ID or name
  cost?: number; // Total cost for purchases
  notes?: string;
}

export interface WasteRecord {
  id: string;
  itemId: string;
  itemName: string;
  quantity: number;
  reason: 'expired' | 'damaged' | 'overcooked' | 'spoiled' | 'other';
  date: string; // ISO string
  reportedBy: string; // Staff ID or name
  estimatedCost: number;
  notes?: string;
}

export interface InventoryStats {
  totalItems: number;
  lowStockItems: number;
  expiringItems: number; // Items expiring in next 3 days
  totalValue: number; // Total inventory value
  monthlyWaste: number; // Waste value this month
  wastePercentage: number; // Waste as percentage of total purchases
}

const INVENTORY_STORAGE_KEY = 'wok_ka_tadka_inventory';
const TRANSACTIONS_STORAGE_KEY = 'wok_ka_tadka_inventory_transactions';
const WASTE_STORAGE_KEY = 'wok_ka_tadka_inventory_waste';

// Get all inventory items from localStorage
export const getAllInventoryItems = (): InventoryItem[] => {
  try {
    const stored = localStorage.getItem(INVENTORY_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading inventory items:', error);
    return [];
  }
};

// Save inventory items to localStorage
export const saveInventoryItems = (items: InventoryItem[]): void => {
  try {
    localStorage.setItem(INVENTORY_STORAGE_KEY, JSON.stringify(items));
  } catch (error) {
    console.error('Error saving inventory items:', error);
  }
};

// Get all stock transactions from localStorage
export const getAllTransactions = (): StockTransaction[] => {
  try {
    const stored = localStorage.getItem(TRANSACTIONS_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading transactions:', error);
    return [];
  }
};

// Save stock transactions to localStorage
export const saveTransactions = (transactions: StockTransaction[]): void => {
  try {
    localStorage.setItem(TRANSACTIONS_STORAGE_KEY, JSON.stringify(transactions));
  } catch (error) {
    console.error('Error saving transactions:', error);
  }
};

// Get all waste records from localStorage
export const getAllWasteRecords = (): WasteRecord[] => {
  try {
    const stored = localStorage.getItem(WASTE_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading waste records:', error);
    return [];
  }
};

// Save waste records to localStorage
export const saveWasteRecords = (records: WasteRecord[]): void => {
  try {
    localStorage.setItem(WASTE_STORAGE_KEY, JSON.stringify(records));
  } catch (error) {
    console.error('Error saving waste records:', error);
  }
};

// Generate unique ID
const generateId = (): string => {
  return `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Add new inventory item
export const addInventoryItem = (item: Omit<InventoryItem, 'id' | 'lastUpdated'>): InventoryItem => {
  const items = getAllInventoryItems();
  const newItem: InventoryItem = {
    ...item,
    id: generateId(),
    lastUpdated: new Date().toISOString()
  };
  
  items.push(newItem);
  saveInventoryItems(items);
  
  // Record initial stock as a purchase transaction
  if (newItem.currentStock > 0) {
    recordTransaction({
      itemId: newItem.id,
      itemName: newItem.name,
      type: 'purchase',
      quantity: newItem.currentStock,
      reason: 'Initial stock entry',
      performedBy: 'admin', // TODO: Get from auth
      cost: newItem.currentStock * newItem.costPerUnit
    });
  }
  
  return newItem;
};

// Update inventory item
export const updateInventoryItem = (itemId: string, updates: Partial<InventoryItem>): InventoryItem | null => {
  const items = getAllInventoryItems();
  const itemIndex = items.findIndex(item => item.id === itemId);
  
  if (itemIndex === -1) {
    throw new Error('Inventory item not found');
  }
  
  const updatedItem = {
    ...items[itemIndex],
    ...updates,
    lastUpdated: new Date().toISOString()
  };
  
  items[itemIndex] = updatedItem;
  saveInventoryItems(items);
  
  return updatedItem;
};

// Record stock transaction (purchase, usage, waste, adjustment)
export const recordTransaction = (transaction: Omit<StockTransaction, 'id' | 'date'>): StockTransaction => {
  const transactions = getAllTransactions();
  const newTransaction: StockTransaction = {
    ...transaction,
    id: generateId(),
    date: new Date().toISOString()
  };
  
  transactions.push(newTransaction);
  saveTransactions(transactions);
  
  // Update item stock based on transaction
  const items = getAllInventoryItems();
  const itemIndex = items.findIndex(item => item.id === transaction.itemId);
  
  if (itemIndex !== -1) {
    const item = items[itemIndex];
    
    if (transaction.type === 'purchase' || transaction.type === 'adjustment') {
      item.currentStock += Math.abs(transaction.quantity);
    } else if (transaction.type === 'usage' || transaction.type === 'waste') {
      item.currentStock = Math.max(0, item.currentStock - Math.abs(transaction.quantity));
    }
    
    item.lastUpdated = new Date().toISOString();
    items[itemIndex] = item;
    saveInventoryItems(items);
  }
  
  return newTransaction;
};

// Record waste
export const recordWaste = (waste: Omit<WasteRecord, 'id' | 'date'>): WasteRecord => {
  const wasteRecords = getAllWasteRecords();
  const newWaste: WasteRecord = {
    ...waste,
    id: generateId(),
    date: new Date().toISOString()
  };
  
  wasteRecords.push(newWaste);
  saveWasteRecords(wasteRecords);
  
  // Also record as a transaction
  recordTransaction({
    itemId: waste.itemId,
    itemName: waste.itemName,
    type: 'waste',
    quantity: waste.quantity,
    reason: `Waste: ${waste.reason}`,
    performedBy: waste.reportedBy,
    notes: waste.notes
  });
  
  return newWaste;
};

// Get inventory statistics
export const getInventoryStats = (): InventoryStats => {
  const items = getAllInventoryItems();
  const wasteRecords = getAllWasteRecords();
  const transactions = getAllTransactions();
  
  const now = new Date();
  const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  
  const lowStockItems = items.filter(item => item.currentStock <= item.minStockLevel).length;
  const expiringItems = items.filter(item => 
    item.expiryDate && new Date(item.expiryDate) <= threeDaysFromNow
  ).length;
  
  const totalValue = items.reduce((sum, item) => sum + (item.currentStock * item.costPerUnit), 0);
  
  const monthlyWaste = wasteRecords
    .filter(waste => new Date(waste.date) >= monthStart)
    .reduce((sum, waste) => sum + waste.estimatedCost, 0);
  
  const monthlyPurchases = transactions
    .filter(t => t.type === 'purchase' && new Date(t.date) >= monthStart)
    .reduce((sum, t) => sum + (t.cost || 0), 0);
  
  const wastePercentage = monthlyPurchases > 0 ? (monthlyWaste / monthlyPurchases) * 100 : 0;
  
  return {
    totalItems: items.length,
    lowStockItems,
    expiringItems,
    totalValue: Math.round(totalValue * 100) / 100,
    monthlyWaste: Math.round(monthlyWaste * 100) / 100,
    wastePercentage: Math.round(wastePercentage * 100) / 100
  };
};

// Get items by category
export const getItemsByCategory = (category: InventoryItem['category']): InventoryItem[] => {
  const items = getAllInventoryItems();
  return items.filter(item => item.category === category);
};

// Get low stock items
export const getLowStockItems = (): InventoryItem[] => {
  const items = getAllInventoryItems();
  return items.filter(item => item.currentStock <= item.minStockLevel);
};

// Get expiring items (within next 3 days)
export const getExpiringItems = (): InventoryItem[] => {
  const items = getAllInventoryItems();
  const threeDaysFromNow = new Date();
  threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
  
  return items.filter(item => 
    item.expiryDate && new Date(item.expiryDate) <= threeDaysFromNow
  );
};

// Search items by name
export const searchItems = (query: string): InventoryItem[] => {
  const items = getAllInventoryItems();
  const lowercaseQuery = query.toLowerCase();
  
  return items.filter(item => 
    item.name.toLowerCase().includes(lowercaseQuery) ||
    item.category.toLowerCase().includes(lowercaseQuery) ||
    (item.supplier && item.supplier.toLowerCase().includes(lowercaseQuery))
  );
};

// Format currency for display
export const formatCurrency = (amount: number): string => {
  return `₹${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

// Format date for display
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Get transactions for an item
export const getItemTransactions = (itemId: string): StockTransaction[] => {
  const transactions = getAllTransactions();
  return transactions.filter(t => t.itemId === itemId).sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
};

// Get waste records for an item
export const getItemWasteRecords = (itemId: string): WasteRecord[] => {
  const wasteRecords = getAllWasteRecords();
  return wasteRecords.filter(w => w.itemId === itemId).sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
};
