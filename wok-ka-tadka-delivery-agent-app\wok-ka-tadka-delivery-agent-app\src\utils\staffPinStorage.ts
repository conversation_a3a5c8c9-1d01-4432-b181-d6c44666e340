// Staff PIN Storage Utility for managing staff authentication PINs

import type { WorkingHours } from './workingHoursStorage';

export interface StaffPin {
  id: string;
  name: string;
  phone: string;
  pin: string;
  role: 'waiter' | 'delivery' | 'kitchen' | 'manager';
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
  createdBy: string; // Admin who created the PIN
  workingHours?: WorkingHours; // Optional per-staff working hours
}

const STORAGE_KEY = 'wok_ka_tadka_staff_pins';

// Normalize phone number (remove spaces, dashes, +91, etc.)
const normalizePhone = (phone: string): string => {
  return phone.replace(/[\s\-\+]/g, '').replace(/^91/, '');
};

// Get all staff PINs from localStorage
export const getAllStaffPins = (): StaffPin[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error loading staff PINs:', error);
    return [];
  }
};

// Save staff PINs to localStorage
export const saveStaffPins = (pins: StaffPin[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(pins));
  } catch (error) {
    console.error('Error saving staff PINs:', error);
  }
};

// Generate a random 4-digit PIN
export const generatePin = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

// Check if PIN already exists
export const isPinExists = (pin: string): boolean => {
  const pins = getAllStaffPins();
  return pins.some(staffPin => staffPin.pin === pin && staffPin.isActive);
};

// Generate unique PIN
export const generateUniquePin = (): string => {
  let pin = generatePin();
  while (isPinExists(pin)) {
    pin = generatePin();
  }
  return pin;
};

// Create new staff PIN
export const createStaffPin = (staffData: Omit<StaffPin, 'id' | 'pin' | 'createdAt' | 'isActive'> & { workingHours?: WorkingHours }): StaffPin => {
  const pins = getAllStaffPins();
  const newPin: StaffPin = {
    ...staffData,
    phone: normalizePhone(staffData.phone), // Normalize phone number
    id: Date.now().toString(),
    pin: generateUniquePin(),
    isActive: true,
    createdAt: new Date().toISOString(),
    ...(staffData.workingHours ? { workingHours: staffData.workingHours } : {})
  };

  pins.push(newPin);
  saveStaffPins(pins);
  return newPin;
};

// Update staff PIN
export const updateStaffPin = (id: string, updates: Partial<StaffPin>): boolean => {
  const pins = getAllStaffPins();
  const index = pins.findIndex(pin => pin.id === id);
  
  if (index === -1) return false;
  
  pins[index] = { ...pins[index], ...updates };
  saveStaffPins(pins);
  return true;
};

// Delete staff PIN
export const deleteStaffPin = (id: string): boolean => {
  const pins = getAllStaffPins();
  const filteredPins = pins.filter(pin => pin.id !== id);
  
  if (filteredPins.length === pins.length) return false;
  
  saveStaffPins(filteredPins);
  return true;
};

// Deactivate staff PIN
export const deactivateStaffPin = (id: string): boolean => {
  return updateStaffPin(id, { isActive: false });
};

// Activate staff PIN
export const activateStaffPin = (id: string): boolean => {
  return updateStaffPin(id, { isActive: true });
};

// Validate PIN for login
export const validateStaffPin = (phone: string, pin: string): StaffPin | null => {
  // Normalize input phone number
  const normalizedInputPhone = normalizePhone(phone);

  const pins = getAllStaffPins();

  // Find exact match (with normalized phone comparison)
  const staffPin = pins.find(p =>
    normalizePhone(p.phone) === normalizedInputPhone &&
    p.pin === pin &&
    p.isActive
  );

  if (staffPin) {
    // Update last used timestamp
    updateStaffPin(staffPin.id, { lastUsed: new Date().toISOString() });
    return staffPin;
  }

  return null;
};

// Get staff PIN by phone
export const getStaffByPhone = (phone: string): StaffPin | null => {
  const pins = getAllStaffPins();
  return pins.find(pin => pin.phone === phone && pin.isActive) || null;
};

// Get staff PIN by ID
export const getStaffById = (id: string): StaffPin | null => {
  const pins = getAllStaffPins();
  return pins.find(pin => pin.id === id) || null;
};

// Reset staff PIN
export const resetStaffPin = (id: string): string | null => {
  const newPin = generateUniquePin();
  const success = updateStaffPin(id, { pin: newPin });
  return success ? newPin : null;
};

// Get active staff count by role
export const getActiveStaffCountByRole = (role: StaffPin['role']): number => {
  const pins = getAllStaffPins();
  return pins.filter(pin => pin.role === role && pin.isActive).length;
};

// Initialize with default admin PIN if no PINs exist
export const initializeDefaultPins = (): void => {
  const pins = getAllStaffPins();
  if (pins.length === 0) {
    // Create default admin PIN
    const defaultAdmin: StaffPin = {
      id: 'admin-default',
      name: 'Admin',
      phone: '1234567890',
      pin: '0000',
      role: 'manager',
      isActive: true,
      createdAt: new Date().toISOString(),
      createdBy: 'system'
    };
    
    saveStaffPins([defaultAdmin]);
  }
};

// Search staff PINs
export const searchStaffPins = (query: string): StaffPin[] => {
  const pins = getAllStaffPins();
  const lowercaseQuery = query.toLowerCase();
  
  return pins.filter(pin => 
    pin.name.toLowerCase().includes(lowercaseQuery) ||
    pin.phone.includes(query) ||
    pin.role.toLowerCase().includes(lowercaseQuery)
  );
};

// Get staff statistics
export const getStaffStatistics = () => {
  const pins = getAllStaffPins();
  const activePins = pins.filter(pin => pin.isActive);
  
  return {
    total: pins.length,
    active: activePins.length,
    inactive: pins.length - activePins.length,
    byRole: {
      waiter: activePins.filter(pin => pin.role === 'waiter').length,
      delivery: activePins.filter(pin => pin.role === 'delivery').length,
      kitchen: activePins.filter(pin => pin.role === 'kitchen').length,
      manager: activePins.filter(pin => pin.role === 'manager').length
    }
  };
};
