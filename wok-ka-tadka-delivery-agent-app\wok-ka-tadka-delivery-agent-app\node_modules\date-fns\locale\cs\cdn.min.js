var D=function(H){return D=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},D(H)},W=function(H,G){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);G&&(Z=Z.filter(function(K){return Object.getOwnPropertyDescriptor(H,K).enumerable})),X.push.apply(X,Z)}return X},z=function(H){for(var G=1;G<arguments.length;G++){var X=arguments[G]!=null?arguments[G]:{};G%2?W(Object(X),!0).forEach(function(Z){C1(H,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):W(Object(X)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(X,Z))})}return H},C1=function(H,G,X){if(G=U1(G),G in H)Object.defineProperty(H,G,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[G]=X;return H},U1=function(H){var G=G1(H,"string");return D(G)=="symbol"?G:String(G)},G1=function(H,G){if(D(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(H,G||"default");if(D(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,X=function B(U,E){for(var C in E)G(U,C,{get:E[C],enumerable:!0,configurable:!0,set:function Y(J){return E[C]=function(){return J}}})},Z={lessThanXSeconds:{one:{regular:"m\xE9n\u011B ne\u017E 1 sekunda",past:"p\u0159ed m\xE9n\u011B ne\u017E 1 sekundou",future:"za m\xE9n\u011B ne\u017E 1 sekundu"},few:{regular:"m\xE9n\u011B ne\u017E {{count}} sekundy",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} sekundami",future:"za m\xE9n\u011B ne\u017E {{count}} sekundy"},many:{regular:"m\xE9n\u011B ne\u017E {{count}} sekund",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} sekundami",future:"za m\xE9n\u011B ne\u017E {{count}} sekund"}},xSeconds:{one:{regular:"1 sekunda",past:"p\u0159ed 1 sekundou",future:"za 1 sekundu"},few:{regular:"{{count}} sekundy",past:"p\u0159ed {{count}} sekundami",future:"za {{count}} sekundy"},many:{regular:"{{count}} sekund",past:"p\u0159ed {{count}} sekundami",future:"za {{count}} sekund"}},halfAMinute:{type:"other",other:{regular:"p\u016Fl minuty",past:"p\u0159ed p\u016Fl minutou",future:"za p\u016Fl minuty"}},lessThanXMinutes:{one:{regular:"m\xE9n\u011B ne\u017E 1 minuta",past:"p\u0159ed m\xE9n\u011B ne\u017E 1 minutou",future:"za m\xE9n\u011B ne\u017E 1 minutu"},few:{regular:"m\xE9n\u011B ne\u017E {{count}} minuty",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} minutami",future:"za m\xE9n\u011B ne\u017E {{count}} minuty"},many:{regular:"m\xE9n\u011B ne\u017E {{count}} minut",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} minutami",future:"za m\xE9n\u011B ne\u017E {{count}} minut"}},xMinutes:{one:{regular:"1 minuta",past:"p\u0159ed 1 minutou",future:"za 1 minutu"},few:{regular:"{{count}} minuty",past:"p\u0159ed {{count}} minutami",future:"za {{count}} minuty"},many:{regular:"{{count}} minut",past:"p\u0159ed {{count}} minutami",future:"za {{count}} minut"}},aboutXHours:{one:{regular:"p\u0159ibli\u017En\u011B 1 hodina",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 hodinou",future:"p\u0159ibli\u017En\u011B za 1 hodinu"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} hodiny",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} hodinami",future:"p\u0159ibli\u017En\u011B za {{count}} hodiny"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} hodin",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} hodinami",future:"p\u0159ibli\u017En\u011B za {{count}} hodin"}},xHours:{one:{regular:"1 hodina",past:"p\u0159ed 1 hodinou",future:"za 1 hodinu"},few:{regular:"{{count}} hodiny",past:"p\u0159ed {{count}} hodinami",future:"za {{count}} hodiny"},many:{regular:"{{count}} hodin",past:"p\u0159ed {{count}} hodinami",future:"za {{count}} hodin"}},xDays:{one:{regular:"1 den",past:"p\u0159ed 1 dnem",future:"za 1 den"},few:{regular:"{{count}} dny",past:"p\u0159ed {{count}} dny",future:"za {{count}} dny"},many:{regular:"{{count}} dn\xED",past:"p\u0159ed {{count}} dny",future:"za {{count}} dn\xED"}},aboutXWeeks:{one:{regular:"p\u0159ibli\u017En\u011B 1 t\xFDden",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 t\xFDdnem",future:"p\u0159ibli\u017En\u011B za 1 t\xFDden"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} t\xFDdny",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} t\xFDdny",future:"p\u0159ibli\u017En\u011B za {{count}} t\xFDdny"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} t\xFDdn\u016F",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} t\xFDdny",future:"p\u0159ibli\u017En\u011B za {{count}} t\xFDdn\u016F"}},xWeeks:{one:{regular:"1 t\xFDden",past:"p\u0159ed 1 t\xFDdnem",future:"za 1 t\xFDden"},few:{regular:"{{count}} t\xFDdny",past:"p\u0159ed {{count}} t\xFDdny",future:"za {{count}} t\xFDdny"},many:{regular:"{{count}} t\xFDdn\u016F",past:"p\u0159ed {{count}} t\xFDdny",future:"za {{count}} t\xFDdn\u016F"}},aboutXMonths:{one:{regular:"p\u0159ibli\u017En\u011B 1 m\u011Bs\xEDc",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 m\u011Bs\xEDcem",future:"p\u0159ibli\u017En\u011B za 1 m\u011Bs\xEDc"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} m\u011Bs\xEDce",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} m\u011Bs\xEDci",future:"p\u0159ibli\u017En\u011B za {{count}} m\u011Bs\xEDce"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} m\u011Bs\xEDc\u016F",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} m\u011Bs\xEDci",future:"p\u0159ibli\u017En\u011B za {{count}} m\u011Bs\xEDc\u016F"}},xMonths:{one:{regular:"1 m\u011Bs\xEDc",past:"p\u0159ed 1 m\u011Bs\xEDcem",future:"za 1 m\u011Bs\xEDc"},few:{regular:"{{count}} m\u011Bs\xEDce",past:"p\u0159ed {{count}} m\u011Bs\xEDci",future:"za {{count}} m\u011Bs\xEDce"},many:{regular:"{{count}} m\u011Bs\xEDc\u016F",past:"p\u0159ed {{count}} m\u011Bs\xEDci",future:"za {{count}} m\u011Bs\xEDc\u016F"}},aboutXYears:{one:{regular:"p\u0159ibli\u017En\u011B 1 rok",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 rokem",future:"p\u0159ibli\u017En\u011B za 1 rok"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} roky",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} roky",future:"p\u0159ibli\u017En\u011B za {{count}} roky"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} rok\u016F",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} roky",future:"p\u0159ibli\u017En\u011B za {{count}} rok\u016F"}},xYears:{one:{regular:"1 rok",past:"p\u0159ed 1 rokem",future:"za 1 rok"},few:{regular:"{{count}} roky",past:"p\u0159ed {{count}} roky",future:"za {{count}} roky"},many:{regular:"{{count}} rok\u016F",past:"p\u0159ed {{count}} roky",future:"za {{count}} rok\u016F"}},overXYears:{one:{regular:"v\xEDce ne\u017E 1 rok",past:"p\u0159ed v\xEDce ne\u017E 1 rokem",future:"za v\xEDce ne\u017E 1 rok"},few:{regular:"v\xEDce ne\u017E {{count}} roky",past:"p\u0159ed v\xEDce ne\u017E {{count}} roky",future:"za v\xEDce ne\u017E {{count}} roky"},many:{regular:"v\xEDce ne\u017E {{count}} rok\u016F",past:"p\u0159ed v\xEDce ne\u017E {{count}} roky",future:"za v\xEDce ne\u017E {{count}} rok\u016F"}},almostXYears:{one:{regular:"skoro 1 rok",past:"skoro p\u0159ed 1 rokem",future:"skoro za 1 rok"},few:{regular:"skoro {{count}} roky",past:"skoro p\u0159ed {{count}} roky",future:"skoro za {{count}} roky"},many:{regular:"skoro {{count}} rok\u016F",past:"skoro p\u0159ed {{count}} roky",future:"skoro za {{count}} rok\u016F"}}},K=function B(U,E,C){var Y,J=Z[U];if(J.type==="other")Y=J.other;else if(E===1)Y=J.one;else if(E>1&&E<5)Y=J.few;else Y=J.many;var A=(C===null||C===void 0?void 0:C.addSuffix)===!0,I=C===null||C===void 0?void 0:C.comparison,T;if(A&&I===-1)T=Y.past;else if(A&&I===1)T=Y.future;else T=Y.regular;return T.replace("{{count}}",String(E))};function N(B){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=U.width?String(U.width):B.defaultWidth,C=B.formats[E]||B.formats[B.defaultWidth];return C}}var $={full:"EEEE, d. MMMM yyyy",long:"d. MMMM yyyy",medium:"d. M. yyyy",short:"dd.MM.yyyy"},x={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},S={full:"{{date}} 'v' {{time}}",long:"{{date}} 'v' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:N({formats:$,defaultWidth:"full"}),time:N({formats:x,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},V=["ned\u011Bli","pond\u011Bl\xED","\xFAter\xFD","st\u0159edu","\u010Dtvrtek","p\xE1tek","sobotu"],j={lastWeek:"'posledn\xED' eeee 've' p",yesterday:"'v\u010Dera v' p",today:"'dnes v' p",tomorrow:"'z\xEDtra v' p",nextWeek:function B(U){var E=U.getDay();return"'v "+V[E]+" o' p"},other:"P"},R=function B(U,E){var C=j[U];if(typeof C==="function")return C(E);return C};function Q(B){return function(U,E){var C=E!==null&&E!==void 0&&E.context?String(E.context):"standalone",Y;if(C==="formatting"&&B.formattingValues){var J=B.defaultFormattingWidth||B.defaultWidth,A=E!==null&&E!==void 0&&E.width?String(E.width):J;Y=B.formattingValues[A]||B.formattingValues[J]}else{var I=B.defaultWidth,T=E!==null&&E!==void 0&&E.width?String(E.width):B.defaultWidth;Y=B.values[T]||B.values[I]}var O=B.argumentCallback?B.argumentCallback(U):U;return Y[O]}}var f={narrow:["p\u0159. n. l.","n. l."],abbreviated:["p\u0159. n. l.","n. l."],wide:["p\u0159ed na\u0161\xEDm letopo\u010Dtem","na\u0161eho letopo\u010Dtu"]},_={narrow:["1","2","3","4"],abbreviated:["1. \u010Dtvrtlet\xED","2. \u010Dtvrtlet\xED","3. \u010Dtvrtlet\xED","4. \u010Dtvrtlet\xED"],wide:["1. \u010Dtvrtlet\xED","2. \u010Dtvrtlet\xED","3. \u010Dtvrtlet\xED","4. \u010Dtvrtlet\xED"]},v={narrow:["L","\xDA","B","D","K","\u010C","\u010C","S","Z","\u0158","L","P"],abbreviated:["led","\xFAno","b\u0159e","dub","kv\u011B","\u010Dvn","\u010Dvc","srp","z\xE1\u0159","\u0159\xEDj","lis","pro"],wide:["leden","\xFAnor","b\u0159ezen","duben","kv\u011Bten","\u010Derven","\u010Dervenec","srpen","z\xE1\u0159\xED","\u0159\xEDjen","listopad","prosinec"]},w={narrow:["L","\xDA","B","D","K","\u010C","\u010C","S","Z","\u0158","L","P"],abbreviated:["led","\xFAno","b\u0159e","dub","kv\u011B","\u010Dvn","\u010Dvc","srp","z\xE1\u0159","\u0159\xEDj","lis","pro"],wide:["ledna","\xFAnora","b\u0159ezna","dubna","kv\u011Btna","\u010Dervna","\u010Dervence","srpna","z\xE1\u0159\xED","\u0159\xEDjna","listopadu","prosince"]},F={narrow:["ne","po","\xFAt","st","\u010Dt","p\xE1","so"],short:["ne","po","\xFAt","st","\u010Dt","p\xE1","so"],abbreviated:["ned","pon","\xFAte","st\u0159","\u010Dtv","p\xE1t","sob"],wide:["ned\u011Ble","pond\u011Bl\xED","\xFAter\xFD","st\u0159eda","\u010Dtvrtek","p\xE1tek","sobota"]},b={narrow:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"}},P={narrow:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"}},k=function B(U,E){var C=Number(U);return C+"."},h={ordinalNumber:k,era:Q({values:f,defaultWidth:"wide"}),quarter:Q({values:_,defaultWidth:"wide",argumentCallback:function B(U){return U-1}}),month:Q({values:v,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"}),day:Q({values:F,defaultWidth:"wide"}),dayPeriod:Q({values:b,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function q(B){return function(U){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=E.width,Y=C&&B.matchPatterns[C]||B.matchPatterns[B.defaultMatchWidth],J=U.match(Y);if(!J)return null;var A=J[0],I=C&&B.parsePatterns[C]||B.parsePatterns[B.defaultParseWidth],T=Array.isArray(I)?c(I,function(M){return M.test(A)}):m(I,function(M){return M.test(A)}),O;O=B.valueCallback?B.valueCallback(T):T,O=E.valueCallback?E.valueCallback(O):O;var E1=U.slice(A.length);return{value:O,rest:E1}}}var m=function B(U,E){for(var C in U)if(Object.prototype.hasOwnProperty.call(U,C)&&E(U[C]))return C;return},c=function B(U,E){for(var C=0;C<U.length;C++)if(E(U[C]))return C;return};function y(B){return function(U){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=U.match(B.matchPattern);if(!C)return null;var Y=C[0],J=U.match(B.parsePattern);if(!J)return null;var A=B.valueCallback?B.valueCallback(J[0]):J[0];A=E.valueCallback?E.valueCallback(A):A;var I=U.slice(Y.length);return{value:A,rest:I}}}var p=/^(\d+)\.?/i,u=/\d+/i,g={narrow:/^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(p[řr](\.|ed) Kristem|p[řr](\.|ed) na[šs][íi]m letopo[čc]tem|po Kristu|na[šs]eho letopo[čc]tu)/i},d={any:[/^p[řr]/i,/^(po|n)/i]},l={narrow:/^[1234]/i,abbreviated:/^[1234]\. [čc]tvrtlet[íi]/i,wide:/^[1234]\. [čc]tvrtlet[íi]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[lúubdkčcszřrlp]/i,abbreviated:/^(led|[úu]no|b[řr]e|dub|kv[ěe]|[čc]vn|[čc]vc|srp|z[áa][řr]|[řr][íi]j|lis|pro)/i,wide:/^(leden|ledna|[úu]nora?|b[řr]ezen|b[řr]ezna|duben|dubna|kv[ěe]ten|kv[ěe]tna|[čc]erven(ec|ce)?|[čc]ervna|srpen|srpna|z[áa][řr][íi]|[řr][íi]jen|[řr][íi]jna|listopad(a|u)?|prosinec|prosince)/i},s={narrow:[/^l/i,/^[úu]/i,/^b/i,/^d/i,/^k/i,/^[čc]/i,/^[čc]/i,/^s/i,/^z/i,/^[řr]/i,/^l/i,/^p/i],any:[/^led/i,/^[úu]n/i,/^b[řr]e/i,/^dub/i,/^kv[ěe]/i,/^[čc]vn|[čc]erven(?!\w)|[čc]ervna/i,/^[čc]vc|[čc]erven(ec|ce)/i,/^srp/i,/^z[áa][řr]/i,/^[řr][íi]j/i,/^lis/i,/^pro/i]},o={narrow:/^[npuúsčps]/i,short:/^(ne|po|[úu]t|st|[čc]t|p[áa]|so)/i,abbreviated:/^(ned|pon|[úu]te|st[rř]|[čc]tv|p[áa]t|sob)/i,wide:/^(ned[ěe]le|pond[ěe]l[íi]|[úu]ter[ýy]|st[řr]eda|[čc]tvrtek|p[áa]tek|sobota)/i},r={narrow:[/^n/i,/^p/i,/^[úu]/i,/^s/i,/^[čc]/i,/^p/i,/^s/i],any:[/^ne/i,/^po/i,/^[úu]t/i,/^st/i,/^[čc]t/i,/^p[áa]/i,/^so/i]},e={any:/^dopoledne|dop\.?|odpoledne|odp\.?|p[ůu]lnoc|poledne|r[áa]no|odpoledne|ve[čc]er|(v )?noci?/i},a={any:{am:/^dop/i,pm:/^odp/i,midnight:/^p[ůu]lnoc/i,noon:/^poledne/i,morning:/r[áa]no/i,afternoon:/odpoledne/i,evening:/ve[čc]er/i,night:/noc/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:u,valueCallback:function B(U){return parseInt(U,10)}}),era:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(U){return U+1}}),month:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},B1={code:"cs",formatDistance:K,formatLong:L,formatRelative:R,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=z(z({},window.dateFns),{},{locale:z(z({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{cs:B1})})})();

//# debugId=4BCFE2F5F99ABBA264756e2164756e21
