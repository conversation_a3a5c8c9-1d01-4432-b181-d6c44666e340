import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Users,
  Clock,
  Plus,
  CheckCircle,
  AlertCircle,
  Coffee,
  Utensils
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { kotStorage } from "@/utils/kotStorage";

const TableManagement = () => {
  const navigate = useNavigate();

  // Load tables from localStorage (same as admin)
  const loadTables = () => {
    try {
      const stored = localStorage.getItem('restaurant_tables');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading tables:', error);
    }
    // Default tables
    return [
      { id: "1", capacity: 4, status: "available", orderNumber: null },
      { id: "2", capacity: 2, status: "available", orderNumber: null },
      { id: "3", capacity: 6, status: "available", orderNumber: null },
      { id: "4", capacity: 4, status: "available", orderNumber: null },
      { id: "5", capacity: 2, status: "available", orderNumber: null },
      { id: "6", capacity: 4, status: "available", orderNumber: null },
      { id: "7", capacity: 8, status: "available", orderNumber: null },
      { id: "8", capacity: 4, status: "available", orderNumber: null },
    ];
  };

  const [tables, setTables] = useState(loadTables);

  // Update table statuses based on active KOTs
  useEffect(() => {
    const updateTableStatuses = () => {
      const updatedTables = tables.map(table => {
        const activeKOT = kotStorage.getActiveKOTForTable(table.id);
        if (activeKOT) {
          return {
            ...table,
            status: "occupied",
            orderNumber: activeKOT.kotNumber,
            orderTime: new Date(activeKOT.createdAt).toLocaleTimeString('en-IN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            }),
            guests: activeKOT.items.length // Using items count as guest count for now
          };
        }
        return { ...table, status: "available", orderNumber: null };
      });
      setTables(updatedTables);
    };

    updateTableStatuses();

    // Update every 30 seconds
    const interval = setInterval(updateTableStatuses, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-white border-2 border-gray-300 text-gray-900";
      case "occupied": return "bg-red-500 text-white";
      case "preparing": return "bg-yellow-500 text-white";
      default: return "bg-gray-400 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "available": return "Free";
      case "occupied": return "Occupied";
      case "preparing": return "Preparing";
      default: return status;
    }
  };



  const stats = {
    total: tables.length,
    available: tables.filter(t => t.status === "available").length,
    occupied: tables.filter(t => t.status === "occupied").length,
    preparing: tables.filter(t => t.status === "preparing").length,
  };

  const handleTableClick = (table: any) => {
    if (table.status === "available") {
      navigate(`/delivery/table-order/${table.id}`);
    } else if (table.status === "occupied" || table.status === "preparing") {
      navigate(`/delivery/table-details/${table.id}`);
    }
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4 apk-header-fixed">
        <div className="flex items-center gap-3 mb-4">
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-white/20 back-button-highlight tap-target"
            onClick={() => navigate("/delivery/dashboard")}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Table Management</h1>
            <p className="text-white/80 text-sm">Select a table to take order</p>
          </div>
        </div>
      </div>

      <div className="p-4 apk-content-with-header">
        {/* Legend */}
        <div className="flex items-center justify-center gap-6 mb-6 bg-white rounded-lg p-3 shadow-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded border-2 border-gray-300 bg-white"></div>
            <span className="text-sm text-gray-600">Free</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded bg-red-500"></div>
            <span className="text-sm text-gray-600">Occupied</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 rounded bg-yellow-500"></div>
            <span className="text-sm text-gray-600">Preparing</span>
          </div>
        </div>

        {/* Tables Grid */}
        <div className="grid grid-cols-2 gap-1 p-1">
          {tables.map((table) => (
            <div
              key={table.id}
              className={`
                rounded-lg p-4 text-center cursor-pointer transition-all duration-200 hover:scale-105 shadow-sm min-h-[140px] flex flex-col justify-center
                ${getStatusColor(table.status)}
              `}
              onClick={() => handleTableClick(table)}
            >
              <div className="mb-1">
                <h2 className="text-base font-bold mb-0.5">Table</h2>
                <h2 className="text-xl font-bold">{table.id}</h2>
              </div>

              <div className="flex items-center justify-center gap-1 mb-1">
                <Users className="h-3 w-3" />
                <span className="text-xs font-medium">{table.capacity} seats</span>
              </div>

              {table.orderNumber && (
                <div className="bg-white/20 rounded-full px-2 py-0.5 mb-1">
                  <span className="text-xs font-medium">Order</span>
                  <div className="text-xs">{table.orderNumber}</div>
                </div>
              )}

              <div className="text-xs font-medium">
                {getStatusText(table.status)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TableManagement;
