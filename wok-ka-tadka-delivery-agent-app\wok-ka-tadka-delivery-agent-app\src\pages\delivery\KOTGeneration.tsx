import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { gstSettingsManager, GSTSettings } from "@/utils/gstSettings";
import {
  ArrowLeft,
  Printer,
  Clock,
  Users,
  CheckCircle,
  AlertTriangle,
  Utensils,
  MessageSquare,
  FileText,
  Calculator
} from "lucide-react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { useToast } from "@/hooks/use-toast";
import { kotStorage, KOT, KOTItem } from "@/utils/kotStorage";
import CustomerSelectionDialog from "@/components/CustomerSelectionDialog";
import { Customer, customerManager } from "@/utils/customerStorage";
import PrintService from "@/services/printService";
import PaymentMethodDialog, { PaymentMethod } from "@/components/PaymentMethodDialog";

const KOTGeneration = () => {
  const navigate = useNavigate();
  const { tableId } = useParams();
  const location = useLocation();
  const { toast } = useToast();
  const [specialInstructions, setSpecialInstructions] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showPaymentMethodDialog, setShowPaymentMethodDialog] = useState(false);
  const [isPrintingBill, setIsPrintingBill] = useState(false);
  const [includeGST, setIncludeGST] = useState(true); // GST toggle state
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('cash');
  const [gstSettings, setGSTSettings] = useState<GSTSettings>(
    gstSettingsManager.getGSTSettings()
  );
  const [currentKOT, setCurrentKOT] = useState<KOT | null>(null);

  const cart = location.state?.cart || [];
  const existingKOT = location.state?.existingKOT;
  const addToExisting = location.state?.addToExisting;
  const createNew = location.state?.createNew;
  const viewMode = location.state?.viewMode;

  const currentTime = new Date().toLocaleTimeString('en-IN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });

  // Load KOT data when in view mode
  useEffect(() => {
    if (viewMode && tableId && !existingKOT) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (activeKOT) {
        setCurrentKOT(activeKOT);
      }
    } else if (existingKOT) {
      setCurrentKOT(existingKOT);
    }
  }, [viewMode, tableId, existingKOT]);

  // Listen for GST settings changes
  useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent<GSTSettings>) => {
      setGSTSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);

    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    };
  }, []);


  const currentDate = new Date().toLocaleDateString('en-IN', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric'
  });

  const kotNumber = existingKOT?.kotNumber || `KOT${Date.now().toString().slice(-6)}`;

  const getTotalAmount = () => {
    return cart.reduce((total: number, item: any) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cart.reduce((total: number, item: any) => total + item.quantity, 0);
  };

  const convertCartToKOTItems = (cartItems: any[]): KOTItem[] => {
    return cartItems.map(item => ({
      id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.quantity,
      veg: item.veg,
      spicy: item.spicy,
      image: item.image,
      addedAt: new Date().toISOString()
    }));
  };

  const generateKOTContent = (kot: KOT): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    const itemsSection = kot.items.map(item =>
      `<tr>
        <td>${item.name}</td>
        <td>${item.quantity}</td>
        <td>₹${item.price}</td>
        <td>₹${item.price * item.quantity}</td>
      </tr>`
    ).join('');

    const instructionsSection = kot.specialInstructions ?
      `<div style="margin: 20px 0;">
        <strong>Special Instructions:</strong> ${kot.specialInstructions}
      </div>` : '';

    return `<!DOCTYPE html>
<html>
<head>
  <title>KOT - Table ${tableId}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { text-align: center; margin-bottom: 20px; }
    .restaurant-name { font-size: 24px; font-weight: bold; }
    .kot-info { margin: 20px 0; }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .total-row { font-weight: bold; background-color: #f9f9f9; }
    @media print { body { margin: 0; } }
  </style>
</head>
<body>
  <div class="header">
    <div class="restaurant-name">Wok Ka Tadka</div>
    <div>Mumbai Style Chinese & Indian</div>
    <div>Phone: +91 9876543210</div>
  </div>

  <div class="kot-info">
    <strong>KOT No:</strong> ${kot.kotNumber}<br>
    <strong>Table:</strong> ${tableId}<br>
    <strong>Date:</strong> ${currentDate}<br>
    <strong>Time:</strong> ${currentTime}
  </div>

  <table>
    <thead>
      <tr>
        <th>Item</th>
        <th>Qty</th>
        <th>Price</th>
        <th>Amount</th>
      </tr>
    </thead>
    <tbody>
      ${itemsSection}
      <tr class="total-row">
        <td colspan="3"><strong>Total Amount</strong></td>
        <td><strong>₹${kot.totalAmount}</strong></td>
      </tr>
    </tbody>
  </table>

  ${instructionsSection}
</body>
</html>`;
  };

  const generateBillContent = (kot: KOT, customer: Customer | null, includeGST: boolean = true): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    const customerSection = customer ?
      `<div class="customer-info">
        <strong>Customer Details:</strong><br>
        <strong>Name:</strong> ${customer.name}<br>
        ${customer.phone ? `<strong>Phone:</strong> ${customer.phone}<br>` : ''}
        ${customer.email ? `<strong>Email:</strong> ${customer.email}<br>` : ''}
        ${customer.isRegular ? '<strong>Status:</strong> Regular Customer ⭐<br>' : ''}
      </div>` : '';

    // Aggregate all items from all KOT versions to handle multiple orders
    const aggregatedItems = new Map<string, { name: string; quantity: number; price: number; veg: boolean }>();

    // Process all items from all versions
    kot.items.forEach(item => {
      const key = `${item.name}_${item.price}`; // Use name + price as key to handle same item with different prices
      if (aggregatedItems.has(key)) {
        const existing = aggregatedItems.get(key)!;
        existing.quantity += item.quantity;
      } else {
        aggregatedItems.set(key, {
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          veg: item.veg
        });
      }
    });

    // Generate items section with aggregated quantities
    const itemsSection = Array.from(aggregatedItems.values()).map(item =>
      `<tr>
        <td>${item.name} ${item.veg ? '🟢' : '🔴'}</td>
        <td>${item.quantity}</td>
        <td>₹${item.price}</td>
        <td>₹${item.price * item.quantity}</td>
      </tr>`
    ).join('');

    const instructionsSection = kot.specialInstructions ?
      `<div style="margin: 20px 0;">
        <strong>Special Instructions:</strong> ${kot.specialInstructions}
      </div>` : '';

    // Generate order history section if multiple versions exist
    const orderHistorySection = kot.versions.length > 1 ? `
      <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff;">
        <strong>Order History (${kot.versions.length} separate orders):</strong><br>
        ${kot.versions.map((version, index) => `
          <div style="margin: 8px 0; padding: 8px; background-color: white; border-radius: 4px;">
            <strong>Order #${index + 1}</strong> - ${new Date(version.addedAt).toLocaleString('en-IN')}<br>
            <small style="color: #666;">
              ${version.addedItems.map(item => `${item.name} (${item.quantity})`).join(', ')}
              ${version.specialInstructions ? `<br><em>Instructions: ${version.specialInstructions}</em>` : ''}
            </small>
          </div>
        `).join('')}
      </div>` : '';

    // Calculate GST using global settings
    const subtotal = kot.totalAmount;
    const cgst = includeGST ? (subtotal * gstSettings.cgstRate) / 100 : 0;
    const sgst = includeGST ? (subtotal * gstSettings.sgstRate) / 100 : 0;
    const totalGST = cgst + sgst;
    const grandTotal = subtotal + totalGST;

    const gstSection = includeGST ? `
      <tr>
        <td colspan="3"><strong>CGST (${gstSettings.cgstRate}%)</strong></td>
        <td><strong>₹${cgst.toFixed(2)}</strong></td>
      </tr>
      <tr>
        <td colspan="3"><strong>SGST (${gstSettings.sgstRate}%)</strong></td>
        <td><strong>₹${sgst.toFixed(2)}</strong></td>
      </tr>` : `
      <tr style="color: #f59e0b;">
        <td colspan="3"><strong>GST (Excluded)</strong></td>
        <td><strong>₹0.00</strong></td>
      </tr>`;



    return `<!DOCTYPE html>
<html>
<head>
  <title>Bill - Table ${tableId}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { text-align: center; margin-bottom: 20px; }
    .restaurant-name { font-size: 24px; font-weight: bold; }
    .bill-info { margin: 20px 0; }
    .customer-info { margin: 10px 0; padding: 10px; background-color: #f5f5f5; }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .total-row { font-weight: bold; background-color: #f9f9f9; }
    .footer { margin-top: 30px; text-align: center; }
    @media print { body { margin: 0; } }
  </style>
</head>
<body>
  <div class="header">
    <div class="restaurant-name">Wok Ka Tadka</div>
    <div>Mumbai Style Chinese & Indian</div>
    <div>Phone: +91 9876543210</div>
  </div>

  <div class="bill-info">
    <strong>Bill No:</strong> ${kot.kotNumber}<br>
    <strong>Table:</strong> ${tableId}<br>
    <strong>Date:</strong> ${currentDate}<br>
    <strong>Time:</strong> ${currentTime}
  </div>

  ${customerSection}

  <table>
    <thead>
      <tr>
        <th>Item</th>
        <th>Qty</th>
        <th>Price</th>
        <th>Amount</th>
      </tr>
    </thead>
    <tbody>
      ${itemsSection}
      <tr>
        <td colspan="3"><strong>Subtotal</strong></td>
        <td><strong>₹${subtotal.toFixed(2)}</strong></td>
      </tr>
      ${gstSection}
      <tr class="total-row" style="background-color: #e5f3ff; font-size: 16px;">
        <td colspan="3"><strong>Grand Total ${includeGST ? '(incl. GST)' : '(excl. GST)'}</strong></td>
        <td><strong>₹${grandTotal.toFixed(2)}</strong></td>
      </tr>
    </tbody>
  </table>

  ${instructionsSection}
  ${orderHistorySection}

  <div class="footer">
    <p>Thank you for dining with us!</p>
    <p>Visit us again soon!</p>
  </div>
</body>
</html>`;
  };

  const handleGenerateKOT = async () => {
    if (!tableId) return;

    setIsGenerating(true);

    try {
      let result: KOT | null = null;

      if (addToExisting && existingKOT) {
        // Add items to existing KOT and print only the new items
        const kotItems = convertCartToKOTItems(cart);
        result = kotStorage.addItemsToKOT(existingKOT.kotNumber, kotItems, specialInstructions);

        if (result) {
          // Generate KOT content for only the NEW items being added
          const newItemsKOT = {
            ...result,
            items: kotItems, // Only the new items
            kotNumber: `${result.kotNumber}-ADD${result.versions.length}`, // Add suffix to show it's an addition
            specialInstructions: specialInstructions || `Additional items for KOT #${existingKOT.kotNumber}`
          };

          // Print the KOT for new items using unified print service
          const success = await PrintService.printKOT(newItemsKOT);

          if (success) {
            toast({
              title: "Items Added & KOT Printed!",
              description: `${cart.length} items added to KOT #${existingKOT.kotNumber} and sent to kitchen`,
            });
          } else {
            toast({
              title: "Items Added",
              description: `${cart.length} items added to KOT #${existingKOT.kotNumber} but printing failed. Please try printing manually.`,
              variant: "destructive"
            });
          }
        }
      } else {
        // Create new KOT
        const kotItems = convertCartToKOTItems(cart);
        result = kotStorage.createKOT(tableId, kotItems, specialInstructions);

        if (result) {
          // Print the new KOT using unified print service
          const success = await PrintService.printKOT(result);

          if (success) {
            toast({
              title: "KOT Generated & Printed!",
              description: `KOT #${result.kotNumber} created and sent to kitchen for Table ${tableId}`,
            });
          } else {
            toast({
              title: "KOT Generated",
              description: `KOT #${result.kotNumber} created but printing failed. Please try printing manually.`,
              variant: "destructive"
            });
          }
        }
      }

      if (result) {
        // Navigate back to table order page after successful operation
        setTimeout(() => {
          navigate(`/delivery/table-order/${tableId}`);
        }, 2000);
      } else {
        throw new Error("Failed to process KOT");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process KOT. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrintBill = () => {
    setShowCustomerDialog(true);
  };

  const handleCustomerSelection = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setShowCustomerDialog(false);
    setShowPaymentMethodDialog(true);
  };

  const handlePaymentMethodSelection = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setShowPaymentMethodDialog(false);
    printBill(selectedCustomer, paymentMethod);
  };

  const printBill = async (customer: Customer | null, paymentMethod: PaymentMethod = 'cash') => {
    if (!tableId) return;

    setIsPrintingBill(true);

    try {
      // Get current KOT for the table
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);

      if (!activeKOT) {
        toast({
          title: "No Active KOT",
          description: "No active KOT found for this table to generate bill.",
          variant: "destructive"
        });
        return;
      }

      // Add customer order to records if customer is selected
      if (customer) {
        customerManager.addCustomerOrder({
          customerId: customer.id,
          orderId: activeKOT.kotNumber,
          tableId: tableId,
          kotNumber: activeKOT.kotNumber,
          amount: activeKOT.totalAmount,
          items: activeKOT.items.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price
          }))
        });
      }

      // Print bill using unified print service
      const success = await PrintService.printBill(activeKOT, customer, {
        includeGST,
        paymentMethod
      });

      if (!success) {
        toast({
          title: "Print Failed",
          description: "Bill printing failed. Please try again.",
          variant: "destructive"
        });
        return;
      }

      // Mark KOT as completed
      kotStorage.completeKOT(activeKOT.kotNumber);

      toast({
        title: "Bill Printed Successfully!",
        description: customer
          ? `Bill printed for ${customer.name} - Table ${tableId}`
          : `Bill printed for Table ${tableId}`,
      });

      // Navigate back to table management
      setTimeout(() => {
        navigate("/delivery/tables");
      }, 2000);
    } catch (error) {
      toast({
        title: "Print Error",
        description: "Failed to print bill. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsPrintingBill(false);
    }
  };



  // Handle case when no cart items and not in view mode
  if (cart.length === 0 && !viewMode) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Items Selected</h2>
            <p className="text-gray-600 mb-4">Please add items to cart before generating KOT</p>
            <Button onClick={() => navigate(`/delivery/table-order/${tableId}`, { replace: true })}>
              Back to Menu
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle case when in view mode but no KOT data available
  if (viewMode && !currentKOT && !existingKOT) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Active KOT Found</h2>
            <p className="text-gray-600 mb-4">No active KOT found for Table {tableId}</p>
            <Button onClick={() => navigate(`/delivery/table-order/${tableId}`, { replace: true })}>
              Back to Menu
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Use currentKOT if available, otherwise use existingKOT
  const displayKOT = currentKOT || existingKOT;

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4 apk-header-fixed">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20 back-button-highlight tap-target"
              onClick={() => {
                // Navigate back to table order page
                navigate(`/delivery/table-order/${tableId}`, { replace: true });
              }}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">
                {viewMode ? 'View KOT' : addToExisting ? 'Add to Existing KOT' : 'Generate KOT'}
              </h1>
              <p className="text-white/80 text-sm">
                Table {tableId}
                {displayKOT && (
                  <span className="ml-2 text-yellow-200">
                    • KOT #{displayKOT.kotNumber}
                  </span>
                )}
              </p>
            </div>
          </div>
          <Logo size="sm" variant="white" />
        </div>
      </div>

      <div className={`${viewMode ? 'bg-gray-50 min-h-screen apk-content-with-header apk-content-with-footer' : 'p-4 space-y-4 apk-content-with-header apk-content-with-footer'}`} style={{paddingBottom: viewMode ? '150px' : undefined}}>
        {viewMode ? (
          /* View Mode - Clean Layout exactly like Reference */
          <div className="bg-white min-h-screen">
            {/* Complete Order History Header */}
            <div className="p-4 border-b bg-white">
              <div className="flex items-center gap-2 text-gray-900">
                <FileText className="h-5 w-5" />
                <h2 className="text-lg font-semibold">Complete Order History - Table {tableId}</h2>
              </div>
            </div>

            {/* Order Timeline */}
            <div className="p-4 space-y-4">
              {displayKOT?.items?.map((item: any, index: number) => (
                <div key={index} className="flex gap-3">
                  {/* Timeline indicator */}
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Clock className="h-4 w-4 text-blue-600" />
                    </div>
                    {index < (displayKOT?.items?.length || 0) - 1 && (
                      <div className="w-0.5 h-16 bg-blue-200 mt-2"></div>
                    )}
                  </div>

                  {/* Order content */}
                  <div className="flex-1 bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">Order #{index + 1}</span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                          {index === 0 ? 'Initial' : 'Added'}
                        </Badge>
                      </div>
                      <span className="text-sm text-gray-600">
                        {displayKOT?.createdAt ? new Date(displayKOT.createdAt).toLocaleString('en-IN', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true
                        }) : currentDate}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">{item.name}</span>
                        {item.isVeg !== undefined && (
                          <div className={`w-3 h-3 border-2 flex items-center justify-center ${
                            item.isVeg ? 'border-green-500' : 'border-red-500'
                          }`}>
                            <div className={`w-1.5 h-1.5 rounded-full ${
                              item.isVeg ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                          </div>
                        )}
                        <span className="text-sm text-gray-600">₹{item.price} × {item.quantity}</span>
                      </div>
                      <span className="font-bold text-gray-900">₹{item.price * item.quantity}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Grand Total */}
            <div className="p-4 border-t bg-white">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xl font-bold text-gray-900">
                  Grand Total ({displayKOT?.items?.length || 0} items):
                </span>
                <span className="text-2xl font-bold text-gray-900">
                  ₹{displayKOT?.totalAmount || 0}
                </span>
              </div>
              {displayKOT?.createdAt && (
                <p className="text-sm text-gray-600">
                  Order started: {new Date(displayKOT.createdAt).toLocaleString('en-IN', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  })}
                </p>
              )}
            </div>

            {/* GST Section */}
            <div className="p-4">
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Calculator className="h-5 w-5 text-yellow-600" />
                    <div>
                      <h3 className="font-semibold text-gray-900">GST (CGST + SGST)</h3>
                      <p className="text-sm text-gray-600">Include {gstSettings.totalGSTRate}% GST in this bill</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className={`text-sm font-medium ${includeGST ? 'text-green-600' : 'text-gray-500'}`}>
                      {includeGST ? 'With GST' : 'Without GST'}
                    </span>
                    <Switch
                      checked={includeGST}
                      onCheckedChange={setIncludeGST}
                      className="data-[state=checked]:bg-green-600"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Edit Mode - Original Layout */
          <Card className="shadow-card border-0 bg-white">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Kitchen Order Ticket</CardTitle>
                <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                  {kotNumber}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Restaurant & Order Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <Logo size="sm" variant="default" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Wok Ka Tadka</h3>
                    <p className="text-sm text-gray-600">Mumbai Style Chinese & Indian</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Table:</span>
                    <span className="font-semibold ml-2">{tableId}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">KOT #:</span>
                    <span className="font-semibold ml-2">{kotNumber}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Date:</span>
                    <span className="font-semibold ml-2">{currentDate}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Time:</span>
                    <span className="font-semibold ml-2">{currentTime}</span>
                  </div>
                </div>
              </div>



            {/* Existing KOT Items (when adding to existing) */}
            {addToExisting && existingKOT && !viewMode && (
              <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Previously Ordered Items ({existingKOT.items.length} items)
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs">
                    KOT #{existingKOT.kotNumber}
                  </Badge>
                </h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {existingKOT.items.map((item: KOTItem, index: number) => (
                    <div key={index} className="flex justify-between items-center py-2 bg-white rounded px-3">
                      <div className="flex items-center gap-2">
                        <span className="text-blue-800 font-medium">{item.name}</span>
                        {item.veg ? (
                          <div className="w-3 h-3 border border-green-500 rounded-sm flex items-center justify-center">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          </div>
                        ) : (
                          <div className="w-3 h-3 border border-red-500 rounded-sm flex items-center justify-center">
                            <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          </div>
                        )}
                        {item.spicy > 0 && (
                          <div className="flex">
                            {Array.from({ length: item.spicy }).map((_, i) => (
                              <span key={i} className="text-red-500 text-xs">🌶️</span>
                            ))}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-blue-700">
                        <span className="text-sm">₹{item.price} × {item.quantity}</span>
                        <span className="font-semibold">₹{item.price * item.quantity}</span>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-3 pt-3 border-t border-blue-200 flex justify-between font-semibold text-blue-900">
                  <span>Previous Order Total:</span>
                  <span>₹{existingKOT.totalAmount}</span>
                </div>
                <div className="mt-2 text-xs text-blue-600">
                  Created: {new Date(existingKOT.createdAt).toLocaleString()}
                  {existingKOT.versions.length > 1 && (
                    <span className="ml-2">• Updated {existingKOT.versions.length - 1} time(s)</span>
                  )}
                </div>
              </div>
            )}

            {/* Order Items - Only show in edit mode */}
            {!viewMode && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Utensils className="h-4 w-4" />
                {addToExisting ? 'New Items to Add' : 'Order Items'} ({getTotalItems()} items)
              </h4>
              <div className="space-y-2">
                {viewMode ? (
                  // Show existing KOT items in view mode
                  displayKOT?.items.map((item: KOTItem, index: number) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">{item.name}</span>
                          {item.veg ? (
                            <div className="w-3 h-3 border border-green-500 rounded-sm flex items-center justify-center">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                            </div>
                          ) : (
                            <div className="w-3 h-3 border border-red-500 rounded-sm flex items-center justify-center">
                              <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                            </div>
                          )}
                          {item.spicy > 0 && (
                            <div className="flex">
                              {Array.from({ length: item.spicy }).map((_, i) => (
                                <span key={i} className="text-red-500 text-xs">🌶️</span>
                              ))}
                            </div>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Added: {new Date(item.addedAt).toLocaleString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          <span className="text-gray-600">₹{item.price} × {item.quantity}</span>
                          <span className="font-semibold text-gray-900">₹{item.price * item.quantity}</span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  // Show cart items for new KOT or adding to existing
                  cart.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-gray-900">{item.name}</span>
                        {item.veg ? (
                          <div className="w-3 h-3 border border-green-500 rounded-sm flex items-center justify-center">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          </div>
                        ) : (
                          <div className="w-3 h-3 border border-red-500 rounded-sm flex items-center justify-center">
                            <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">₹{item.price} each</div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-900">Qty: {item.quantity}</div>
                      <div className="text-sm text-primary font-medium">₹{item.price * item.quantity}</div>
                    </div>
                  </div>
                  ))
                )}
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                {viewMode ? (
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total KOT Amount:</span>
                    <span className="text-primary">₹{displayKOT?.totalAmount || 0}</span>
                  </div>
                ) : addToExisting ? (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span>New Items Total:</span>
                      <span>₹{getTotalAmount()}</span>
                    </div>
                    <div className="flex justify-between items-center text-lg font-bold border-t pt-2">
                      <span>Grand Total:</span>
                      <span className="text-primary">₹{getTotalAmount() + (existingKOT?.totalAmount || 0)}</span>
                    </div>
                  </div>
                ) : (
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total Amount:</span>
                    <span className="text-primary">₹{getTotalAmount()}</span>
                  </div>
                )}
              </div>
            </div>
            )}

            {/* Special Instructions */}
            {!viewMode && (
              <div className="relative z-10">
                <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Special Instructions
                </h4>
                <Textarea
                  placeholder="Add any special instructions for the admin (e.g., less spicy, extra sauce, etc.)"
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  className="min-h-[80px] resize-none relative z-10"
                />
              </div>
            )}

            {/* Kitchen Priority */}
            {!viewMode && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-yellow-800">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Estimated Preparation Time: 25-30 minutes</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        )}
      </div>

      {/* Fixed Action Buttons */}
      {!viewMode && (
        <div className="apk-footer-fixed bg-white border-t shadow-lg p-4">
          <Button
            variant="delivery"
            size="lg"
            className="w-full h-12 text-sm sm:text-base apk-button-safe"
            onClick={handleGenerateKOT}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                <span className="truncate">
                  {addToExisting ? 'Printing KOT...' : 'Printing KOT...'}
                </span>
              </>
            ) : (
              <>
                <Printer className="h-4 w-4 mr-2 shrink-0" />
                <span className="truncate">
                  {addToExisting
                    ? `Print KOT (New Items)`
                    : 'Print KOT'
                  }
                </span>
              </>
            )}
          </Button>
        </div>
      )}

      {/* View Mode Fixed Buttons */}
      {viewMode && displayKOT && displayKOT.status === 'active' && (
        <div className="apk-footer-fixed bg-white border-t shadow-lg p-4 space-y-3">
          {/* GST Toggle */}
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calculator className="h-4 w-4 text-yellow-600" />
                <div>
                  <span className="font-medium text-sm text-gray-900">GST (CGST + SGST)</span>
                  <p className="text-xs text-gray-600">Include {gstSettings.totalGSTRate}% GST in bill</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-xs font-medium ${includeGST ? 'text-green-600' : 'text-gray-500'}`}>
                  {includeGST ? 'With GST' : 'Without GST'}
                </span>
                <Switch
                  checked={includeGST}
                  onCheckedChange={setIncludeGST}
                  className="data-[state=checked]:bg-green-600"
                />
              </div>
            </div>
            {!includeGST && (
              <div className="mt-2 p-2 bg-orange-50 rounded border border-orange-200">
                <p className="text-xs text-orange-700">
                  <strong>Note:</strong> GST will be excluded from this bill.
                </p>
              </div>
            )}
          </div>

          <Button
            variant="outline"
            size="lg"
            className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white border-orange-500"
            onClick={() => setShowCustomerDialog(true)}
            disabled={isPrintingBill}
          >
            {isPrintingBill ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                <span>Printing Bill...</span>
              </>
            ) : (
              <>
                <Printer className="h-4 w-4 mr-2" />
                <span>Print Bill</span>
              </>
            )}
          </Button>
          <Button
            variant="default"
            size="lg"
            className="w-full h-12 bg-green-600 hover:bg-green-700"
            onClick={() => {
              if (displayKOT) {
                kotStorage.completeKOT(displayKOT.kotNumber);
                toast({
                  title: "Order Completed!",
                  description: `Table ${tableId} order has been completed successfully`,
                });
                setTimeout(() => {
                  navigate("/delivery/tables");
                }, 1500);
              }
            }}
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Complete Order
          </Button>
        </div>
      )}

      {/* Success Message */}
      {isGenerating && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="shadow-card border-0 bg-green-50 border-green-200 m-4">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 text-green-800">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600"></div>
                <div>
                  <p className="font-medium">Processing your order...</p>
                  <p className="text-sm">{addToExisting ? 'Printing KOT for new items' : 'Generating and printing KOT'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Customer Selection Dialog */}
      <CustomerSelectionDialog
        isOpen={showCustomerDialog}
        onClose={() => setShowCustomerDialog(false)}
        onCustomerSelect={handleCustomerSelection}
        title="Select Customer for Bill"
        description="Choose a customer for this bill or skip to print without customer details"
        showEditButtons={false}
        showPhoneNumbers={false}
      />

      {/* Payment Method Selection Dialog */}
      <PaymentMethodDialog
        open={showPaymentMethodDialog}
        onClose={() => setShowPaymentMethodDialog(false)}
        onSelect={handlePaymentMethodSelection}
        title="Select Payment Method"
      />
    </div>
  );
};

export default KOTGeneration;
