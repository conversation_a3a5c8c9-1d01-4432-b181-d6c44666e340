import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  BarChart3,
  Download,
  RefreshCw
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";

const SalesAnalytics = () => {
  const navigate = useNavigate();
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedPeriod, setSelectedPeriod] = useState<"today" | "week" | "month" | "custom">("today");

  // Mock sales data
  const salesData = {
    today: {
      totalSales: 15420,
      totalOrders: 47,
      avgOrderValue: 328,
      growth: 12,
      hourlyData: [
        { hour: "9 AM", sales: 850, orders: 3 },
        { hour: "10 AM", sales: 1200, orders: 4 },
        { hour: "11 AM", sales: 1850, orders: 6 },
        { hour: "12 PM", sales: 2400, orders: 8 },
        { hour: "1 PM", sales: 3200, orders: 12 },
        { hour: "2 PM", sales: 2800, orders: 9 },
        { hour: "3 PM", sales: 1600, orders: 5 },
        { hour: "4 PM", sales: 1520, orders: 4 }
      ]
    },
    week: {
      totalSales: 98500,
      totalOrders: 312,
      avgOrderValue: 316,
      growth: 8,
      dailyData: [
        { day: "Mon", sales: 12500, orders: 38 },
        { day: "Tue", sales: 14200, orders: 45 },
        { day: "Wed", sales: 13800, orders: 42 },
        { day: "Thu", sales: 15600, orders: 48 },
        { day: "Fri", sales: 16800, orders: 52 },
        { day: "Sat", sales: 18200, orders: 58 },
        { day: "Sun", sales: 7400, orders: 29 }
      ]
    },
    month: {
      totalSales: 425000,
      totalOrders: 1340,
      avgOrderValue: 317,
      growth: 15,
      weeklyData: [
        { week: "Week 1", sales: 98500, orders: 312 },
        { week: "Week 2", sales: 105200, orders: 335 },
        { week: "Week 3", sales: 112800, orders: 358 },
        { week: "Week 4", sales: 108500, orders: 335 }
      ]
    }
  };

  const topItems = [
    { name: "Chicken Biryani", quantity: 45, revenue: 13500, growth: 15 },
    { name: "Paneer Butter Masala", quantity: 32, revenue: 9600, growth: 8 },
    { name: "Mutton Curry", quantity: 28, revenue: 11200, growth: 22 },
    { name: "Garlic Naan", quantity: 67, revenue: 4020, growth: 5 },
    { name: "Dal Makhani", quantity: 24, revenue: 4800, growth: -3 }
  ];

  const paymentMethods = [
    { method: "Cash", amount: 6200, percentage: 40.2 },
    { method: "UPI", amount: 5800, percentage: 37.6 },
    { method: "Card", amount: 2420, percentage: 15.7 },
    { method: "Online", amount: 1000, percentage: 6.5 }
  ];

  const getCurrentData = () => {
    return salesData[selectedPeriod] || salesData.today;
  };

  const getChartData = () => {
    const data = getCurrentData();
    if (selectedPeriod === "today") return data.hourlyData;
    if (selectedPeriod === "week") return data.dailyData;
    if (selectedPeriod === "month") return data.weeklyData;
    return data.hourlyData;
  };

  const handlePeriodChange = (period: "today" | "week" | "month" | "custom") => {
    setSelectedPeriod(period);
  };

  const handleExportData = () => {
    // Mock export functionality
    alert("Sales data exported successfully!");
  };

  const currentData = getCurrentData();
  const chartData = getChartData();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-3 sm:p-4">
          <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Sales Analytics</h1>
              <p className="text-xs sm:text-sm text-gray-500 truncate">Revenue and performance insights</p>
            </div>
          </div>
          <div className="flex items-center gap-1 sm:gap-2 shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm"
            >
              <Download className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Export</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600 shrink-0"
            >
              <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-3 sm:p-6 space-y-4 sm:space-y-6">
        {/* Period Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Calendar className="h-4 w-4 sm:h-5 sm:w-5" />
              Select Time Period
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 sm:gap-3 mb-4">
              <Button
                variant={selectedPeriod === "today" ? "default" : "outline"}
                onClick={() => handlePeriodChange("today")}
                size="sm"
                className="text-xs sm:text-sm"
              >
                Today
              </Button>
              <Button
                variant={selectedPeriod === "week" ? "default" : "outline"}
                onClick={() => handlePeriodChange("week")}
                size="sm"
                className="text-xs sm:text-sm"
              >
                This Week
              </Button>
              <Button
                variant={selectedPeriod === "month" ? "default" : "outline"}
                onClick={() => handlePeriodChange("month")}
                size="sm"
                className="text-xs sm:text-sm"
              >
                This Month
              </Button>
              <Button
                variant={selectedPeriod === "custom" ? "default" : "outline"}
                onClick={() => handlePeriodChange("custom")}
                size="sm"
                className="text-xs sm:text-sm"
              >
                Custom Range
              </Button>
            </div>

            {selectedPeriod === "custom" && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1 pr-3">
                  <p className="text-green-100 text-xs sm:text-sm">Total Sales</p>
                  <p className="text-xl sm:text-3xl font-bold truncate">₹{currentData.totalSales.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1 sm:mt-2">
                    {currentData.growth > 0 ? (
                      <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
                    ) : (
                      <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
                    )}
                    <span className="text-xs sm:text-sm truncate">
                      {currentData.growth > 0 ? '+' : ''}{currentData.growth}% from last period
                    </span>
                  </div>
                </div>
                <DollarSign className="h-8 w-8 sm:h-10 sm:w-10 text-green-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1 pr-3">
                  <p className="text-blue-100 text-xs sm:text-sm">Total Orders</p>
                  <p className="text-xl sm:text-3xl font-bold">{currentData.totalOrders}</p>
                  <div className="flex items-center gap-1 mt-1 sm:mt-2">
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
                    <span className="text-xs sm:text-sm truncate">+5% from last period</span>
                  </div>
                </div>
                <ShoppingCart className="h-8 w-8 sm:h-10 sm:w-10 text-blue-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white sm:col-span-2 lg:col-span-1">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1 pr-3">
                  <p className="text-purple-100 text-xs sm:text-sm">Avg Order Value</p>
                  <p className="text-xl sm:text-3xl font-bold">₹{currentData.avgOrderValue}</p>
                  <div className="flex items-center gap-1 mt-1 sm:mt-2">
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
                    <span className="text-xs sm:text-sm truncate">+2% from last period</span>
                  </div>
                </div>
                <BarChart3 className="h-8 w-8 sm:h-10 sm:w-10 text-purple-200 shrink-0" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sales Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Sales Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-48 sm:h-64 lg:h-80 flex items-end justify-between gap-1 sm:gap-2 p-2 sm:p-4 bg-gray-50 rounded-lg overflow-x-auto">
              {chartData.map((item, index) => {
                const maxSales = Math.max(...chartData.map(d => d.sales));
                const baseHeight = window.innerWidth < 640 ? 120 : window.innerWidth < 1024 ? 160 : 200;
                const height = (item.sales / maxSales) * baseHeight;
                return (
                  <div key={index} className="flex flex-col items-center flex-1 min-w-[40px] sm:min-w-[60px]">
                    <div className="text-[10px] sm:text-xs text-gray-600 mb-1 sm:mb-2 text-center leading-tight">
                      ₹{item.sales > 1000 ? `${(item.sales/1000).toFixed(1)}k` : item.sales.toLocaleString()}
                    </div>
                    <div
                      className="bg-blue-500 rounded-t w-full min-h-[15px] sm:min-h-[20px] transition-all duration-300 hover:bg-blue-600 cursor-pointer"
                      style={{ height: `${Math.max(height, 15)}px` }}
                      title={`₹${item.sales.toLocaleString()}`}
                    />
                    <div className="text-[10px] sm:text-xs text-gray-500 mt-1 sm:mt-2 text-center leading-tight">
                      {(item.hour || item.day || item.week)?.replace(' ', '\n')}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Top Selling Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Top Selling Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 sm:space-y-4">
                {topItems.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1 min-w-0 pr-3">
                      <p className="font-semibold text-xs sm:text-sm truncate">{item.name}</p>
                      <p className="text-xs sm:text-sm text-gray-600">{item.quantity} orders</p>
                    </div>
                    <div className="text-right shrink-0">
                      <p className="font-bold text-xs sm:text-sm">₹{item.revenue.toLocaleString()}</p>
                      <div className="flex items-center gap-1 justify-end">
                        {item.growth > 0 ? (
                          <TrendingUp className="h-3 w-3 text-green-500 shrink-0" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-500 shrink-0" />
                        )}
                        <span className={`text-xs ${item.growth > 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {item.growth > 0 ? '+' : ''}{item.growth}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentMethods.map((method, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 rounded-full bg-blue-500" />
                      <span className="text-sm font-medium">{method.method}</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-bold">₹{method.amount.toLocaleString()}</p>
                      <p className="text-xs text-gray-500">{method.percentage}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SalesAnalytics;
