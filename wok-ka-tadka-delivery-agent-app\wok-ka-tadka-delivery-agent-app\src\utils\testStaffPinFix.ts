// Test utility to verify staff PIN fix
import { 
  createStaffPin, 
  validateStaffPin, 
  getAllStaffPins,
  initializeDefaultPins 
} from './staffPinStorage';

export const testStaffPinFix = () => {
  console.log("🧪 Testing Staff PIN Fix...");
  console.log("=" .repeat(40));
  
  // Initialize
  initializeDefaultPins();
  
  // Test 1: Create staff with different phone formats
  console.log("\n📱 Test 1: Phone Number Normalization");
  
  const testCases = [
    { name: "Test User 1", phone: "9876543210", role: "waiter" as const },
    { name: "Test User 2", phone: "+91 9876543211", role: "waiter" as const },
    { name: "Test User 3", phone: "91-9876-543-212", role: "waiter" as const },
    { name: "Test User 4", phone: "9876 543 213", role: "waiter" as const }
  ];
  
  const createdStaff = [];
  
  for (const testCase of testCases) {
    try {
      const staff = createStaffPin({
        ...testCase,
        createdBy: "test"
      });
      createdStaff.push(staff);
      console.log(`✅ Created: ${staff.name} | Phone: ${staff.phone} | PIN: ${staff.pin}`);
    } catch (error) {
      console.log(`❌ Failed to create: ${testCase.name}`, error);
    }
  }
  
  // Test 2: Validate with different phone formats
  console.log("\n🔍 Test 2: Validation with Different Formats");
  
  for (const staff of createdStaff) {
    const testFormats = [
      staff.phone, // Normalized format
      `+91${staff.phone}`, // With +91
      `91${staff.phone}`, // With 91
      staff.phone.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3'), // With spaces
      staff.phone.replace(/(\d{4})(\d{3})(\d{3})/, '$1-$2-$3') // With dashes
    ];
    
    console.log(`\nTesting ${staff.name} (PIN: ${staff.pin}):`);
    
    for (const phoneFormat of testFormats) {
      const result = validateStaffPin(phoneFormat, staff.pin);
      console.log(`  ${phoneFormat.padEnd(15)} → ${result ? '✅ PASS' : '❌ FAIL'}`);
    }
  }
  
  // Test 3: Check storage
  console.log("\n📋 Test 3: Storage Check");
  const allPins = getAllStaffPins();
  console.log("All stored PINs:", allPins.map(p => ({ 
    name: p.name, 
    phone: p.phone, 
    pin: p.pin, 
    active: p.isActive 
  })));
  
  // Cleanup
  console.log("\n🧹 Cleaning up test data...");
  const cleanedPins = allPins.filter(p => !p.name.startsWith("Test User"));
  localStorage.setItem('wok_ka_tadka_staff_pins', JSON.stringify(cleanedPins));
  
  console.log("✅ Staff PIN fix test completed!");
  return true;
};

// Quick test function for manual testing
export const quickTest = (phone: string, pin: string) => {
  console.log(`🔍 Quick test: ${phone} / ${pin}`);
  const result = validateStaffPin(phone, pin);
  console.log(`Result:`, result);
  return result;
};

// Make available in console
if (typeof window !== 'undefined') {
  (window as any).testStaffPinFix = testStaffPinFix;
  (window as any).quickTest = quickTest;
}
