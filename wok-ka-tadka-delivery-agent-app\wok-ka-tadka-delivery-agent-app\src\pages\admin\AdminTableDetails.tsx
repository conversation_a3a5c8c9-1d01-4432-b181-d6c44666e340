import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { gstSettingsManager, GSTSettings } from "@/utils/gstSettings";
import {
  ArrowLeft,
  Users,
  Clock,
  DollarSign,
  Plus,
  CheckCircle,
  Receipt,
  Utensils,
  FileText,
  Printer,
  Calculator
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { kotStorage } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";
import CustomerSelectionDialog from "@/components/CustomerSelectionDialog";
import { Customer, customerManager } from "@/utils/customerStorage";
import PrintService from "@/services/printService";
import PaymentMethodDialog, { PaymentMethod } from "@/components/PaymentMethodDialog";

const AdminTableDetails = () => {
  const navigate = useNavigate();
  const { tableId } = useParams();
  const { toast } = useToast();
  const [tableData, setTableData] = useState<any>(null);
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showPaymentMethodDialog, setShowPaymentMethodDialog] = useState(false);
  const [isPrintingBill, setIsPrintingBill] = useState(false);
  const [isPrintingKOT, setIsPrintingKOT] = useState(false);
  const [includeGST, setIncludeGST] = useState(true); // GST toggle state
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('cash');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [gstSettings, setGSTSettings] = useState<GSTSettings>(
    gstSettingsManager.getGSTSettings()
  );

  useEffect(() => {
    if (tableId) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (activeKOT) {
        setTableData({
          id: tableId,
          capacity: 4, // Default capacity
          guests: activeKOT.items.length,
          status: "occupied",
          orderTime: new Date(activeKOT.createdAt).toLocaleTimeString('en-IN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }),
          kotNumber: activeKOT.kotNumber,
          orders: activeKOT.items.map((item: any, index: number) => ({
            id: index + 1,
            name: item.name,
            quantity: item.quantity,
            price: item.price,
            status: "served" // Default status
          })),
          totalAmount: activeKOT.totalAmount,
          createdAt: activeKOT.createdAt,
          versions: activeKOT.versions
        });
      } else {
        // No active KOT found, redirect back
        navigate("/admin/tables");
      }
    }
  }, [tableId, navigate]);

  // Listen for GST settings changes
  useEffect(() => {
    const handleGSTSettingsChange = (event: CustomEvent<GSTSettings>) => {
      setGSTSettings(event.detail);
    };

    window.addEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);

    return () => {
      window.removeEventListener('gstSettingsChanged', handleGSTSettingsChange as EventListener);
    };
  }, []);



  const getTotalAmount = () => {
    return tableData?.totalAmount || 0;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "served": return "bg-green-100 text-green-700";
      case "preparing": return "bg-yellow-100 text-yellow-700";
      case "pending": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const handleCompleteOrder = () => {
    if (tableData && tableData.kotNumber) {
      kotStorage.completeKOT(tableData.kotNumber);
      toast({
        title: "Order Completed!",
        description: `Table ${tableId} order has been completed successfully`,
      });
      navigate("/admin/tables");
    }
  };

  const handleViewFullKOT = () => {
    if (tableData) {
      // Get the actual KOT from storage
      const actualKOT = kotStorage.getActiveKOTForTable(tableId);
      if (actualKOT) {
        navigate(`/admin/kot/${tableId}`, {
          state: {
            existingKOT: actualKOT,
            viewMode: true,
            tableId: tableId
          }
        });
      } else {
        toast({
          title: "Error",
          description: "No active KOT found for this table",
          variant: "destructive",
        });
      }
    }
  };

  const handlePrintBill = () => {
    setShowCustomerDialog(true);
  };

  const handlePrintKOT = async () => {
    if (!tableId) return;

    setIsPrintingKOT(true);

    try {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (!activeKOT) {
        toast({
          title: "No Active KOT",
          description: "No active KOT found for this table.",
          variant: "destructive"
        });
        return;
      }

      // Print the KOT using unified print service
      const success = await PrintService.printKOT(activeKOT);

      if (success) {
        toast({
          title: "KOT Printed Successfully!",
          description: `KOT #${activeKOT.kotNumber} printed for Table ${tableId}`,
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      toast({
        title: "Print Error",
        description: "Failed to print KOT. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsPrintingKOT(false);
    }
  };

  const generateKOTContent = (kot: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>KOT #${kot.kotNumber}</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
          }
          .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
          }
          .restaurant-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .kot-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-weight: bold;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
          }
          th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: #f0f0f0;
            font-weight: bold;
          }
          .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
          }
          .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
          }
          @media print {
            body { margin: 0; padding: 10px; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="restaurant-name">🥢 Wok Ka Tadka</div>
          <div>Mumbai Style Chinese & Indian</div>
          <div style="margin-top: 10px; font-size: 16px; font-weight: bold;">KITCHEN ORDER TICKET</div>
        </div>

        <div class="kot-info">
          <div>KOT #: ${kot.kotNumber}</div>
          <div>Table: ${kot.tableId}</div>
        </div>

        <div class="kot-info">
          <div>Date: ${currentDate}</div>
          <div>Time: ${currentTime}</div>
        </div>

        <table>
          <thead>
            <tr>
              <th>Item</th>
              <th>Qty</th>
              <th>Special Notes</th>
            </tr>
          </thead>
          <tbody>
            ${kot.items.map((item: any) => `
              <tr>
                <td>${item.name} ${item.veg ? '🟢' : '🔴'}</td>
                <td>${item.quantity}</td>
                <td>${item.specialInstructions || '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        ${kot.specialInstructions ? `
          <div style="margin-bottom: 15px;">
            <strong>Special Instructions:</strong><br>
            ${kot.specialInstructions}
          </div>
        ` : ''}

        <div class="footer">
          <p><strong>Total Items: ${kot.items.reduce((sum: number, item: any) => sum + item.quantity, 0)}</strong></p>
          <p>Please prepare items as per order</p>
          <p>Printed on: ${currentDate} ${currentTime}</p>
        </div>
      </body>
      </html>
    `;
  };

  const handleCustomerSelection = (customer: Customer | null) => {
    setShowCustomerDialog(false);
    setShowPaymentMethodDialog(true);
    setSelectedCustomer(customer);
  };

  const handlePaymentMethodSelection = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setShowPaymentMethodDialog(false);
    printBill(selectedCustomer, paymentMethod);
  };

  const printBill = async (customer: Customer | null, paymentMethod: PaymentMethod = 'cash') => {
    if (!tableId) return;

    setIsPrintingBill(true);

    try {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      if (!activeKOT) {
        toast({
          title: "No Active KOT",
          description: "No active KOT found for this table to generate bill.",
          variant: "destructive"
        });
        return;
      }

      // Add customer order to records if customer is selected
      if (customer) {
        customerManager.addCustomerOrder({
          customerId: customer.id,
          orderId: activeKOT.kotNumber,
          tableId: tableId,
          kotNumber: activeKOT.kotNumber,
          amount: activeKOT.totalAmount,
          items: activeKOT.items.map(item => ({
            name: item.name,
            quantity: item.quantity,
            price: item.price
          }))
        });
      }

      // Print bill using unified print service
      const success = await PrintService.printBill(activeKOT, customer, {
        includeGST,
        paymentMethod
      });

      if (success) {
        toast({
          title: "Bill Printed Successfully!",
          description: customer
            ? `Bill printed for ${customer.name} - Table ${tableId}`
            : `Bill printed for Table ${tableId}`,
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      toast({
        title: "Print Error",
        description: "Failed to print bill. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsPrintingBill(false);
    }
  };

  const generateBillContent = (kot: any, customer: Customer | null, includeGST: boolean = true): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Bill - Table ${tableId}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 20px; }
          .restaurant-name { font-size: 24px; font-weight: bold; }
          .bill-info { margin: 20px 0; }
          .customer-info { margin: 10px 0; padding: 10px; background-color: #f5f5f5; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .total-row { font-weight: bold; background-color: #f9f9f9; }
          .footer { margin-top: 30px; text-align: center; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="restaurant-name">Wok Ka Tadka</div>
          <div>Mumbai Style Chinese & Indian</div>
          <div>Phone: +91 9876543210</div>
        </div>

        <div class="bill-info">
          <strong>Bill No:</strong> ${kot.kotNumber}<br>
          <strong>Table:</strong> ${tableId}<br>
          <strong>Date:</strong> ${currentDate}<br>
          <strong>Time:</strong> ${currentTime}
        </div>

        ${customer ? `
          <div class="customer-info">
            <strong>Customer Details:</strong><br>
            <strong>Name:</strong> ${customer.name}<br>
            ${customer.phone ? `<strong>Phone:</strong> ${customer.phone}<br>` : ''}
            ${customer.email ? `<strong>Email:</strong> ${customer.email}<br>` : ''}
            ${customer.isRegular ? '<strong>Status:</strong> Regular Customer ⭐<br>' : ''}
          </div>
        ` : ''}

        <table>
          <thead>
            <tr>
              <th>Item</th>
              <th>Qty</th>
              <th>Price</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            ${(() => {
              // Aggregate all items from all KOT versions to handle multiple orders
              const aggregatedItems = new Map();

              // Process all items from all versions
              kot.items.forEach((item: any) => {
                const key = `${item.name}_${item.price}`;
                if (aggregatedItems.has(key)) {
                  const existing = aggregatedItems.get(key);
                  existing.quantity += item.quantity;
                } else {
                  aggregatedItems.set(key, {
                    name: item.name,
                    quantity: item.quantity,
                    price: item.price,
                    veg: item.veg
                  });
                }
              });

              return Array.from(aggregatedItems.values()).map((item: any) => `
                <tr>
                  <td>${item.name} ${item.veg ? '🟢' : '🔴'}</td>
                  <td>${item.quantity}</td>
                  <td>₹${item.price}</td>
                  <td>₹${item.price * item.quantity}</td>
                </tr>
              `).join('');
            })()}
            <tr>
              <td colspan="3"><strong>Subtotal</strong></td>
              <td><strong>₹${kot.totalAmount.toFixed(2)}</strong></td>
            </tr>
            ${includeGST ? `
              <tr>
                <td colspan="3"><strong>CGST (${gstSettings.cgstRate}%)</strong></td>
                <td><strong>₹${(kot.totalAmount * gstSettings.cgstRate / 100).toFixed(2)}</strong></td>
              </tr>
              <tr>
                <td colspan="3"><strong>SGST (${gstSettings.sgstRate}%)</strong></td>
                <td><strong>₹${(kot.totalAmount * gstSettings.sgstRate / 100).toFixed(2)}</strong></td>
              </tr>
            ` : `
              <tr style="color: #f59e0b;">
                <td colspan="3"><strong>GST (Excluded)</strong></td>
                <td><strong>₹0.00</strong></td>
              </tr>
            `}
            <tr class="total-row" style="background-color: #e5f3ff; font-size: 16px;">
              <td colspan="3"><strong>Grand Total ${includeGST ? '(incl. GST)' : '(excl. GST)'}</strong></td>
              <td><strong>₹${(kot.totalAmount * (includeGST ? (1 + gstSettings.totalGSTRate / 100) : 1)).toFixed(2)}</strong></td>
            </tr>
          </tbody>
        </table>

        ${kot.versions && kot.versions.length > 1 ? `
          <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff;">
            <strong>Order History (${kot.versions.length} separate orders):</strong><br>
            ${kot.versions.map((version: any, index: number) => `
              <div style="margin: 8px 0; padding: 8px; background-color: white; border-radius: 4px;">
                <strong>Order #${index + 1}</strong> - ${new Date(version.addedAt).toLocaleString('en-IN')}<br>
                <small style="color: #666;">
                  ${version.addedItems.map((item: any) => `${item.name} (${item.quantity})`).join(', ')}
                  ${version.specialInstructions ? `<br><em>Instructions: ${version.specialInstructions}</em>` : ''}
                </small>
              </div>
            `).join('')}
          </div>
        ` : ''}

        <div class="footer">
          <p>Thank you for dining with us!</p>
          <p>Visit us again soon!</p>
        </div>
      </body>
      </html>
    `;
  };

  if (!tableData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading table details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/admin/tables")}
                className="flex items-center gap-1 sm:gap-2 shrink-0"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <Logo className="h-6 sm:h-8 shrink-0" />
              <div className="min-w-0 flex-1">
                <h1 className="text-base sm:text-lg font-bold text-gray-900 truncate">Table {tableId}</h1>
                <p className="text-xs sm:text-sm text-gray-600 truncate">Order Details</p>
              </div>
            </div>
            <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs sm:text-sm shrink-0">
              KOT #{tableData.kotNumber}
            </Badge>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 space-y-4 sm:space-y-6">
        {/* Table Info */}
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className="bg-blue-100 p-2 rounded-lg shrink-0">
                  <Users className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-gray-600">Items Ordered</p>
                  <p className="font-semibold text-sm sm:text-base truncate">{tableData.guests} items</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="bg-green-100 p-2 rounded-lg shrink-0">
                  <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-gray-600">Order Time</p>
                  <p className="font-semibold text-sm sm:text-base truncate">{tableData.orderTime}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Orders */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base sm:text-lg flex items-center gap-2">
              <Utensils className="h-4 w-4 sm:h-5 sm:w-5" />
              Current Orders
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {tableData.orders.map((order: any) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1 min-w-0 pr-3">
                  <h4 className="font-medium text-gray-900 text-sm sm:text-base truncate">{order.name}</h4>
                  <p className="text-xs sm:text-sm text-gray-600">Qty: {order.quantity} × ₹{order.price}</p>
                </div>
                <div className="text-right shrink-0">
                  <Badge className={`${getStatusColor(order.status)} text-xs mb-1`}>
                    {order.status}
                  </Badge>
                  <p className="font-semibold text-blue-600 text-sm sm:text-base">₹{order.price * order.quantity}</p>
                </div>
              </div>
            ))}

            <div className="border-t pt-3 mt-4">
              <div className="flex justify-between items-center text-base sm:text-lg font-bold">
                <span>Total Amount:</span>
                <span className="text-blue-600">₹{getTotalAmount()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            variant="default"
            size="lg"
            className="w-full h-12 sm:h-14 bg-blue-600 hover:bg-blue-700 text-sm sm:text-base"
            onClick={() => navigate(`/admin/take-order?table=${tableId}&dine-in=true`)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add More Items
          </Button>

          {/* GST Toggle */}
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calculator className="h-4 w-4 text-yellow-600" />
                <div>
                  <span className="font-medium text-sm text-gray-900">GST (CGST + SGST)</span>
                  <p className="text-xs text-gray-600">Include {gstSettings.totalGSTRate}% GST in bill</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-xs font-medium ${includeGST ? 'text-green-600' : 'text-gray-500'}`}>
                  {includeGST ? 'With GST' : 'Without GST'}
                </span>
                <Switch
                  checked={includeGST}
                  onCheckedChange={setIncludeGST}
                  className="data-[state=checked]:bg-green-600"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            <Button
              variant="outline"
              size="lg"
              className="h-12 sm:h-14 text-xs sm:text-sm"
              onClick={handlePrintKOT}
              disabled={isPrintingKOT}
            >
              {isPrintingKOT ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Printing...</span>
                  <span className="sm:hidden">Print...</span>
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">KOT</span>
                  <span className="sm:hidden">KOT</span>
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="h-12 sm:h-14 text-xs sm:text-sm"
              onClick={handlePrintBill}
              disabled={isPrintingBill}
            >
              {isPrintingBill ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Printing...</span>
                  <span className="sm:hidden">Print...</span>
                </>
              ) : (
                <>
                  <Receipt className="h-4 w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">Print Bill</span>
                  <span className="sm:hidden">Print</span>
                </>
              )}
            </Button>
            <Button
              variant="default"
              size="lg"
              className="h-12 sm:h-14 bg-green-600 hover:bg-green-700 text-xs sm:text-sm"
              onClick={handleCompleteOrder}
            >
              <CheckCircle className="h-4 w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Complete</span>
              <span className="sm:hidden">Done</span>
            </Button>
          </div>
        </div>

        {/* Order Timeline */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Order Timeline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Order Placed</p>
                <p className="text-sm text-gray-600">{tableData.orderTime}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">KOT Sent to Kitchen</p>
                <p className="text-sm text-gray-600">{tableData.orderTime}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">Food Being Prepared</p>
                <p className="text-sm text-gray-600">In Progress</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <p className="font-medium text-gray-400">Ready to Serve</p>
                <p className="text-sm text-gray-400">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Selection Dialog */}
        <CustomerSelectionDialog
          isOpen={showCustomerDialog}
          onClose={() => setShowCustomerDialog(false)}
          onCustomerSelect={handleCustomerSelection}
          title="Select Customer for Bill"
          description="Choose a customer for this bill or skip to print without customer details"
        />

        {/* Payment Method Selection Dialog */}
        <PaymentMethodDialog
          open={showPaymentMethodDialog}
          onClose={() => setShowPaymentMethodDialog(false)}
          onSelect={handlePaymentMethodSelection}
          title="Select Payment Method"
        />
      </div>
    </div>
  );
};

export default AdminTableDetails;
