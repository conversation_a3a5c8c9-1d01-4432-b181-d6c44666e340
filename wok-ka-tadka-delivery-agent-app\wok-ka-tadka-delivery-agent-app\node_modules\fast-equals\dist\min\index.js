!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self)["fast-equals"]={})}(this,(function(e){"use strict";var r=Object.getOwnPropertyNames,t=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty;function n(e,r){return function(t,a,n){return e(t,a,n)&&r(t,a,n)}}function u(e){return function(r,t,a){if(!r||!t||"object"!=typeof r||"object"!=typeof t)return e(r,t,a);var n=a.cache,u=n.get(r),o=n.get(t);if(u&&o)return u===t&&o===r;n.set(r,t),n.set(t,r);var i=e(r,t,a);return n.delete(r),n.delete(t),i}}function o(e){return r(e).concat(t(e))}var i=Object.hasOwn||function(e,r){return a.call(e,r)};function c(e,r){return e||r?e===r:e===r||e!=e&&r!=r}var l="_owner",f=Object.getOwnPropertyDescriptor,s=Object.keys;function p(e,r,t){var a=e.length;if(r.length!==a)return!1;for(;a-- >0;)if(!t.equals(e[a],r[a],a,a,e,r,t))return!1;return!0}function q(e,r){return c(e.getTime(),r.getTime())}function v(e,r,t){if(e.size!==r.size)return!1;for(var a,n,u={},o=e.entries(),i=0;(a=o.next())&&!a.done;){for(var c=r.entries(),l=!1,f=0;(n=c.next())&&!n.done;){var s=a.value,p=s[0],q=s[1],v=n.value,y=v[0],b=v[1];l||u[f]||!(l=t.equals(p,y,i,f,e,r,t)&&t.equals(q,b,p,y,e,r,t))||(u[f]=!0),f++}if(!l)return!1;i++}return!0}function y(e,r,t){var a,n=s(e),u=n.length;if(s(r).length!==u)return!1;for(;u-- >0;){if((a=n[u])===l&&(e.$$typeof||r.$$typeof)&&e.$$typeof!==r.$$typeof)return!1;if(!i(r,a)||!t.equals(e[a],r[a],a,a,e,r,t))return!1}return!0}function b(e,r,t){var a,n,u,c=o(e),s=c.length;if(o(r).length!==s)return!1;for(;s-- >0;){if((a=c[s])===l&&(e.$$typeof||r.$$typeof)&&e.$$typeof!==r.$$typeof)return!1;if(!i(r,a))return!1;if(!t.equals(e[a],r[a],a,a,e,r,t))return!1;if(n=f(e,a),u=f(r,a),(n||u)&&(!n||!u||n.configurable!==u.configurable||n.enumerable!==u.enumerable||n.writable!==u.writable))return!1}return!0}function E(e,r){return c(e.valueOf(),r.valueOf())}function g(e,r){return e.source===r.source&&e.flags===r.flags}function j(e,r,t){if(e.size!==r.size)return!1;for(var a,n,u={},o=e.values();(a=o.next())&&!a.done;){for(var i=r.values(),c=!1,l=0;(n=i.next())&&!n.done;)c||u[l]||!(c=t.equals(a.value,n.value,a.value,n.value,e,r,t))||(u[l]=!0),l++;if(!c)return!1}return!0}function d(e,r){var t=e.length;if(r.length!==t)return!1;for(;t-- >0;)if(e[t]!==r[t])return!1;return!0}var m="[object Arguments]",h="[object Boolean]",O="[object Date]",w="[object Map]",S="[object Number]",$="[object Object]",A="[object RegExp]",x="[object Set]",C="[object String]",D=Array.isArray,M="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,P=Object.assign,T=Object.prototype.toString.call.bind(Object.prototype.toString);var I=Z(),z=Z({strict:!0}),B=Z({circular:!0}),R=Z({circular:!0,strict:!0}),W=Z({createInternalComparator:function(){return c}}),k=Z({strict:!0,createInternalComparator:function(){return c}}),V=Z({circular:!0,createInternalComparator:function(){return c}}),N=Z({circular:!0,createInternalComparator:function(){return c},strict:!0});function Z(e){void 0===e&&(e={});var r,t=e.circular,a=void 0!==t&&t,o=e.createInternalComparator,i=e.createState,c=e.strict,l=void 0!==c&&c,f=function(e){var r=e.circular,t=e.createCustomConfig,a=e.strict,o={areArraysEqual:a?b:p,areDatesEqual:q,areMapsEqual:a?n(v,b):v,areObjectsEqual:a?b:y,arePrimitiveWrappersEqual:E,areRegExpsEqual:g,areSetsEqual:a?n(j,b):j,areTypedArraysEqual:a?b:d};if(t&&(o=P({},o,t(o))),r){var i=u(o.areArraysEqual),c=u(o.areMapsEqual),l=u(o.areObjectsEqual),f=u(o.areSetsEqual);o=P({},o,{areArraysEqual:i,areMapsEqual:c,areObjectsEqual:l,areSetsEqual:f})}return o}(e),s=function(e){var r=e.areArraysEqual,t=e.areDatesEqual,a=e.areMapsEqual,n=e.areObjectsEqual,u=e.arePrimitiveWrappersEqual,o=e.areRegExpsEqual,i=e.areSetsEqual,c=e.areTypedArraysEqual;return function(e,l,f){if(e===l)return!0;if(null==e||null==l||"object"!=typeof e||"object"!=typeof l)return e!=e&&l!=l;var s=e.constructor;if(s!==l.constructor)return!1;if(s===Object)return n(e,l,f);if(D(e))return r(e,l,f);if(null!=M&&M(e))return c(e,l,f);if(s===Date)return t(e,l,f);if(s===RegExp)return o(e,l,f);if(s===Map)return a(e,l,f);if(s===Set)return i(e,l,f);var p=T(e);return p===O?t(e,l,f):p===A?o(e,l,f):p===w?a(e,l,f):p===x?i(e,l,f):p===$?"function"!=typeof e.then&&"function"!=typeof l.then&&n(e,l,f):p===m?n(e,l,f):(p===h||p===S||p===C)&&u(e,l,f)}}(f);return function(e){var r=e.circular,t=e.comparator,a=e.createState,n=e.equals,u=e.strict;if(a)return function(e,o){var i=a(),c=i.cache,l=void 0===c?r?new WeakMap:void 0:c,f=i.meta;return t(e,o,{cache:l,equals:n,meta:f,strict:u})};if(r)return function(e,r){return t(e,r,{cache:new WeakMap,equals:n,meta:void 0,strict:u})};var o={cache:void 0,equals:n,meta:void 0,strict:u};return function(e,r){return t(e,r,o)}}({circular:a,comparator:s,createState:i,equals:o?o(s):(r=s,function(e,t,a,n,u,o,i){return r(e,t,i)}),strict:l})}e.circularDeepEqual=B,e.circularShallowEqual=V,e.createCustomEqual=Z,e.deepEqual=I,e.sameValueZeroEqual=c,e.shallowEqual=W,e.strictCircularDeepEqual=R,e.strictCircularShallowEqual=N,e.strictDeepEqual=z,e.strictShallowEqual=k}));
