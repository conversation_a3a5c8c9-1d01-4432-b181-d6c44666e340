import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ting<PERSON>, <PERSON>, Key, Clock, Save, TestTube } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  PlatformConfig,
  multiPlatformOrderManager 
} from '@/utils/multiPlatformOrders';

const MultiPlatformSettings = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [configs, setConfigs] = useState<PlatformConfig[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = () => {
    const platformConfigs = multiPlatformOrderManager.getPlatformConfigs();
    setConfigs(platformConfigs);
  };

  const handleConfigUpdate = (platform: string, updates: Partial<PlatformConfig>) => {
    setConfigs(prevConfigs =>
      prevConfigs.map(config =>
        config.platform === platform
          ? { ...config, ...updates }
          : config
      )
    );
  };

  const handleCredentialUpdate = (platform: string, key: string, value: string) => {
    setConfigs(prevConfigs =>
      prevConfigs.map(config =>
        config.platform === platform
          ? {
              ...config,
              credentials: {
                ...config.credentials,
                [key]: value
              }
            }
          : config
      )
    );
  };

  const handleNotificationUpdate = (platform: string, key: string, value: boolean) => {
    setConfigs(prevConfigs =>
      prevConfigs.map(config =>
        config.platform === platform
          ? {
              ...config,
              notifications: {
                ...config.notifications,
                [key]: value
              }
            }
          : config
      )
    );
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      // Save each platform configuration
      for (const config of configs) {
        multiPlatformOrderManager.updatePlatformConfig(config.platform as any, config);
      }
      
      toast({
        title: "Settings Saved",
        description: "Platform configurations have been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async (platform: string) => {
    toast({
      title: "Testing Connection",
      description: `Testing ${platform} API connection...`,
    });

    // Simulate API test
    setTimeout(() => {
      toast({
        title: "Connection Test",
        description: `${platform} connection test completed. Check console for details.`,
      });
    }, 2000);
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'own-app':
        return '🏪';
      default:
        return '📱';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'own-app':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b apk-header">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/admin/multi-platform-orders')}
                className="shrink-0"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-gray-900">Platform Settings</h1>
                <p className="text-xs sm:text-sm text-gray-600">Configure restaurant app integrations</p>
              </div>
            </div>
            <Button
              onClick={handleSaveSettings}
              disabled={isSaving}
              className="bg-primary hover:bg-primary/90"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 apk-content-with-header space-y-4 sm:space-y-6">
        {/* Platform Configuration Cards */}
        <div className="space-y-6">
          {configs.map((config) => (
            <Card key={config.platform} className={`${getPlatformColor(config.platform)} border-2`}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getPlatformIcon(config.platform)}</span>
                    <div>
                      <h3 className="text-lg font-bold capitalize">{config.platform.replace('-', ' ')}</h3>
                      <p className="text-sm text-gray-600 font-normal">
                        {config.platform === 'own-app' && 'Restaurant\'s own mobile app'}
                      </p>
                    </div>
                  </div>
                  <Switch
                    checked={config.isEnabled}
                    onCheckedChange={(checked) => 
                      handleConfigUpdate(config.platform, { isEnabled: checked })
                    }
                  />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <Tabs defaultValue="credentials" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="credentials">
                      <Key className="h-4 w-4 mr-2" />
                      Credentials
                    </TabsTrigger>
                    <TabsTrigger value="notifications">
                      <Bell className="h-4 w-4 mr-2" />
                      Notifications
                    </TabsTrigger>
                    <TabsTrigger value="settings">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="credentials" className="space-y-4">
                    {config.platform !== 'own-app' && (
                      <>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`${config.platform}-api-key`}>API Key</Label>
                            <Input
                              id={`${config.platform}-api-key`}
                              type="password"
                              placeholder="Enter API key"
                              value={config.credentials.apiKey || ''}
                              onChange={(e) => 
                                handleCredentialUpdate(config.platform, 'apiKey', e.target.value)
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor={`${config.platform}-secret-key`}>Secret Key</Label>
                            <Input
                              id={`${config.platform}-secret-key`}
                              type="password"
                              placeholder="Enter secret key"
                              value={config.credentials.secretKey || ''}
                              onChange={(e) => 
                                handleCredentialUpdate(config.platform, 'secretKey', e.target.value)
                              }
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`${config.platform}-restaurant-id`}>Restaurant ID</Label>
                            <Input
                              id={`${config.platform}-restaurant-id`}
                              placeholder="Enter restaurant ID"
                              value={config.credentials.restaurantId || ''}
                              onChange={(e) => 
                                handleCredentialUpdate(config.platform, 'restaurantId', e.target.value)
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor={`${config.platform}-webhook-url`}>Webhook URL</Label>
                            <Input
                              id={`${config.platform}-webhook-url`}
                              placeholder="Enter webhook URL"
                              value={config.credentials.webhookUrl || ''}
                              onChange={(e) => 
                                handleCredentialUpdate(config.platform, 'webhookUrl', e.target.value)
                              }
                            />
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => handleTestConnection(config.platform)}
                          className="w-full sm:w-auto"
                        >
                          <TestTube className="h-4 w-4 mr-2" />
                          Test Connection
                        </Button>
                      </>
                    )}
                    {config.platform === 'own-app' && (
                      <div className="text-center py-8">
                        <div className="text-4xl mb-4">🏪</div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Own App Integration</h3>
                        <p className="text-gray-600">
                          This platform represents orders from your restaurant's own mobile app.
                          No external credentials required.
                        </p>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="notifications" className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor={`${config.platform}-new-order`}>New Order Notifications</Label>
                          <p className="text-sm text-gray-600">Get notified when new orders arrive</p>
                        </div>
                        <Switch
                          id={`${config.platform}-new-order`}
                          checked={config.notifications.newOrder}
                          onCheckedChange={(checked) => 
                            handleNotificationUpdate(config.platform, 'newOrder', checked)
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor={`${config.platform}-status-update`}>Status Update Notifications</Label>
                          <p className="text-sm text-gray-600">Get notified when order status changes</p>
                        </div>
                        <Switch
                          id={`${config.platform}-status-update`}
                          checked={config.notifications.statusUpdate}
                          onCheckedChange={(checked) => 
                            handleNotificationUpdate(config.platform, 'statusUpdate', checked)
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor={`${config.platform}-sound-alert`}>Sound Alerts</Label>
                          <p className="text-sm text-gray-600">Play sound when notifications arrive</p>
                        </div>
                        <Switch
                          id={`${config.platform}-sound-alert`}
                          checked={config.notifications.soundAlert}
                          onCheckedChange={(checked) => 
                            handleNotificationUpdate(config.platform, 'soundAlert', checked)
                          }
                        />
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="settings" className="space-y-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor={`${config.platform}-auto-accept`}>Auto Accept Orders</Label>
                          <p className="text-sm text-gray-600">Automatically accept incoming orders</p>
                        </div>
                        <Switch
                          id={`${config.platform}-auto-accept`}
                          checked={config.autoAccept}
                          onCheckedChange={(checked) => 
                            handleConfigUpdate(config.platform, { autoAccept: checked })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor={`${config.platform}-prep-time`}>
                          <Clock className="h-4 w-4 inline mr-2" />
                          Default Preparation Time (minutes)
                        </Label>
                        <Input
                          id={`${config.platform}-prep-time`}
                          type="number"
                          min="5"
                          max="120"
                          value={config.preparationTime}
                          onChange={(e) => 
                            handleConfigUpdate(config.platform, { 
                              preparationTime: parseInt(e.target.value) || 15 
                            })
                          }
                          className="w-32"
                        />
                        <p className="text-sm text-gray-600 mt-1">
                          Time communicated to {config.platform} for order preparation
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Integration Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Integration Guide
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">🏪 Own App Integration</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Configure your restaurant's mobile app settings</li>
                <li>• Set up notification preferences for new orders</li>
                <li>• Adjust preparation times based on your kitchen capacity</li>
                <li>• Enable auto-accept for streamlined order processing</li>
                <li>• Test order flow to ensure smooth operations</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MultiPlatformSettings;
