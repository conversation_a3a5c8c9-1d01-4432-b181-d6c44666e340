// Test utility for attendance system
import {
  markCheckIn,
  markCheckOut,
  getTodayAttendance,
  getStaffAttendanceRecords,
  getStaffAttendanceStats,
  canCheckIn,
  canCheckOut,
  formatTime,
  formatDate
} from './attendanceStorage';

export const testAttendanceSystem = () => {
  console.log('🧪 Testing Attendance System...');
  
  const testStaffId = 'test-staff-001';
  const testStaffName = 'Test Waiter';
  const testStaffPhone = '9876543210';
  
  try {
    // Test 1: Check if can check in (should be true initially)
    const canCheckInInitially = canCheckIn(testStaffId);
    console.log(`✅ Can check in initially: ${canCheckInInitially}`);
    
    // Test 2: Mark check in
    if (canCheckInInitially) {
      const checkInRecord = markCheckIn(testStaffId, testStaffName, testStaffPhone, 'Test check-in');
      console.log(`✅ Check-in successful:`, checkInRecord);
      
      // Test 3: Verify today's attendance
      const todayAttendance = getTodayAttendance(testStaffId);
      console.log(`✅ Today's attendance:`, todayAttendance);
      
      // Test 4: Check if can check in again (should be false)
      const canCheckInAgain = canCheckIn(testStaffId);
      console.log(`✅ Can check in again: ${canCheckInAgain} (should be false)`);
      
      // Test 5: Check if can check out (should be true)
      const canCheckOutNow = canCheckOut(testStaffId);
      console.log(`✅ Can check out: ${canCheckOutNow}`);
      
      // Wait a moment to simulate work time
      setTimeout(() => {
        try {
          // Test 6: Mark check out
          const checkOutRecord = markCheckOut(testStaffId, 'Test check-out');
          console.log(`✅ Check-out successful:`, checkOutRecord);
          
          // Test 7: Get staff attendance records
          const records = getStaffAttendanceRecords(testStaffId, 10);
          console.log(`✅ Staff attendance records (${records.length} found):`, records);
          
          // Test 8: Get attendance statistics
          const stats = getStaffAttendanceStats(testStaffId, 30);
          console.log(`✅ Attendance statistics:`, stats);
          
          // Test 9: Test formatting functions
          const now = new Date().toISOString();
          const today = new Date().toISOString().split('T')[0];
          console.log(`✅ Format time: ${formatTime(now)}`);
          console.log(`✅ Format date: ${formatDate(today)}`);
          
          console.log('🎉 All attendance system tests passed!');
          
        } catch (error) {
          console.error('❌ Check-out test failed:', error);
        }
      }, 1000);
      
    } else {
      console.log('ℹ️ Staff already checked in today, testing check-out functionality...');
      
      if (canCheckOut(testStaffId)) {
        const checkOutRecord = markCheckOut(testStaffId, 'Test check-out');
        console.log(`✅ Check-out successful:`, checkOutRecord);
      } else {
        console.log('ℹ️ Cannot check out - either not checked in or already checked out');
      }
    }
    
  } catch (error) {
    console.error('❌ Attendance system test failed:', error);
  }
};

// Function to clear test data
export const clearTestAttendanceData = () => {
  const testStaffId = 'test-staff-001';
  
  // Get all records and filter out test data
  const allRecords = JSON.parse(localStorage.getItem('wok_ka_tadka_attendance') || '[]');
  const filteredRecords = allRecords.filter((record: any) => record.staffId !== testStaffId);
  
  localStorage.setItem('wok_ka_tadka_attendance', JSON.stringify(filteredRecords));
  console.log('🧹 Test attendance data cleared');
};

// Run test if this file is imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  console.log('📋 Attendance system utilities loaded. Run testAttendanceSystem() to test.');
}
