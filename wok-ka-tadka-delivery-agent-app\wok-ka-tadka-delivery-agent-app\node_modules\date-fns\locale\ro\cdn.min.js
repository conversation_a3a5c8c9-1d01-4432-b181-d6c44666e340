var x=function(G){return x=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(U){return typeof U}:function(U){return U&&typeof Symbol=="function"&&U.constructor===Symbol&&U!==Symbol.prototype?"symbol":typeof U},x(G)},$=function(G,U){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(G);U&&(I=I.filter(function(N){return Object.getOwnPropertyDescriptor(G,N).enumerable})),J.push.apply(J,I)}return J},K=function(G){for(var U=1;U<arguments.length;U++){var J=arguments[U]!=null?arguments[U]:{};U%2?$(Object(J),!0).forEach(function(I){X0(G,I,J[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):$(Object(J)).forEach(function(I){Object.defineProperty(G,I,Object.getOwnPropertyDescriptor(J,I))})}return G},X0=function(G,U,J){if(U=B0(U),U in G)Object.defineProperty(G,U,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[U]=J;return G},B0=function(G){var U=C0(G,"string");return x(U)=="symbol"?U:String(U)},C0=function(G,U){if(x(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var I=J.call(G,U||"default");if(x(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(U==="string"?String:Number)(G)};(function(G){var U=Object.defineProperty,J=function X(C,B){for(var H in B)U(C,H,{get:B[H],enumerable:!0,configurable:!0,set:function Y(Z){return B[H]=function(){return Z}}})},I={lessThanXSeconds:{one:"mai pu\u021Bin de o secund\u0103",other:"mai pu\u021Bin de {{count}} secunde"},xSeconds:{one:"1 secund\u0103",other:"{{count}} secunde"},halfAMinute:"jum\u0103tate de minut",lessThanXMinutes:{one:"mai pu\u021Bin de un minut",other:"mai pu\u021Bin de {{count}} minute"},xMinutes:{one:"1 minut",other:"{{count}} minute"},aboutXHours:{one:"circa 1 or\u0103",other:"circa {{count}} ore"},xHours:{one:"1 or\u0103",other:"{{count}} ore"},xDays:{one:"1 zi",other:"{{count}} zile"},aboutXWeeks:{one:"circa o s\u0103pt\u0103m\xE2n\u0103",other:"circa {{count}} s\u0103pt\u0103m\xE2ni"},xWeeks:{one:"1 s\u0103pt\u0103m\xE2n\u0103",other:"{{count}} s\u0103pt\u0103m\xE2ni"},aboutXMonths:{one:"circa 1 lun\u0103",other:"circa {{count}} luni"},xMonths:{one:"1 lun\u0103",other:"{{count}} luni"},aboutXYears:{one:"circa 1 an",other:"circa {{count}} ani"},xYears:{one:"1 an",other:"{{count}} ani"},overXYears:{one:"peste 1 an",other:"peste {{count}} ani"},almostXYears:{one:"aproape 1 an",other:"aproape {{count}} ani"}},N=function X(C,B,H){var Y,Z=I[C];if(typeof Z==="string")Y=Z;else if(B===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(B));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"\xEEn "+Y;else return Y+" \xEEn urm\u0103";return Y};function z(X){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},B=C.width?String(C.width):X.defaultWidth,H=X.formats[B]||X.formats[X.defaultWidth];return H}}var D={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd.MM.yyyy"},M={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'la' {{time}}",long:"{{date}} 'la' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},S={date:z({formats:D,defaultWidth:"full"}),time:z({formats:M,defaultWidth:"full"}),dateTime:z({formats:R,defaultWidth:"full"})},L={lastWeek:"eeee 'trecut\u0103 la' p",yesterday:"'ieri la' p",today:"'ast\u0103zi la' p",tomorrow:"'m\xE2ine la' p",nextWeek:"eeee 'viitoare la' p",other:"P"},V=function X(C,B,H,Y){return L[C]};function Q(X){return function(C,B){var H=B!==null&&B!==void 0&&B.context?String(B.context):"standalone",Y;if(H==="formatting"&&X.formattingValues){var Z=X.defaultFormattingWidth||X.defaultWidth,T=B!==null&&B!==void 0&&B.width?String(B.width):Z;Y=X.formattingValues[T]||X.formattingValues[Z]}else{var E=X.defaultWidth,A=B!==null&&B!==void 0&&B.width?String(B.width):X.defaultWidth;Y=X.values[A]||X.values[E]}var O=X.argumentCallback?X.argumentCallback(C):C;return Y[O]}}var j={narrow:["\xCE","D"],abbreviated:["\xCE.d.C.","D.C."],wide:["\xCEnainte de Cristos","Dup\u0103 Cristos"]},f={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["primul trimestru","al doilea trimestru","al treilea trimestru","al patrulea trimestru"]},v={narrow:["I","F","M","A","M","I","I","A","S","O","N","D"],abbreviated:["ian","feb","mar","apr","mai","iun","iul","aug","sep","oct","noi","dec"],wide:["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie"]},w={narrow:["d","l","m","m","j","v","s"],short:["du","lu","ma","mi","jo","vi","s\xE2"],abbreviated:["dum","lun","mar","mie","joi","vin","s\xE2m"],wide:["duminic\u0103","luni","mar\u021Bi","miercuri","joi","vineri","s\xE2mb\u0103t\u0103"]},_={narrow:{am:"a",pm:"p",midnight:"mn",noon:"ami",morning:"dim",afternoon:"da",evening:"s",night:"n"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"}},P={narrow:{am:"a",pm:"p",midnight:"mn",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nop\u021Bii",noon:"amiaz\u0103",morning:"diminea\u021B\u0103",afternoon:"dup\u0103-amiaz\u0103",evening:"sear\u0103",night:"noapte"}},F=function X(C,B){return String(C)},k={ordinalNumber:F,era:Q({values:j,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function X(C){return C-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:w,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function q(X){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=B.width,Y=H&&X.matchPatterns[H]||X.matchPatterns[X.defaultMatchWidth],Z=C.match(Y);if(!Z)return null;var T=Z[0],E=H&&X.parsePatterns[H]||X.parsePatterns[X.defaultParseWidth],A=Array.isArray(E)?b(E,function(W){return W.test(T)}):m(E,function(W){return W.test(T)}),O;O=X.valueCallback?X.valueCallback(A):A,O=B.valueCallback?B.valueCallback(O):O;var t=C.slice(T.length);return{value:O,rest:t}}}var m=function X(C,B){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&B(C[H]))return H;return},b=function X(C,B){for(var H=0;H<C.length;H++)if(B(C[H]))return H;return};function h(X){return function(C){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(X.matchPattern);if(!H)return null;var Y=H[0],Z=C.match(X.parsePattern);if(!Z)return null;var T=X.valueCallback?X.valueCallback(Z[0]):Z[0];T=B.valueCallback?B.valueCallback(T):T;var E=C.slice(Y.length);return{value:T,rest:E}}}var c=/^(\d+)?/i,y=/\d+/i,p={narrow:/^(Î|D)/i,abbreviated:/^(Î\.?\s?d\.?\s?C\.?|Î\.?\s?e\.?\s?n\.?|D\.?\s?C\.?|e\.?\s?n\.?)/i,wide:/^(Înainte de Cristos|Înaintea erei noastre|După Cristos|Era noastră)/i},u={any:[/^ÎC/i,/^DC/i],wide:[/^(Înainte de Cristos|Înaintea erei noastre)/i,/^(După Cristos|Era noastră)/i]},g={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^trimestrul [1234]/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[ifmaasond]/i,abbreviated:/^(ian|feb|mar|apr|mai|iun|iul|aug|sep|oct|noi|dec)/i,wide:/^(ianuarie|februarie|martie|aprilie|mai|iunie|iulie|august|septembrie|octombrie|noiembrie|decembrie)/i},i={narrow:[/^i/i,/^f/i,/^m/i,/^a/i,/^m/i,/^i/i,/^i/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ia/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^iun/i,/^iul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[dlmjvs]/i,short:/^(d|l|ma|mi|j|v|s)/i,abbreviated:/^(dum|lun|mar|mie|jo|vi|sâ)/i,wide:/^(duminica|luni|marţi|miercuri|joi|vineri|sâmbătă)/i},s={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^mi/i,/^j/i,/^v/i,/^s/i]},o={narrow:/^(a|p|mn|a|(dimineaţa|după-amiaza|seara|noaptea))/i,any:/^([ap]\.?\s?m\.?|miezul nopții|amiaza|(dimineaţa|după-amiaza|seara|noaptea))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/amiaza/i,morning:/dimineaţa/i,afternoon:/după-amiaza/i,evening:/seara/i,night:/noaptea/i}},e={ordinalNumber:h({matchPattern:c,parsePattern:y,valueCallback:function X(C){return parseInt(C,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function X(C){return C+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"ro",formatDistance:N,formatLong:S,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{ro:a})})})();

//# debugId=2D7C736B61D926E764756e2164756e21
