import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Filter, Search, Bell, RefreshCw, Settings, Eye, ShoppingCart, TrendingUp, DollarSign, AlertCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  MultiPlatformOrder,
  OrderPlatform,
  OrderStatus,
  multiPlatformOrderManager
} from '@/utils/multiPlatformOrders';
import { initializeSampleMultiPlatformOrders } from '@/utils/sampleMultiPlatformOrders';
import MultiPlatformNotifications from '@/components/MultiPlatformNotifications';

const MultiPlatformOrders = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [orders, setOrders] = useState<MultiPlatformOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<MultiPlatformOrder[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [notifications, setNotifications] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Load orders and notifications
  useEffect(() => {
    loadOrders();
    loadNotifications();
    
    // Set up periodic refresh
    const interval = setInterval(() => {
      loadOrders();
      loadNotifications();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Filter orders when search term or filters change
  useEffect(() => {
    filterOrders();
  }, [orders, searchTerm, selectedStatus]);

  const loadOrders = () => {
    const allOrders = multiPlatformOrderManager.getAllOrders();
    setOrders(allOrders);
  };

  const loadNotifications = () => {
    const unreadCount = multiPlatformOrderManager.getUnreadNotificationsCount();
    setNotifications(unreadCount);
  };

  const filterOrders = () => {
    let filtered = [...orders];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.platformOrderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customer.phone.includes(searchTerm)
      );
    }



    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(order => order.status === selectedStatus);
    }

    setFilteredOrders(filtered);
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      loadOrders();
      loadNotifications();
      toast({
        title: "Orders Refreshed",
        description: "Latest orders have been loaded successfully.",
      });
    } catch (error) {
      toast({
        title: "Refresh Failed",
        description: "Failed to refresh orders. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInitializeSampleData = () => {
    initializeSampleMultiPlatformOrders();
    loadOrders();
    loadNotifications();
    toast({
      title: "Sample Data Loaded",
      description: "Sample restaurant orders have been generated for testing.",
    });
  };

  const getPlatformColor = (platform: OrderPlatform) => {
    switch (platform) {
      case 'own-app':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'new':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'preparing':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'picked-up':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'delivered':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'cancelled':
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPlatformIcon = (platform: OrderPlatform) => {
    switch (platform) {
      case 'own-app':
        return '🏪'; // Restaurant icon
      default:
        return '📱';
    }
  };

  const getActiveOrdersCount = () => {
    return orders.filter(order => 
      !['delivered', 'cancelled', 'rejected'].includes(order.status)
    ).length;
  };

  const getTodayOrdersCount = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return orders.filter(order => 
      new Date(order.timestamps.orderPlaced) >= today
    ).length;
  };

  const getTodayRevenue = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return orders
      .filter(order => 
        new Date(order.timestamps.orderPlaced) >= today && 
        order.status === 'delivered'
      )
      .reduce((sum, order) => sum + order.pricing.total, 0);
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary text-white shadow-lg apk-header-fixed">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/admin/dashboard')}
                className="shrink-0 text-white hover:bg-white/20"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-lg sm:text-xl font-bold text-white">Restaurant Orders</h1>
                <p className="text-xs sm:text-sm text-white/80">Manage your restaurant orders</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <MultiPlatformNotifications
                currentPage="restaurant-orders"
                onNotificationClick={() => navigate('/admin/dashboard')}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/admin/multi-platform-settings')}
                className="hidden sm:flex text-white hover:bg-white/20"
              >
                <Settings className="h-4 w-4 mr-1" />
                Settings
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="relative text-white hover:bg-white/20"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                {notifications > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs bg-red-500">
                    {notifications}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 apk-content-with-header-no-gap space-y-4 sm:space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3">
          <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-102">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-blue-100 text-xs font-medium">Active Orders</p>
                  <p className="text-xl sm:text-2xl font-bold text-white">{getActiveOrdersCount()}</p>
                  <div className="flex items-center gap-1 mt-0.5">
                    <TrendingUp className="h-3 w-3 text-blue-200" />
                    <span className="text-xs text-blue-200">Live tracking</span>
                  </div>
                </div>
                <ShoppingCart className="h-6 w-6 sm:h-7 sm:w-7 text-blue-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-102">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-green-100 text-xs font-medium">Today's Orders</p>
                  <p className="text-xl sm:text-2xl font-bold text-white">{getTodayOrdersCount()}</p>
                  <div className="flex items-center gap-1 mt-0.5">
                    <TrendingUp className="h-3 w-3 text-green-200" />
                    <span className="text-xs text-green-200">+15% from yesterday</span>
                  </div>
                </div>
                <ShoppingCart className="h-6 w-6 sm:h-7 sm:w-7 text-green-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-102">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-purple-100 text-xs font-medium">Today's Revenue</p>
                  <p className="text-xl sm:text-2xl font-bold text-white">₹{getTodayRevenue()}</p>
                  <div className="flex items-center gap-1 mt-0.5">
                    <TrendingUp className="h-3 w-3 text-purple-200" />
                    <span className="text-xs text-purple-200">+8% from yesterday</span>
                  </div>
                </div>
                <DollarSign className="h-6 w-6 sm:h-7 sm:w-7 text-purple-200 shrink-0" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-102">
            <CardContent className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-orange-100 text-xs font-medium">Notifications</p>
                  <p className="text-xl sm:text-2xl font-bold text-white">{notifications}</p>
                  <div className="flex items-center gap-1 mt-0.5">
                    <AlertCircle className="h-3 w-3 text-orange-200" />
                    <span className="text-xs text-orange-200">Pending alerts</span>
                  </div>
                </div>
                <Bell className="h-6 w-6 sm:h-7 sm:w-7 text-orange-200 shrink-0" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by order ID, customer name, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="accepted">Accepted</SelectItem>
                  <SelectItem value="preparing">Preparing</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="picked-up">Picked Up</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Development Helper */}
        {process.env.NODE_ENV === 'development' && (
          <Card className="border-dashed border-2 border-gray-300">
            <CardContent className="p-4 text-center">
              <p className="text-sm text-gray-600 mb-2">Development Mode</p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleInitializeSampleData}
              >
                Generate Sample Orders
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Orders List */}
        <div className="space-y-3 sm:space-y-4">
          {filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <Bell className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Found</h3>
                <p className="text-gray-600">
                  {orders.length === 0 
                    ? "No orders available. Orders from Swiggy, Zomato, and your app will appear here."
                    : "No orders match your current filters. Try adjusting your search criteria."
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredOrders.map((order) => (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-4 gap-3 sm:gap-4">
                    <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                      <div className="text-2xl shrink-0">
                        {getPlatformIcon(order.platform)}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-bold text-base sm:text-lg">#{order.platformOrderId}</span>
                          <Badge className={`${getPlatformColor(order.platform)} text-xs`}>
                            {order.platform.toUpperCase()}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 truncate">{order.customer.name} • {order.customer.phone}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 sm:gap-3 shrink-0">
                      <Badge className={`${getStatusColor(order.status)} text-xs`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/multi-platform-order/${order.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Order Time</p>
                      <p className="font-medium">
                        {new Date(order.timestamps.orderPlaced).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Items</p>
                      <p className="font-medium">{order.items.length} items</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Total Amount</p>
                      <p className="font-bold text-primary">₹{order.pricing.total}</p>
                    </div>
                  </div>

                  {order.specialInstructions && (
                    <div className="mt-3 p-2 bg-yellow-50 rounded-lg">
                      <p className="text-xs text-yellow-800">
                        <strong>Special Instructions:</strong> {order.specialInstructions}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default MultiPlatformOrders;
