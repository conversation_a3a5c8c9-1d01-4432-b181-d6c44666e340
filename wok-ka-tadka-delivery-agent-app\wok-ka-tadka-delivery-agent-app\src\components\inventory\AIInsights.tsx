import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Lightbulb, 
  AlertTriangle, 
  TrendingUp, 
  RefreshCw, 
  Loader2,
  IndianRupee,
  Clock,
  CheckCircle
} from "lucide-react";
import { geminiAI, type AIInsight, type AIRecommendation } from "@/services/geminiAI";
import { InventoryItem, InventoryStats } from "@/utils/inventoryStorage";
import { useToast } from "@/hooks/use-toast";

interface AIInsightsProps {
  inventoryItems: InventoryItem[];
  inventoryStats: InventoryStats;
  onRefresh?: () => void;
}

export const AIInsights = ({ inventoryItems, inventoryStats, onRefresh }: AIInsightsProps) => {
  const [insights, setInsights] = useState<AIRecommendation | null>(null);
  const [wasteReduction, setWasteReduction] = useState<AIInsight[]>([]);
  const [stockOptimization, setStockOptimization] = useState<AIInsight[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { toast } = useToast();

  const loadAIInsights = async () => {
    if (inventoryItems.length === 0) return;
    
    setLoading(true);
    try {
      // Get comprehensive insights
      const [mainInsights, wasteInsights, optimizationInsights] = await Promise.all([
        geminiAI.getInventoryInsights(inventoryItems, inventoryStats),
        geminiAI.getWasteReductionSuggestions(inventoryItems),
        geminiAI.getStockOptimizationAdvice(inventoryItems)
      ]);

      setInsights(mainInsights);
      setWasteReduction(wasteInsights);
      setStockOptimization(optimizationInsights);
      setLastUpdated(new Date());

      toast({
        title: "AI Insights Updated",
        description: "Fresh recommendations generated successfully",
      });
    } catch (error) {
      console.error('Failed to load AI insights:', error);
      toast({
        title: "AI Insights Failed",
        description: error instanceof Error ? error.message : "Failed to generate insights",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAIInsights();
  }, [inventoryItems.length, inventoryStats.totalItems]);

  const getInsightIcon = (type: AIInsight['type']) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'suggestion': return <Lightbulb className="h-4 w-4" />;
      case 'optimization': return <TrendingUp className="h-4 w-4" />;
      case 'forecast': return <Brain className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: AIInsight['priority']) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatSavings = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const renderInsightCard = (insight: AIInsight, index: number) => (
    <Card key={insight.id || index} className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className={`p-2 rounded-full ${
            insight.type === 'warning' ? 'bg-red-100 text-red-600' :
            insight.type === 'suggestion' ? 'bg-blue-100 text-blue-600' :
            insight.type === 'optimization' ? 'bg-green-100 text-green-600' :
            'bg-purple-100 text-purple-600'
          }`}>
            {getInsightIcon(insight.type)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h4 className="font-semibold text-sm">{insight.title}</h4>
              <Badge className={`${getPriorityColor(insight.priority)} text-xs`}>
                {insight.priority}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 mb-2">{insight.description}</p>
            <div className="flex items-center justify-between">
              {insight.estimatedSavings && (
                <div className="flex items-center gap-1 text-green-600">
                  <IndianRupee className="h-3 w-3" />
                  <span className="text-xs font-medium">
                    Save {formatSavings(insight.estimatedSavings)}
                  </span>
                </div>
              )}
              {insight.actionable && (
                <Button size="sm" variant="outline" className="h-6 text-xs">
                  Take Action
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (inventoryItems.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No Inventory Data</h3>
          <p className="text-sm text-gray-500">Add inventory items to get AI-powered insights</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-500 to-blue-600 text-white">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Brain className="h-8 w-8" />
              <div>
                <CardTitle className="text-xl">AI-Powered Insights</CardTitle>
                <p className="text-purple-100 text-sm">
                  Smart recommendations to optimize your inventory
                </p>
              </div>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={loadAIInsights}
              disabled={loading}
              className="bg-white/20 hover:bg-white/30 text-white border-white/30"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Stats */}
      {insights && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Lightbulb className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
              <p className="text-2xl font-bold">{insights.recommendations.length}</p>
              <p className="text-sm text-gray-600">Recommendations</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <IndianRupee className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-2xl font-bold">{formatSavings(insights.totalPotentialSavings)}</p>
              <p className="text-sm text-gray-600">Potential Savings</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Clock className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <p className="text-sm font-medium">
                {lastUpdated ? lastUpdated.toLocaleTimeString() : 'Never'}
              </p>
              <p className="text-sm text-gray-600">Last Updated</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Insights */}
      {insights && insights.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-600" />
              General Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {insights.recommendations.map((insight, index) => renderInsightCard(insight, index))}
          </CardContent>
        </Card>
      )}

      {/* Waste Reduction */}
      {wasteReduction.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Waste Reduction Opportunities
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {wasteReduction.map((insight, index) => renderInsightCard(insight, index))}
          </CardContent>
        </Card>
      )}

      {/* Stock Optimization */}
      {stockOptimization.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Stock Optimization
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {stockOptimization.map((insight, index) => renderInsightCard(insight, index))}
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="p-6 text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">Analyzing Inventory...</h3>
            <p className="text-sm text-gray-500">AI is generating personalized insights for your restaurant</p>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && !insights && wasteReduction.length === 0 && stockOptimization.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Insights Available</h3>
            <p className="text-sm text-gray-500 mb-4">
              Click refresh to generate AI-powered recommendations
            </p>
            <Button onClick={loadAIInsights} disabled={loading}>
              <Brain className="h-4 w-4 mr-2" />
              Generate Insights
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
