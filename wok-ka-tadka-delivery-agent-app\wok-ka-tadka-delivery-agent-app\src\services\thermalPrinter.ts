// Thermal Printer Service for Everycom EC58
// This service handles communication with thermal printers via different methods

export interface PrinterConfig {
  name: string;
  type: 'usb' | 'bluetooth' | 'network' | 'serial';
  address?: string; // For network/bluetooth
  port?: number; // For network
  baudRate?: number; // For serial
  characterSet?: string;
  paperWidth?: number; // in mm (58mm for EC58)
}

export interface PrintJob {
  id: string;
  type: 'kot' | 'bill' | 'receipt';
  content: string;
  timestamp: Date;
  status: 'pending' | 'printing' | 'completed' | 'failed';
}

export class ThermalPrinterService {
  private config: PrinterConfig;
  private isConnected: boolean = false;
  private printQueue: PrintJob[] = [];
  private usbDevice: USBDevice | null = null;
  private bluetoothCharacteristic: BluetoothRemoteGATTCharacteristic | null = null;
  private connectionType: 'usb' | 'bluetooth' | 'network' | null = null;

  constructor(config: PrinterConfig) {
    this.config = {
      characterSet: 'UTF-8',
      paperWidth: 58, // EC58 is 58mm
      ...config
    };
  }

  // Initialize printer connection
  async initialize(): Promise<boolean> {
    try {
      console.log('Initializing thermal printer...', this.config);
      
      // For web applications, we'll use different approaches based on the connection type
      switch (this.config.type) {
        case 'usb':
          return await this.initializeUSB();
        case 'bluetooth':
          return await this.initializeBluetooth();
        case 'network':
          return await this.initializeNetwork();
        default:
          throw new Error(`Unsupported printer type: ${this.config.type}`);
      }
    } catch (error) {
      console.error('Failed to initialize printer:', error);
      return false;
    }
  }

  // USB connection using WebUSB API (requires HTTPS)
  private async initializeUSB(): Promise<boolean> {
    try {
      if (!navigator.usb) {
        throw new Error('WebUSB not supported in this browser');
      }

      console.log('Requesting USB device access...');

      // Request USB device access with broader filter for thermal printers
      const device = await navigator.usb.requestDevice({
        filters: [
          { vendorId: 0x0416 }, // Common thermal printer vendor ID
          { vendorId: 0x04b8 }, // Epson
          { vendorId: 0x0519 }, // Another common vendor
          { vendorId: 0x1a86 }, // QinHeng Electronics (common for Chinese printers)
          { vendorId: 0x067b }, // Prolific Technology Inc
          { vendorId: 0x0483 }, // STMicroelectronics
          { vendorId: 0x1fc9 }, // NXP Semiconductors
          { vendorId: 0x28e9 }, // GigaDevice Semiconductor Inc
          { vendorId: 0x0403 }, // Future Technology Devices International
          // Add more generic filters
          { classCode: 7 }, // Printer class
          { classCode: 9 }, // Hub class (some printers use this)
        ]
      });

      console.log('USB device selected:', {
        productName: device.productName,
        manufacturerName: device.manufacturerName,
        vendorId: device.vendorId,
        productId: device.productId,
        configurations: device.configurations.length
      });

      console.log('Opening USB device...');
      await device.open();

      console.log('Device opened successfully');

      // Try to select the first available configuration
      if (device.configurations.length > 0) {
        const configValue = device.configurations[0].configurationValue;
        console.log('Selecting configuration:', configValue);

        try {
          await device.selectConfiguration(configValue);
          console.log('Configuration selected successfully');
        } catch (configError) {
          console.warn('Failed to select configuration, trying to continue:', configError);
        }

        // Try to claim the first available interface
        const config = device.configurations[0];
        if (config.interfaces.length > 0) {
          const interfaceNumber = config.interfaces[0].interfaceNumber;
          console.log('Claiming interface:', interfaceNumber);

          try {
            await device.claimInterface(interfaceNumber);
            console.log('Interface claimed successfully');
          } catch (interfaceError) {
            console.warn('Failed to claim interface, but continuing:', interfaceError);
            // Some printers work without claiming interface
          }
        }
      } else {
        console.warn('No configurations available, but continuing...');
      }

      // Store device reference for later use
      this.usbDevice = device;
      this.isConnected = true;
      console.log('USB printer connected successfully');
      return true;
    } catch (error) {
      console.error('USB connection failed:', error);

      // Provide more helpful error messages
      if (error.name === 'NotFoundError') {
        console.log('No USB device selected or no compatible device found');
        throw new Error('No USB device selected. Please select your printer from the list.');
      } else if (error.name === 'SecurityError') {
        console.log('USB access denied - make sure you\'re using HTTPS');
        throw new Error('USB access denied. Make sure you\'re using HTTPS and grant permission.');
      } else if (error.name === 'NetworkError') {
        console.log('Device communication error');
        throw new Error('Failed to communicate with the device. Try unplugging and reconnecting the printer.');
      } else if (error.name === 'InvalidStateError') {
        console.log('Device is in invalid state');
        throw new Error('Device is busy or in invalid state. Try disconnecting and reconnecting.');
      } else {
        throw new Error(`USB connection failed: ${error.message || 'Unknown error'}`);
      }
    }
  }

  // Enhanced Bluetooth connection for Everycom EC58
  private async initializeBluetooth(): Promise<boolean> {
    try {
      if (!navigator.bluetooth) {
        throw new Error('Web Bluetooth not supported in this browser');
      }

      console.log('Requesting Bluetooth device for Everycom EC58...');

      const device = await navigator.bluetooth.requestDevice({
        filters: [
          // Everycom EC58 specific patterns (based on manual analysis)
          { namePrefix: 'EC58' },
          { namePrefix: 'Everycom' },
          { namePrefix: 'EC-58' },
          { namePrefix: 'EVERYCOM' },
          { namePrefix: 'EC_58' },
          { namePrefix: 'BT58' },
          // Generic thermal printer patterns
          { namePrefix: 'Thermal' },
          { namePrefix: 'POS' },
          { namePrefix: 'BT' },
          { namePrefix: 'Printer' },
          { namePrefix: 'Receipt' },
          { namePrefix: 'MTP' }, // Mobile Thermal Printer
          { namePrefix: '58mm' },
          // Service-based filters for thermal printers
          { services: ['000018f0-0000-1000-8000-00805f9b34fb'] }, // Nordic UART
          { services: ['00001101-0000-1000-8000-00805f9b34fb'] }, // Serial Port Profile (SPP)
          { services: ['49535343-fe7d-4ae5-8fa9-9fafd205e455'] }, // Microchip service
          { services: ['0000ffe0-0000-1000-8000-00805f9b34fb'] }, // Common thermal printer service
        ],
        optionalServices: [
          // Essential services for Everycom EC58
          '000018f0-0000-1000-8000-00805f9b34fb', // Nordic UART Service
          '00001101-0000-1000-8000-00805f9b34fb', // Serial Port Profile (SPP) - Critical for thermal printers
          '0000180f-0000-1000-8000-00805f9b34fb', // Battery service
          '00001800-0000-1000-8000-00805f9b34fb', // Generic access
          '00001801-0000-1000-8000-00805f9b34fb', // Generic attribute
          // Thermal printer specific services
          '49535343-fe7d-4ae5-8fa9-9fafd205e455', // Microchip service
          '6e400001-b5a3-f393-e0a9-e50e24dcca9e', // Nordic UART alternative
          '0000ffe0-0000-1000-8000-00805f9b34fb', // Common thermal printer service
          '0000fff0-0000-1000-8000-00805f9b34fb', // Alternative thermal service
          // Write/Read characteristics
          '00002af1-0000-1000-8000-00805f9b34fb', // Write characteristic
          '00002af0-0000-1000-8000-00805f9b34fb', // Read characteristic
          '0000ffe1-0000-1000-8000-00805f9b34fb', // Common thermal write
          '0000fff1-0000-1000-8000-00805f9b34fb', // Alternative write
        ]
      });

      console.log('Bluetooth device found:', device.name, 'ID:', device.id);

      const server = await device.gatt?.connect();
      if (!server) {
        throw new Error('Failed to connect to GATT server');
      }

      console.log('Connected to GATT server, finding services...');

      // Try to find and connect to the appropriate service and characteristic
      const serviceUUIDs = [
        '000018f0-0000-1000-8000-00805f9b34fb', // Nordic UART
        '00001101-0000-1000-8000-00805f9b34fb', // Serial Port Profile
        '49535343-fe7d-4ae5-8fa9-9fafd205e455', // Microchip
        '6e400001-b5a3-f393-e0a9-e50e24dcca9e', // Nordic UART alternative
      ];

      let service = null;
      let characteristic = null;

      for (const serviceUUID of serviceUUIDs) {
        try {
          service = await server.getPrimaryService(serviceUUID);
          console.log(`Found service: ${serviceUUID}`);

          // Try different characteristic UUIDs for writing
          const charUUIDs = [
            '00002af1-0000-1000-8000-00805f9b34fb', // Nordic write
            '49535343-1e4d-4bd9-ba61-23c647249616', // Microchip write
            '6e400002-b5a3-f393-e0a9-e50e24dcca9e', // Nordic write alternative
            '0000ffe1-0000-1000-8000-00805f9b34fb', // Common thermal printer write
          ];

          for (const charUUID of charUUIDs) {
            try {
              characteristic = await service.getCharacteristic(charUUID);
              console.log(`Found write characteristic: ${charUUID}`);
              break;
            } catch (e) {
              console.log(`Characteristic ${charUUID} not found, trying next...`);
            }
          }

          if (characteristic) break;
        } catch (e) {
          console.log(`Service ${serviceUUID} not found, trying next...`);
        }
      }

      if (characteristic) {
        this.bluetoothCharacteristic = characteristic;
        this.connectionType = 'bluetooth';
        console.log('Bluetooth characteristic configured for printing');
      }

      this.isConnected = true;
      console.log('Bluetooth printer connected successfully');
      return true;
    } catch (error) {
      console.error('Bluetooth connection failed:', error);

      // Provide more helpful error messages
      if (error.name === 'NotFoundError') {
        console.log('No Bluetooth device selected. Make sure your printer is in pairing mode and discoverable.');
      } else if (error.name === 'SecurityError') {
        console.log('Bluetooth access denied. Make sure you\'re using HTTPS and have granted permissions.');
      } else if (error.name === 'NetworkError') {
        console.log('Failed to connect to the device. Make sure the printer is powered on, in range, and not connected to another device.');
      } else if (error.name === 'NotSupportedError') {
        console.log('Bluetooth operation not supported. Your printer may need to be paired in Windows Bluetooth settings first.');
      }

      return false;
    }
  }

  // Network connection (for network-enabled printers)
  private async initializeNetwork(): Promise<boolean> {
    try {
      if (!this.config.address) {
        throw new Error('Network address not provided');
      }

      // For network printers, we'll use a simple ping test
      const response = await fetch(`http://${this.config.address}:${this.config.port || 9100}`, {
        method: 'HEAD',
        mode: 'no-cors'
      });

      this.isConnected = true;
      console.log('Network printer connected successfully');
      return true;
    } catch (error) {
      console.error('Network connection failed:', error);
      return false;
    }
  }

  // Generate ESC/POS commands for thermal printing
  private generateESCPOSCommands(content: string, type: 'kot' | 'bill'): Uint8Array {
    const commands: number[] = [];
    
    // ESC/POS command constants
    const ESC = 0x1B;
    const GS = 0x1D;
    const LF = 0x0A;
    const CR = 0x0D;
    
    // Initialize printer
    commands.push(ESC, 0x40); // ESC @ - Initialize printer
    
    // Set character set to UTF-8
    commands.push(ESC, 0x74, 0x06); // ESC t 6 - UTF-8
    
    // Set line spacing
    commands.push(ESC, 0x33, 0x20); // ESC 3 32 - Set line spacing to 32/180 inch
    
    // Center alignment for header
    commands.push(ESC, 0x61, 0x01); // ESC a 1 - Center alignment
    
    // Add content
    const lines = content.split('\n');
    for (const line of lines) {
      // Convert string to bytes
      const lineBytes = new TextEncoder().encode(line);
      commands.push(...Array.from(lineBytes));
      commands.push(LF); // Line feed
    }
    
    // Cut paper (if supported)
    commands.push(GS, 0x56, 0x00); // GS V 0 - Full cut
    
    return new Uint8Array(commands);
  }

  // Print content
  async print(content: string, type: 'kot' | 'bill' = 'bill'): Promise<boolean> {
    if (!this.isConnected) {
      console.warn('Printer not connected, falling back to browser print');
      return this.fallbackPrint(content);
    }

    try {
      const printJob: PrintJob = {
        id: `print_${Date.now()}`,
        type,
        content,
        timestamp: new Date(),
        status: 'pending'
      };

      this.printQueue.push(printJob);
      
      // Generate ESC/POS commands
      const commands = this.generateESCPOSCommands(content, type);
      
      // Send to printer based on connection type
      switch (this.config.type) {
        case 'usb':
          // Try simple USB printing first
          const simpleSuccess = await this.printUSBSimple(content);
          if (simpleSuccess) {
            printJob.status = 'completed';
            return true;
          }
          // Fall back to ESC/POS commands
          return await this.printUSB(commands, printJob);
        case 'bluetooth':
          return await this.printBluetooth(commands, printJob);
        case 'network':
          return await this.printNetwork(commands, printJob);
        default:
          throw new Error('Unsupported printer type');
      }
    } catch (error) {
      console.error('Print failed:', error);
      return this.fallbackPrint(content);
    }
  }

  // USB printing
  private async printUSB(commands: Uint8Array, job: PrintJob): Promise<boolean> {
    try {
      if (!this.usbDevice) {
        throw new Error('USB device not available');
      }

      job.status = 'printing';
      console.log('Sending data to USB printer...');

      // Find the first OUT endpoint
      let endpoint: USBEndpoint | null = null;

      for (const config of this.usbDevice.configurations) {
        for (const iface of config.interfaces) {
          for (const alt of iface.alternates) {
            for (const ep of alt.endpoints) {
              if (ep.direction === 'out') {
                endpoint = ep;
                break;
              }
            }
            if (endpoint) break;
          }
          if (endpoint) break;
        }
        if (endpoint) break;
      }

      if (endpoint) {
        console.log('Found OUT endpoint:', endpoint.endpointNumber);
        // Send data to the endpoint
        const result = await this.usbDevice.transferOut(endpoint.endpointNumber, commands);
        console.log('USB transfer result:', result);

        if (result.status === 'ok') {
          job.status = 'completed';
          console.log('USB print completed successfully');
          return true;
        } else {
          throw new Error(`Transfer failed with status: ${result.status}`);
        }
      } else {
        // Try a simple approach - send to endpoint 1 (common for printers)
        console.log('No OUT endpoint found, trying endpoint 1...');
        const result = await this.usbDevice.transferOut(1, commands);
        console.log('USB transfer result:', result);

        if (result.status === 'ok') {
          job.status = 'completed';
          console.log('USB print completed successfully');
          return true;
        } else {
          throw new Error(`Transfer failed with status: ${result.status}`);
        }
      }
    } catch (error) {
      job.status = 'failed';
      console.error('USB print failed:', error);

      // Try fallback approach with simpler data
      try {
        console.log('Trying fallback USB approach...');
        const simpleData = new TextEncoder().encode(job.content + '\n\n\n');
        const result = await this.usbDevice!.transferOut(1, simpleData);

        if (result.status === 'ok') {
          job.status = 'completed';
          console.log('USB fallback print completed');
          return true;
        }
      } catch (fallbackError) {
        console.error('USB fallback also failed:', fallbackError);
      }

      return false;
    }
  }

  // Bluetooth printing
  private async printBluetooth(commands: Uint8Array, job: PrintJob): Promise<boolean> {
    try {
      if (!this.bluetoothCharacteristic) {
        throw new Error('Bluetooth characteristic not available');
      }

      job.status = 'printing';
      console.log('Sending data to Bluetooth printer...');

      // Split data into chunks (Bluetooth has MTU limitations, typically 20-512 bytes)
      const chunkSize = 20; // Conservative chunk size for compatibility
      const chunks = [];

      for (let i = 0; i < commands.length; i += chunkSize) {
        chunks.push(commands.slice(i, i + chunkSize));
      }

      console.log(`Sending ${chunks.length} chunks to Bluetooth printer`);

      // Send each chunk with a small delay
      for (let i = 0; i < chunks.length; i++) {
        try {
          await this.bluetoothCharacteristic.writeValue(chunks[i]);
          console.log(`Sent chunk ${i + 1}/${chunks.length}`);

          // Small delay between chunks to prevent overwhelming the printer
          if (i < chunks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        } catch (error) {
          console.error(`Failed to send chunk ${i + 1}:`, error);
          throw error;
        }
      }

      // Wait for printing to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      job.status = 'completed';
      console.log('Bluetooth print completed successfully');
      return true;
    } catch (error) {
      job.status = 'failed';
      console.error('Bluetooth print failed:', error);

      // Provide specific error messages
      if (error.name === 'NetworkError') {
        console.log('Bluetooth connection lost. Try reconnecting the printer.');
      } else if (error.name === 'InvalidStateError') {
        console.log('Bluetooth device is not ready. Make sure it\'s connected and powered on.');
      }

      return false;
    }
  }

  // Network printing
  private async printNetwork(commands: Uint8Array, job: PrintJob): Promise<boolean> {
    try {
      job.status = 'printing';
      
      const response = await fetch(`http://${this.config.address}:${this.config.port || 9100}`, {
        method: 'POST',
        body: commands,
        headers: {
          'Content-Type': 'application/octet-stream'
        }
      });

      if (response.ok) {
        job.status = 'completed';
        console.log('Network print completed');
        return true;
      } else {
        throw new Error(`Network print failed: ${response.status}`);
      }
    } catch (error) {
      job.status = 'failed';
      console.error('Network print failed:', error);
      return false;
    }
  }

  // Simple USB printing for basic compatibility
  private async printUSBSimple(content: string): Promise<boolean> {
    try {
      if (!this.usbDevice) {
        return false;
      }

      console.log('Attempting simple USB print...');

      // Create simple text data with basic formatting
      const lines = content.split('\n');
      let printData = '';

      for (const line of lines) {
        printData += line + '\n';
      }

      // Add some line feeds at the end
      printData += '\n\n\n';

      // Convert to bytes
      const data = new TextEncoder().encode(printData);

      // Try to send to common printer endpoints
      const endpoints = [1, 2, 3]; // Common OUT endpoints for printers

      for (const endpointNum of endpoints) {
        try {
          console.log(`Trying endpoint ${endpointNum}...`);
          const result = await this.usbDevice.transferOut(endpointNum, data);

          if (result.status === 'ok') {
            console.log(`Simple USB print successful on endpoint ${endpointNum}`);
            return true;
          }
        } catch (endpointError) {
          console.log(`Endpoint ${endpointNum} failed:`, endpointError);
          continue;
        }
      }

      return false;
    } catch (error) {
      console.error('Simple USB print failed:', error);
      return false;
    }
  }

  // Fallback to browser printing
  private fallbackPrint(content: string): boolean {
    try {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Print</title>
              <style>
                body { font-family: monospace; font-size: 12px; margin: 0; padding: 10px; }
                @media print { body { margin: 0; } }
              </style>
            </head>
            <body>
              <pre>${content}</pre>
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Fallback print failed:', error);
      return false;
    }
  }

  // Get printer status
  getStatus(): { connected: boolean; queueLength: number } {
    return {
      connected: this.isConnected,
      queueLength: this.printQueue.filter(job => job.status === 'pending').length
    };
  }

  // Disconnect printer
  disconnect(): void {
    this.isConnected = false;
    console.log('Printer disconnected');
  }
}

// Default printer configurations
export const defaultPrinterConfigs: PrinterConfig[] = [
  {
    name: 'Everycom EC58 USB',
    type: 'usb',
    paperWidth: 58
  },
  {
    name: 'Everycom EC58 Bluetooth',
    type: 'bluetooth',
    paperWidth: 58
  },
  {
    name: 'Network Printer',
    type: 'network',
    address: '*************',
    port: 9100,
    paperWidth: 58
  }
];

// Global printer service instance
let globalPrinterService: ThermalPrinterService | null = null;

// Set global printer service
export const setGlobalPrinterService = (service: ThermalPrinterService | null): void => {
  globalPrinterService = service;
};

// Get global printer service
export const getGlobalPrinterService = (): ThermalPrinterService | null => {
  return globalPrinterService;
};

// Check if thermal printer is available
export const isThermalPrinterAvailable = (): boolean => {
  return globalPrinterService !== null && globalPrinterService.getStatus().connected;
};
