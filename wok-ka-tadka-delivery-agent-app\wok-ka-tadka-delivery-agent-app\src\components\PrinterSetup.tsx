import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Badge } from './ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { useToast } from './ui/use-toast';
import { 
  Printer, 
  Settings, 
  Wifi, 
  Bluetooth, 
  Usb, 
  TestTube,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { ThermalPrinterService, PrinterConfig, defaultPrinterConfigs } from '../services/thermalPrinter';
import { thermalFormatter } from '../utils/thermalPrintFormatter';
import PrinterTroubleshooting from './PrinterTroubleshooting';
import USBDebugger from './USBDebugger';
import BluetoothTroubleshooting from './BluetoothTroubleshooting';

interface PrinterSetupProps {
  onPrinterConfigured?: (printer: ThermalPrinterService) => void;
}

const PrinterSetup: React.FC<PrinterSetupProps> = ({ onPrinterConfigured }) => {
  const [printerService, setPrinterService] = useState<ThermalPrinterService | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [connectionError, setConnectionError] = useState<string>('');
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const [showDebugger, setShowDebugger] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<PrinterConfig>(defaultPrinterConfigs[0]);
  const [customConfig, setCustomConfig] = useState<PrinterConfig>({
    name: 'Custom Printer',
    type: 'usb',
    paperWidth: 58
  });
  const [showCustomConfig, setShowCustomConfig] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Load saved printer configuration
    const savedConfig = localStorage.getItem('printerConfig');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setSelectedConfig(config);
        initializePrinter(config);
      } catch (error) {
        console.error('Failed to load saved printer config:', error);
      }
    }
  }, []);

  const initializePrinter = async (config: PrinterConfig) => {
    setIsConnecting(true);
    setConnectionError('');
    setShowTroubleshooting(false);

    try {
      const service = new ThermalPrinterService(config);
      const connected = await service.initialize();
      
      setPrinterService(service);
      setIsConnected(connected);
      
      if (connected) {
        localStorage.setItem('printerConfig', JSON.stringify(config));
        onPrinterConfigured?.(service);
        toast({
          title: "Printer Connected",
          description: `Successfully connected to ${config.name}`,
        });
      } else {
        const errorMsg = "Could not connect to printer. Check your connection and try again.";
        setConnectionError(errorMsg);
        setShowTroubleshooting(true);
        toast({
          title: "Connection Failed",
          description: errorMsg,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Printer initialization failed:', error);
      const errorMsg = error instanceof Error ? error.message : "Failed to initialize printer. Please check your configuration.";
      setConnectionError(errorMsg);
      setShowTroubleshooting(true);
      toast({
        title: "Initialization Error",
        description: errorMsg,
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const handleConnect = () => {
    const config = showCustomConfig ? customConfig : selectedConfig;
    initializePrinter(config);
  };

  const handleDisconnect = () => {
    if (printerService) {
      printerService.disconnect();
      setPrinterService(null);
      setIsConnected(false);
      toast({
        title: "Printer Disconnected",
        description: "Printer has been disconnected successfully.",
      });
    }
  };

  const handleTestPrint = async () => {
    if (!printerService) {
      toast({
        title: "No Printer Connected",
        description: "Please connect a printer first.",
        variant: "destructive"
      });
      return;
    }

    setIsTesting(true);
    try {
      const testContent = thermalFormatter.formatTestPrint();
      const success = await printerService.print(testContent, 'bill');
      
      if (success) {
        toast({
          title: "Test Print Successful",
          description: "Test page printed successfully!",
        });
      } else {
        toast({
          title: "Test Print Failed",
          description: "Failed to print test page. Check printer connection.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Test print failed:', error);
      toast({
        title: "Test Print Error",
        description: "An error occurred during test printing.",
        variant: "destructive"
      });
    } finally {
      setIsTesting(false);
    }
  };

  const getConnectionIcon = (type: string) => {
    switch (type) {
      case 'usb': return <Usb className="h-4 w-4" />;
      case 'bluetooth': return <Bluetooth className="h-4 w-4" />;
      case 'network': return <Wifi className="h-4 w-4" />;
      default: return <Printer className="h-4 w-4" />;
    }
  };

  const getStatusBadge = () => {
    if (isConnecting) {
      return <Badge variant="secondary"><AlertCircle className="h-3 w-3 mr-1" />Connecting...</Badge>;
    }
    if (isConnected) {
      return <Badge variant="default" className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" />Connected</Badge>;
    }
    return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Disconnected</Badge>;
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Printer className="h-5 w-5" />
          Thermal Printer Setup
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Printer Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Printer Configuration</h3>
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Advanced
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Advanced Printer Settings</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Printer Name</label>
                    <Input
                      value={customConfig.name}
                      onChange={(e) => setCustomConfig({...customConfig, name: e.target.value})}
                      placeholder="Enter printer name"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Connection Type</label>
                    <Select
                      value={customConfig.type}
                      onValueChange={(value: any) => setCustomConfig({...customConfig, type: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="usb">USB</SelectItem>
                        <SelectItem value="bluetooth">Bluetooth</SelectItem>
                        <SelectItem value="network">Network</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {customConfig.type === 'network' && (
                    <>
                      <div>
                        <label className="text-sm font-medium">IP Address</label>
                        <Input
                          value={customConfig.address || ''}
                          onChange={(e) => setCustomConfig({...customConfig, address: e.target.value})}
                          placeholder="*************"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Port</label>
                        <Input
                          type="number"
                          value={customConfig.port || 9100}
                          onChange={(e) => setCustomConfig({...customConfig, port: parseInt(e.target.value)})}
                          placeholder="9100"
                        />
                      </div>
                    </>
                  )}
                  <div>
                    <label className="text-sm font-medium">Paper Width (mm)</label>
                    <Input
                      type="number"
                      value={customConfig.paperWidth || 58}
                      onChange={(e) => setCustomConfig({...customConfig, paperWidth: parseInt(e.target.value)})}
                      placeholder="58"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowCustomConfig(false)}
                      className="flex-1"
                    >
                      Use Preset
                    </Button>
                    <Button
                      onClick={() => setShowCustomConfig(true)}
                      className="flex-1"
                    >
                      Use Custom
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {!showCustomConfig ? (
            <Select
              value={selectedConfig.name}
              onValueChange={(value) => {
                const config = defaultPrinterConfigs.find(c => c.name === value);
                if (config) setSelectedConfig(config);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a printer configuration" />
              </SelectTrigger>
              <SelectContent>
                {defaultPrinterConfigs.map((config) => (
                  <SelectItem key={config.name} value={config.name}>
                    <div className="flex items-center gap-2">
                      {getConnectionIcon(config.type)}
                      {config.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="p-4 border rounded-lg bg-gray-50">
              <div className="flex items-center gap-2 mb-2">
                {getConnectionIcon(customConfig.type)}
                <span className="font-medium">{customConfig.name}</span>
                <Badge variant="outline">{customConfig.type.toUpperCase()}</Badge>
              </div>
              <p className="text-sm text-gray-600">
                {customConfig.type === 'network' && customConfig.address && 
                  `Network: ${customConfig.address}:${customConfig.port}`}
                {customConfig.type === 'usb' && 'USB Connection'}
                {customConfig.type === 'bluetooth' && 'Bluetooth Connection'}
              </p>
            </div>
          )}
        </div>

        {/* Connection Controls */}
        <div className="flex gap-3">
          {!isConnected ? (
            <Button
              onClick={handleConnect}
              disabled={isConnecting}
              className="flex-1"
            >
              {isConnecting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Connecting...
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4 mr-2" />
                  Connect Printer
                </>
              )}
            </Button>
          ) : (
            <>
              <Button
                variant="outline"
                onClick={handleDisconnect}
                className="flex-1"
              >
                Disconnect
              </Button>
              <Button
                onClick={handleTestPrint}
                disabled={isTesting}
                className="flex-1"
              >
                {isTesting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Testing...
                  </>
                ) : (
                  <>
                    <TestTube className="h-4 w-4 mr-2" />
                    Test Print
                  </>
                )}
              </Button>
            </>
          )}
        </div>

        {/* Status Information */}
        {printerService && (
          <div className="p-4 border rounded-lg bg-blue-50">
            <h4 className="font-medium mb-2">Printer Status</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Connection:</span>
                <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
                  {isConnected ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Queue:</span>
                <span>{printerService.getStatus().queueLength} jobs</span>
              </div>
              <div className="flex justify-between">
                <span>Type:</span>
                <span className="capitalize">{selectedConfig.type}</span>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="p-4 border rounded-lg bg-yellow-50">
          <h4 className="font-medium mb-2">Setup Instructions</h4>
          <ul className="text-sm space-y-1 text-gray-700">
            <li>• Ensure your Everycom EC58 printer is powered on</li>
            <li>• For USB: Connect the printer via USB cable</li>
            <li>• For Bluetooth: Pair the printer in your device settings first</li>
            <li>• For Network: Ensure printer is connected to the same network</li>
            <li>• Test print to verify the connection works properly</li>
          </ul>
        </div>

        {/* Debug Tools */}
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDebugger(!showDebugger)}
            className="text-xs"
          >
            <TestTube className="h-3 w-3 mr-1" />
            {showDebugger ? 'Hide' : 'Show'} USB Debugger
          </Button>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="text-xs">
                <Bluetooth className="h-3 w-3 mr-1" />
                Bluetooth Help
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Bluetooth Troubleshooting</DialogTitle>
              </DialogHeader>
              <BluetoothTroubleshooting />
            </DialogContent>
          </Dialog>
        </div>

        {/* USB Debugger */}
        {showDebugger && (
          <div className="mt-4">
            <USBDebugger />
          </div>
        )}

        {/* Troubleshooting */}
        {showTroubleshooting && (
          <div className="mt-4">
            <PrinterTroubleshooting
              connectionType={showCustomConfig ? customConfig.type : selectedConfig.type}
              error={connectionError}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PrinterSetup;
