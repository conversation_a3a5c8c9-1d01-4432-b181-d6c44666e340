import { cn } from "@/lib/utils";

interface LogoProps {
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  showText?: boolean;
  variant?: "default" | "white" | "gradient";
}

const Logo = ({ 
  size = "md", 
  className, 
  showText = false, 
  variant = "default" 
}: LogoProps) => {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12", 
    lg: "w-16 h-16",
    xl: "w-24 h-24"
  };

  const containerClasses = {
    default: "bg-gradient-to-br from-orange-400 to-orange-500",
    white: "bg-white",
    gradient: "bg-gradient-to-br from-red-400 via-red-500 to-red-600"
  };

  const textSizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg", 
    xl: "text-2xl"
  };

  const logoSizeClasses = {
    sm: "w-5 h-5",
    md: "w-8 h-8",
    lg: "w-12 h-12",
    xl: "w-16 h-16"
  };

  return (
    <div className={cn("flex items-center gap-3", className)}>
      <div className={cn(
        "rounded-2xl flex items-center justify-center shadow-lg",
        containerClasses[variant],
        sizeClasses[size]
      )}>
        <img 
          src="/img/app.png" 
          alt="Wok Ka Tadka Logo" 
          className={cn("object-contain", logoSizeClasses[size])}
        />
      </div>
      {showText && (
        <div>
          <h1 className={cn(
            "font-bold",
            textSizeClasses[size],
            variant === "white" ? "text-gray-900" : "text-white"
          )}>
            Wok Ka Tadka
          </h1>
          <p className={cn(
            "text-xs opacity-80",
            variant === "white" ? "text-gray-600" : "text-white/80"
          )}>
            Mumbai Style Chinese & Indian
          </p>
        </div>
      )}
    </div>
  );
};

export { Logo };
