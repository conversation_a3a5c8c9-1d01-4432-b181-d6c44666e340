// Notification System for Early Checkout Approvals

export interface EarlyCheckoutNotification {
  id: string;
  staffId: string;
  staffName: string;
  staffPhone: string;
  requestTime: string; // ISO string
  scheduledEndTime: string; // ISO string
  actualCheckoutTime: string; // ISO string
  minutesEarly: number;
  reason?: string; // Optional reason for early checkout
  status: 'pending' | 'approved' | 'rejected';
  adminResponse?: {
    adminId: string;
    adminName: string;
    responseTime: string;
    comments?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface NotificationStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  todayRequests: number;
}

export interface NotificationAnalytics {
  totalRequests: number;
  approvalRate: number; // Percentage of approved requests
  rejectionRate: number; // Percentage of rejected requests
  averageResponseTime: number; // Average time to respond in hours
  mostCommonReasons: { reason: string; count: number }[];
  requestsByDay: { date: string; count: number }[];
  requestsByStaff: { staffId: string; staffName: string; count: number }[];
  peakRequestHours: { hour: number; count: number }[];
  averageEarlyMinutes: number; // Average minutes early for requests
}

// Add a new notification type for check-in
export interface CheckInNotification {
  id: string;
  type: 'checkin';
  staffId: string;
  staffName: string;
  staffPhone: string;
  checkInTime: string; // ISO string
  createdAt: string;
}

const STORAGE_KEY = 'wok_ka_tadka_notifications';
const BACKUP_STORAGE_KEY = 'wok_ka_tadka_notifications_backup';

// Error handling and recovery utilities
export class NotificationStorageError extends Error {
  constructor(message: string, public code: string, public recoverable: boolean = true) {
    super(message);
    this.name = 'NotificationStorageError';
  }
}

// Create backup of notifications
const createBackup = (notifications: EarlyCheckoutNotification[]): void => {
  try {
    const backup = {
      timestamp: new Date().toISOString(),
      data: notifications,
      version: '1.0'
    };
    localStorage.setItem(BACKUP_STORAGE_KEY, JSON.stringify(backup));
  } catch (error) {
    console.warn('Failed to create notification backup:', error);
  }
};

// Restore from backup
const restoreFromBackup = (): EarlyCheckoutNotification[] => {
  try {
    const backupData = localStorage.getItem(BACKUP_STORAGE_KEY);
    if (backupData) {
      const backup = JSON.parse(backupData);
      if (backup.data && Array.isArray(backup.data)) {
        console.log('Restored notifications from backup');
        return backup.data;
      }
    }
  } catch (error) {
    console.error('Failed to restore from backup:', error);
  }
  return [];
};

// Validate notification data structure
const validateNotification = (notification: any): notification is EarlyCheckoutNotification => {
  return (
    notification &&
    typeof notification.id === 'string' &&
    typeof notification.staffId === 'string' &&
    typeof notification.staffName === 'string' &&
    typeof notification.staffPhone === 'string' &&
    typeof notification.requestTime === 'string' &&
    typeof notification.scheduledEndTime === 'string' &&
    typeof notification.actualCheckoutTime === 'string' &&
    typeof notification.minutesEarly === 'number' &&
    ['pending', 'approved', 'rejected'].includes(notification.status) &&
    typeof notification.createdAt === 'string' &&
    typeof notification.updatedAt === 'string'
  );
};

// Sanitize and validate notification array
const sanitizeNotifications = (notifications: any[]): EarlyCheckoutNotification[] => {
  if (!Array.isArray(notifications)) {
    throw new NotificationStorageError('Invalid notifications data format', 'INVALID_FORMAT');
  }

  const validNotifications: EarlyCheckoutNotification[] = [];
  const invalidCount = notifications.length;

  notifications.forEach((notification, index) => {
    if (validateNotification(notification)) {
      validNotifications.push(notification);
    } else {
      console.warn(`Invalid notification at index ${index}:`, notification);
    }
  });

  if (validNotifications.length < invalidCount) {
    console.warn(`Filtered out ${invalidCount - validNotifications.length} invalid notifications`);
  }

  return validNotifications;
};

// Get all notifications from localStorage with error handling and recovery
export const getAllNotifications = (): EarlyCheckoutNotification[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return [];
    }

    const parsed = JSON.parse(stored);
    const sanitized = sanitizeNotifications(parsed);

    // If we had to sanitize data, save the clean version
    if (sanitized.length !== parsed.length) {
      saveNotifications(sanitized);
    }

    return sanitized;
  } catch (error) {
    console.error('Error loading notifications:', error);

    // Try to restore from backup
    try {
      const backupData = restoreFromBackup();
      if (backupData.length > 0) {
        console.log('Successfully restored notifications from backup');
        saveNotifications(backupData);
        return backupData;
      }
    } catch (backupError) {
      console.error('Failed to restore from backup:', backupError);
    }

    // If all else fails, return empty array and create error notification
    console.warn('Returning empty notifications array due to storage errors');
    return [];
  }
};

// Save notifications to localStorage with backup and error handling
export const saveNotifications = (notifications: EarlyCheckoutNotification[]): void => {
  try {
    // Validate notifications before saving
    const sanitized = sanitizeNotifications(notifications);

    // Create backup before saving
    createBackup(sanitized);

    // Save to main storage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(sanitized));

    // Dispatch custom event for real-time updates
    window.dispatchEvent(new CustomEvent('notificationsChanged', {
      detail: { notifications: sanitized }
    }));
  } catch (error) {
    console.error('Error saving notifications:', error);

    // Try to determine if it's a quota exceeded error
    if (error instanceof Error && error.name === 'QuotaExceededError') {
      throw new NotificationStorageError(
        'Storage quota exceeded. Please clear some data or contact administrator.',
        'QUOTA_EXCEEDED',
        false
      );
    }

    throw new NotificationStorageError(
      'Failed to save notifications. Data may not be persisted.',
      'SAVE_FAILED',
      true
    );
  }
};

// Generate unique notification ID
const generateNotificationId = (): string => {
  return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Create new early checkout notification
export const createEarlyCheckoutNotification = (
  staffId: string,
  staffName: string,
  staffPhone: string,
  scheduledEndTime: Date,
  actualCheckoutTime: Date,
  reason?: string
): EarlyCheckoutNotification => {
  const now = new Date();
  const minutesEarly = Math.round((scheduledEndTime.getTime() - actualCheckoutTime.getTime()) / (1000 * 60));
  
  const notification: EarlyCheckoutNotification = {
    id: generateNotificationId(),
    staffId,
    staffName,
    staffPhone,
    requestTime: now.toISOString(),
    scheduledEndTime: scheduledEndTime.toISOString(),
    actualCheckoutTime: actualCheckoutTime.toISOString(),
    minutesEarly,
    reason,
    status: 'pending',
    createdAt: now.toISOString(),
    updatedAt: now.toISOString()
  };

  const notifications = getAllNotifications();
  notifications.unshift(notification); // Add to beginning for latest first
  saveNotifications(notifications);
  
  return notification;
};

// Update notification status (approve/reject)
export const updateNotificationStatus = (
  notificationId: string,
  status: 'approved' | 'rejected',
  adminId: string,
  adminName: string,
  comments?: string
): EarlyCheckoutNotification | null => {
  const notifications = getAllNotifications();
  const notificationIndex = notifications.findIndex(n => n.id === notificationId);
  
  if (notificationIndex === -1) {
    throw new Error('Notification not found');
  }
  
  const notification = notifications[notificationIndex];
  
  // Update notification
  notification.status = status;
  notification.adminResponse = {
    adminId,
    adminName,
    responseTime: new Date().toISOString(),
    comments
  };
  notification.updatedAt = new Date().toISOString();
  
  notifications[notificationIndex] = notification;
  saveNotifications(notifications);
  
  return notification;
};

// Get notifications by status
export const getNotificationsByStatus = (status: 'pending' | 'approved' | 'rejected'): EarlyCheckoutNotification[] => {
  const notifications = getAllNotifications();
  return notifications.filter(n => n.status === status);
};

// Get pending notifications count
export const getPendingNotificationsCount = (): number => {
  const notifications = getAllNotifications();
  return notifications.filter(n => n.status === 'pending').length;
};

// Get notifications for specific staff member
export const getStaffNotifications = (staffId: string, limit?: number): EarlyCheckoutNotification[] => {
  const notifications = getAllNotifications();
  const staffNotifications = notifications
    .filter(n => n.staffId === staffId)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  
  return limit ? staffNotifications.slice(0, limit) : staffNotifications;
};

// Get notification statistics
export const getNotificationStats = (): NotificationStats => {
  const notifications = getAllNotifications();
  const today = new Date().toDateString();
  
  return {
    total: notifications.length,
    pending: notifications.filter(n => n.status === 'pending').length,
    approved: notifications.filter(n => n.status === 'approved').length,
    rejected: notifications.filter(n => n.status === 'rejected').length,
    todayRequests: notifications.filter(n => 
      new Date(n.createdAt).toDateString() === today
    ).length
  };
};

// Get notification by ID
export const getNotificationById = (notificationId: string): EarlyCheckoutNotification | null => {
  const notifications = getAllNotifications();
  return notifications.find(n => n.id === notificationId) || null;
};

// Delete notification (admin only)
export const deleteNotification = (notificationId: string): boolean => {
  const notifications = getAllNotifications();
  const filteredNotifications = notifications.filter(n => n.id !== notificationId);
  
  if (filteredNotifications.length === notifications.length) {
    return false; // Notification not found
  }
  
  saveNotifications(filteredNotifications);
  return true;
};

// Format time for display
export const formatNotificationTime = (isoString: string): string => {
  const date = new Date(isoString);
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

// Get time ago string
export const getTimeAgo = (isoString: string): string => {
  const now = new Date();
  const time = new Date(isoString);
  const diffMs = now.getTime() - time.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return time.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

// Clear all notifications (admin only - for testing)
export const clearAllNotifications = (): void => {
  localStorage.removeItem(STORAGE_KEY);
  window.dispatchEvent(new CustomEvent('notificationsChanged', {
    detail: { notifications: [] }
  }));
};

// Get comprehensive notification analytics
export const getNotificationAnalytics = (dateRange?: { start: Date; end: Date }): NotificationAnalytics => {
  const notifications = getAllNotifications();

  // Filter by date range if provided
  const filteredNotifications = dateRange
    ? notifications.filter(n => {
        const requestDate = new Date(n.requestTime);
        return requestDate >= dateRange.start && requestDate <= dateRange.end;
      })
    : notifications;

  const totalRequests = filteredNotifications.length;
  const approvedCount = filteredNotifications.filter(n => n.status === 'approved').length;
  const rejectedCount = filteredNotifications.filter(n => n.status === 'rejected').length;
  const respondedCount = approvedCount + rejectedCount;

  // Calculate rates
  const approvalRate = respondedCount > 0 ? (approvedCount / respondedCount) * 100 : 0;
  const rejectionRate = respondedCount > 0 ? (rejectedCount / respondedCount) * 100 : 0;

  // Calculate average response time
  const respondedNotifications = filteredNotifications.filter(n => n.adminResponse);
  const totalResponseTime = respondedNotifications.reduce((sum, n) => {
    if (n.adminResponse) {
      const requestTime = new Date(n.requestTime);
      const responseTime = new Date(n.adminResponse.responseTime);
      return sum + (responseTime.getTime() - requestTime.getTime());
    }
    return sum;
  }, 0);
  const averageResponseTime = respondedNotifications.length > 0
    ? totalResponseTime / (respondedNotifications.length * 1000 * 60 * 60) // Convert to hours
    : 0;

  // Most common reasons
  const reasonCounts: { [key: string]: number } = {};
  filteredNotifications.forEach(n => {
    if (n.reason) {
      reasonCounts[n.reason] = (reasonCounts[n.reason] || 0) + 1;
    }
  });
  const mostCommonReasons = Object.entries(reasonCounts)
    .map(([reason, count]) => ({ reason, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // Requests by day (last 30 days)
  const requestsByDay: { [key: string]: number } = {};
  const last30Days = new Date();
  last30Days.setDate(last30Days.getDate() - 30);

  filteredNotifications.forEach(n => {
    const requestDate = new Date(n.requestTime);
    if (requestDate >= last30Days) {
      const dateKey = requestDate.toISOString().split('T')[0];
      requestsByDay[dateKey] = (requestsByDay[dateKey] || 0) + 1;
    }
  });

  const requestsByDayArray = Object.entries(requestsByDay)
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Requests by staff
  const staffCounts: { [key: string]: { name: string; count: number } } = {};
  filteredNotifications.forEach(n => {
    if (!staffCounts[n.staffId]) {
      staffCounts[n.staffId] = { name: n.staffName, count: 0 };
    }
    staffCounts[n.staffId].count++;
  });
  const requestsByStaff = Object.entries(staffCounts)
    .map(([staffId, data]) => ({ staffId, staffName: data.name, count: data.count }))
    .sort((a, b) => b.count - a.count);

  // Peak request hours
  const hourCounts: { [key: number]: number } = {};
  filteredNotifications.forEach(n => {
    const hour = new Date(n.requestTime).getHours();
    hourCounts[hour] = (hourCounts[hour] || 0) + 1;
  });
  const peakRequestHours = Object.entries(hourCounts)
    .map(([hour, count]) => ({ hour: parseInt(hour), count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // Average early minutes
  const totalEarlyMinutes = filteredNotifications.reduce((sum, n) => sum + n.minutesEarly, 0);
  const averageEarlyMinutes = totalRequests > 0 ? totalEarlyMinutes / totalRequests : 0;

  return {
    totalRequests,
    approvalRate: Math.round(approvalRate * 100) / 100,
    rejectionRate: Math.round(rejectionRate * 100) / 100,
    averageResponseTime: Math.round(averageResponseTime * 100) / 100,
    mostCommonReasons,
    requestsByDay: requestsByDayArray,
    requestsByStaff,
    peakRequestHours,
    averageEarlyMinutes: Math.round(averageEarlyMinutes * 100) / 100
  };
};

// Get notifications for a specific date range
export const getNotificationsByDateRange = (startDate: Date, endDate: Date): EarlyCheckoutNotification[] => {
  const notifications = getAllNotifications();
  return notifications.filter(n => {
    const requestDate = new Date(n.requestTime);
    return requestDate >= startDate && requestDate <= endDate;
  });
};

// Get notification trends (weekly/monthly comparison)
export const getNotificationTrends = () => {
  const notifications = getAllNotifications();
  const now = new Date();

  // This week vs last week
  const thisWeekStart = new Date(now);
  thisWeekStart.setDate(now.getDate() - now.getDay());
  thisWeekStart.setHours(0, 0, 0, 0);

  const lastWeekStart = new Date(thisWeekStart);
  lastWeekStart.setDate(lastWeekStart.getDate() - 7);

  const thisWeekEnd = new Date(thisWeekStart);
  thisWeekEnd.setDate(thisWeekEnd.getDate() + 6);
  thisWeekEnd.setHours(23, 59, 59, 999);

  const lastWeekEnd = new Date(lastWeekStart);
  lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
  lastWeekEnd.setHours(23, 59, 59, 999);

  const thisWeekCount = notifications.filter(n => {
    const date = new Date(n.requestTime);
    return date >= thisWeekStart && date <= thisWeekEnd;
  }).length;

  const lastWeekCount = notifications.filter(n => {
    const date = new Date(n.requestTime);
    return date >= lastWeekStart && date <= lastWeekEnd;
  }).length;

  // This month vs last month
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

  const thisMonthCount = notifications.filter(n => {
    const date = new Date(n.requestTime);
    return date >= thisMonthStart && date <= thisMonthEnd;
  }).length;

  const lastMonthCount = notifications.filter(n => {
    const date = new Date(n.requestTime);
    return date >= lastMonthStart && date <= lastMonthEnd;
  }).length;

  return {
    weekly: {
      thisWeek: thisWeekCount,
      lastWeek: lastWeekCount,
      change: lastWeekCount > 0 ? ((thisWeekCount - lastWeekCount) / lastWeekCount) * 100 : 0
    },
    monthly: {
      thisMonth: thisMonthCount,
      lastMonth: lastMonthCount,
      change: lastMonthCount > 0 ? ((thisMonthCount - lastMonthCount) / lastMonthCount) * 100 : 0
    }
  };
};

// Export notification data for reporting
export const exportNotificationData = (format: 'json' | 'csv' = 'json'): string => {
  const notifications = getAllNotifications();

  if (format === 'csv') {
    const headers = [
      'ID', 'Staff Name', 'Staff Phone', 'Request Time', 'Minutes Early',
      'Reason', 'Status', 'Admin Response', 'Response Time'
    ];

    const csvData = notifications.map(n => [
      n.id,
      n.staffName,
      n.staffPhone,
      formatNotificationTime(n.requestTime),
      n.minutesEarly.toString(),
      n.reason || '',
      n.status,
      n.adminResponse?.comments || '',
      n.adminResponse ? formatNotificationTime(n.adminResponse.responseTime) : ''
    ]);

    return [headers, ...csvData].map(row => row.join(',')).join('\n');
  }

  return JSON.stringify({
    exportDate: new Date().toISOString(),
    totalRecords: notifications.length,
    notifications: notifications,
    analytics: getNotificationAnalytics()
  }, null, 2);
};

// Add function to create check-in notification
export const createCheckInNotification = (
  staffId: string,
  staffName: string,
  staffPhone: string,
  checkInTime: string
): CheckInNotification => {
  const notifications = getAllNotifications() as (EarlyCheckoutNotification | CheckInNotification)[];
  const notification: CheckInNotification = {
    id: `checkin-${staffId}-${checkInTime}`,
    type: 'checkin',
    staffId,
    staffName,
    staffPhone,
    checkInTime,
    createdAt: new Date().toISOString(),
  };
  notifications.push(notification);
  localStorage.setItem('wok_ka_tadka_notifications', JSON.stringify(notifications));
  window.dispatchEvent(new CustomEvent('notificationsChanged'));
  return notification;
};
