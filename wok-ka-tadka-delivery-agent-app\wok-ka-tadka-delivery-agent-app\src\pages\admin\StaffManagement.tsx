import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Search,
  Plus,
  Edit,
  Eye,
  RefreshCw,
  Users,
  Phone,
  Calendar,
  Star,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  UserPlus
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { getCurrentWorkingHours, validateWorkingHours, type WorkingHours } from "@/utils/workingHoursStorage";
import { updateStaffPin } from "@/utils/staffPinStorage";

const StaffManagement = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("all");
  const [selectedStaff, setSelectedStaff] = useState<any>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isAddStaffDialogOpen, setIsAddStaffDialogOpen] = useState(false);
  const [newStaffData, setNewStaffData] = useState({
    name: "",
    phone: "",
    role: "staff",
    shift: "morning",
    salary: ""
  });
  const [isWorkingHoursModalOpen, setIsWorkingHoursModalOpen] = useState(false);
  const [workingHoursForm, setWorkingHoursForm] = useState<WorkingHours | null>(null);
  const [workingHoursErrors, setWorkingHoursErrors] = useState<string[]>([]);

  // Mock staff data
  const staff = [
    {
      id: 1,
      name: "Sunita Devi",
      phone: "+91 98765 43210",
      role: "staff",
      shift: "morning",
      joinDate: "2024-01-15",
      salary: 18000,
      status: "active",
      performance: {
        rating: 4.8,
        ordersServed: 156,
        customerFeedback: 4.7,
        punctuality: 95
      },
      todayStats: {
        ordersServed: 12,
        tablesManaged: 8,
        hoursWorked: 6,
        tips: 450
      },
      workingHours: {
        startTime: "09:00",
        endTime: "18:00",
        totalHours: 9,
        workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
        breakDuration: 30,
        overtimeThreshold: 120,
        earlyCheckoutThreshold: 60
      }
    },
    {
      id: 2,
      name: "Raj Kumar",
      phone: "+91 87654 32109",
      role: "staff",
      shift: "evening",
      joinDate: "2024-02-01",
      salary: 16000,
      status: "active",
      performance: {
        rating: 4.6,
        ordersServed: 134,
        customerFeedback: 4.5,
        punctuality: 88
      },
      todayStats: {
        ordersServed: 15,
        tablesManaged: 10,
        hoursWorked: 7,
        tips: 380
      },
      workingHours: {
        startTime: "10:00",
        endTime: "20:00",
        totalHours: 10,
        workingDays: ["monday", "wednesday", "friday"],
        breakDuration: 45,
        overtimeThreshold: 150,
        earlyCheckoutThreshold: 90
      }
    },
    {
      id: 3,
      name: "Amit Singh",
      phone: "+91 76543 21098",
      role: "staff",
      shift: "full-day",
      joinDate: "2024-01-20",
      salary: 20000,
      status: "active",
      performance: {
        rating: 4.9,
        deliveriesCompleted: 89,
        onTimeDeliveries: 92,
        customerRating: 4.8
      },
      todayStats: {
        deliveriesCompleted: 8,
        distanceCovered: 45,
        hoursWorked: 8,
        earnings: 650
      },
      workingHours: {
        startTime: "08:00",
        endTime: "17:00",
        totalHours: 9,
        workingDays: ["monday", "tuesday", "thursday", "friday"],
        breakDuration: 30,
        overtimeThreshold: 120,
        earlyCheckoutThreshold: 60
      }
    },
    {
      id: 4,
      name: "Priya Sharma",
      phone: "+91 65432 10987",
      role: "admin",
      shift: "morning",
      joinDate: "2024-03-01",
      salary: 22000,
      status: "active",
      performance: {
        rating: 4.7,
        ordersCompleted: 245,
        avgPrepTime: 18,
        qualityScore: 4.6
      },
      todayStats: {
        ordersCompleted: 28,
        avgPrepTime: 16,
        hoursWorked: 6,
        efficiency: 95
      },
      workingHours: {
        startTime: "07:00",
        endTime: "16:00",
        totalHours: 9,
        workingDays: ["monday", "wednesday", "friday"],
        breakDuration: 45,
        overtimeThreshold: 150,
        earlyCheckoutThreshold: 90
      }
    },
    {
      id: 5,
      name: "Rohit Gupta",
      phone: "+91 54321 09876",
      role: "staff",
      shift: "evening",
      joinDate: "2024-02-15",
      salary: 17000,
      status: "on-leave",
      performance: {
        rating: 4.4,
        ordersServed: 98,
        customerFeedback: 4.3,
        punctuality: 82
      },
      todayStats: {
        ordersServed: 0,
        tablesManaged: 0,
        hoursWorked: 0,
        tips: 0
      },
      workingHours: {
        startTime: "09:00",
        endTime: "18:00",
        totalHours: 9,
        workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
        breakDuration: 30,
        overtimeThreshold: 120,
        earlyCheckoutThreshold: 60
      }
    }
  ];

  const roles = ["all", "admin", "staff"];
  const shifts = ["morning", "evening", "night", "full-day"];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800 border-green-200";
      case "on-leave": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "inactive": return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "admin": return <Star className="h-4 w-4" />;
      case "staff": return <Users className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const filteredStaff = staff.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.phone.includes(searchTerm);
    const matchesRole = selectedRole === "all" || member.role === selectedRole;
    
    return matchesSearch && matchesRole;
  });

  const staffStats = {
    total: staff.length,
    active: staff.filter(s => s.status === "active").length,
    onLeave: staff.filter(s => s.status === "on-leave").length,
    admins: staff.filter(s => s.role === "admin").length,
    staff: staff.filter(s => s.role === "staff").length
  };

  const handleViewDetails = (staffMember: any) => {
    setSelectedStaff(staffMember);
    setIsDetailsDialogOpen(true);
  };

  const handleAddStaff = () => {
    setNewStaffData({
      name: "",
      phone: "",
      role: "staff",
      shift: "morning",
      salary: ""
    });
    setIsAddStaffDialogOpen(true);
  };

  const handleSaveNewStaff = () => {
    // Mock save functionality
    alert(`New staff member ${newStaffData.name} added successfully!`);
    setIsAddStaffDialogOpen(false);
  };

  const handleStatusChange = (staffId: number, newStatus: string) => {
    // Mock status change
    alert(`Staff member status updated to ${newStatus}`);
  };

  const handleOpenWorkingHoursModal = () => {
    if (selectedStaff?.workingHours) {
      setWorkingHoursForm({ ...selectedStaff.workingHours });
    } else {
      setWorkingHoursForm({ ...getCurrentWorkingHours() });
    }
    setWorkingHoursErrors([]);
    setIsWorkingHoursModalOpen(true);
  };

  const handleWorkingHoursChange = (field: keyof WorkingHours, value: any) => {
    setWorkingHoursForm((prev) => prev ? { ...prev, [field]: value } : prev);
  };

  const handleWorkingDaysChange = (day: string) => {
    setWorkingHoursForm((prev) => {
      if (!prev) return prev;
      const days = prev.workingDays.includes(day)
        ? prev.workingDays.filter((d) => d !== day)
        : [...prev.workingDays, day];
      return { ...prev, workingDays: days };
    });
  };

  const handleSaveWorkingHours = () => {
    if (!workingHoursForm || !selectedStaff) return;
    const validation = validateWorkingHours(workingHoursForm);
    if (!validation.isValid) {
      setWorkingHoursErrors(validation.errors);
      return;
    }
    updateStaffPin(selectedStaff.id, { workingHours: workingHoursForm });
    setIsWorkingHoursModalOpen(false);
    setSelectedStaff({ ...selectedStaff, workingHours: workingHoursForm });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Staff Management</h1>
              <p className="text-sm text-gray-500">Manage restaurant staff and performance</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddStaff}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Add Staff
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600"
            >
              <RefreshCw className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Staff Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{staffStats.total}</p>
              <p className="text-sm text-blue-100">Total Staff</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{staffStats.active}</p>
              <p className="text-sm text-green-100">Active</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{staffStats.onLeave}</p>
              <p className="text-sm text-yellow-100">On Leave</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{staffStats.waiters}</p>
              <p className="text-sm text-purple-100">Waiters</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold">{staffStats.delivery}</p>
              <p className="text-sm text-orange-100">Delivery</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                >
                  {roles.map(role => (
                    <option key={role} value={role}>
                      {role === "all" ? "All Roles" : role.charAt(0).toUpperCase() + role.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Staff List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredStaff.map((member) => (
            <Card key={member.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {getRoleIcon(member.role)}
                      <div>
                        <h3 className="font-bold text-lg">{member.name}</h3>
                        <p className="text-sm text-gray-600 capitalize">{member.role}</p>
                      </div>
                    </div>
                    <Badge className={`${getStatusColor(member.status)}`}>
                      {member.status.replace("-", " ")}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(member)}
                      className="flex items-center gap-1"
                    >
                      <Eye className="h-4 w-4" />
                      View
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <p className="text-gray-500">Phone</p>
                    <p className="font-medium">{member.phone}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Shift</p>
                    <p className="font-medium capitalize">{member.shift}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Join Date</p>
                    <p className="font-medium">{member.joinDate}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Salary</p>
                    <p className="font-medium">₹{member.salary.toLocaleString()}</p>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <p className="text-sm font-medium mb-2">Today's Performance</p>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {member.role === "waiter" && (
                      <>
                        <div>
                          <span className="text-gray-500">Orders Served:</span>
                          <span className="font-medium ml-1">{member.todayStats.ordersServed}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Tables:</span>
                          <span className="font-medium ml-1">{member.todayStats.tablesManaged}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Hours:</span>
                          <span className="font-medium ml-1">{member.todayStats.hoursWorked}h</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Tips:</span>
                          <span className="font-medium ml-1">₹{member.todayStats.tips}</span>
                        </div>
                      </>
                    )}
                    {member.role === "delivery" && (
                      <>
                        <div>
                          <span className="text-gray-500">Deliveries:</span>
                          <span className="font-medium ml-1">{member.todayStats.deliveriesCompleted}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Distance:</span>
                          <span className="font-medium ml-1">{member.todayStats.distanceCovered}km</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Hours:</span>
                          <span className="font-medium ml-1">{member.todayStats.hoursWorked}h</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Earnings:</span>
                          <span className="font-medium ml-1">₹{member.todayStats.earnings}</span>
                        </div>
                      </>
                    )}
                    {member.role === "kitchen" && (
                      <>
                        <div>
                          <span className="text-gray-500">Orders:</span>
                          <span className="font-medium ml-1">{member.todayStats.ordersCompleted}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Avg Time:</span>
                          <span className="font-medium ml-1">{member.todayStats.avgPrepTime}min</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Hours:</span>
                          <span className="font-medium ml-1">{member.todayStats.hoursWorked}h</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Efficiency:</span>
                          <span className="font-medium ml-1">{member.todayStats.efficiency}%</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">{member.performance.rating}</span>
                  </div>
                  
                  {member.status === "active" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusChange(member.id, "on-leave")}
                      className="text-yellow-600 border-yellow-600 hover:bg-yellow-50"
                    >
                      Mark Leave
                    </Button>
                  )}
                  
                  {member.status === "on-leave" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusChange(member.id, "active")}
                      className="text-green-600 border-green-600 hover:bg-green-50"
                    >
                      Mark Active
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredStaff.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No staff members found matching your criteria</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Staff Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedStaff?.name} - Details</DialogTitle>
          </DialogHeader>
          {selectedStaff && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-gray-500">Role</Label>
                  <p className="font-medium capitalize">{selectedStaff.role}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-500">Status</Label>
                  <Badge className={`${getStatusColor(selectedStaff.status)} mt-1`}>
                    {selectedStaff.status.replace("-", " ")}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm text-gray-500">Phone</Label>
                  <p className="font-medium">{selectedStaff.phone}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-500">Shift</Label>
                  <p className="font-medium capitalize">{selectedStaff.shift}</p>
                </div>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <Label className="text-sm text-gray-500">Overall Performance</Label>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div>
                    <p className="text-sm">Rating: <span className="font-bold">{selectedStaff.performance.rating}/5</span></p>
                  </div>
                  {selectedStaff.role === "waiter" && (
                    <>
                      <div>
                        <p className="text-sm">Orders Served: <span className="font-bold">{selectedStaff.performance.ordersServed}</span></p>
                      </div>
                      <div>
                        <p className="text-sm">Customer Feedback: <span className="font-bold">{selectedStaff.performance.customerFeedback}/5</span></p>
                      </div>
                      <div>
                        <p className="text-sm">Punctuality: <span className="font-bold">{selectedStaff.performance.punctuality}%</span></p>
                      </div>
                    </>
                  )}
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleOpenWorkingHoursModal}
                className="mt-2"
              >
                Set Working Hours
              </Button>
              {selectedStaff?.workingHours && (
                <div className="text-xs text-gray-600 mt-1">
                  <span>Custom Working Hours: {selectedStaff.workingHours.startTime} - {selectedStaff.workingHours.endTime} ({selectedStaff.workingHours.totalHours}h)</span>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Staff Dialog */}
      <Dialog open={isAddStaffDialogOpen} onOpenChange={setIsAddStaffDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Staff Member</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={newStaffData.name}
                onChange={(e) => setNewStaffData({...newStaffData, name: e.target.value})}
                placeholder="Enter full name"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={newStaffData.phone}
                onChange={(e) => setNewStaffData({...newStaffData, phone: e.target.value})}
                placeholder="Enter phone number"
                className="mt-1"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="role">Role</Label>
                <select
                  id="role"
                  value={newStaffData.role}
                  onChange={(e) => setNewStaffData({...newStaffData, role: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm mt-1"
                >
                  <option value="admin">Admin</option>
                  <option value="staff">Staff</option>
                </select>
              </div>
              <div>
                <Label htmlFor="shift">Shift</Label>
                <select
                  id="shift"
                  value={newStaffData.shift}
                  onChange={(e) => setNewStaffData({...newStaffData, shift: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-200 rounded-md text-sm mt-1"
                >
                  {shifts.map(shift => (
                    <option key={shift} value={shift}>
                      {shift.charAt(0).toUpperCase() + shift.slice(1).replace("-", " ")}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div>
              <Label htmlFor="salary">Monthly Salary</Label>
              <Input
                id="salary"
                type="number"
                value={newStaffData.salary}
                onChange={(e) => setNewStaffData({...newStaffData, salary: e.target.value})}
                placeholder="Enter monthly salary"
                className="mt-1"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsAddStaffDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveNewStaff}>
                Add Staff Member
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Working Hours Modal */}
      <Dialog open={isWorkingHoursModalOpen} onOpenChange={setIsWorkingHoursModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Set Working Hours for {selectedStaff?.name}</DialogTitle>
          </DialogHeader>
          {workingHoursForm && (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label>Start Time</Label>
                  <Input type="time" value={workingHoursForm.startTime} onChange={e => handleWorkingHoursChange('startTime', e.target.value)} />
                </div>
                <div>
                  <Label>End Time</Label>
                  <Input type="time" value={workingHoursForm.endTime} onChange={e => handleWorkingHoursChange('endTime', e.target.value)} />
                </div>
              </div>
              <div>
                <Label>Total Hours</Label>
                <Input type="number" value={workingHoursForm.totalHours} onChange={e => handleWorkingHoursChange('totalHours', Number(e.target.value))} />
              </div>
              <div>
                <Label>Working Days</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {['monday','tuesday','wednesday','thursday','friday','saturday','sunday'].map(day => (
                    <Button key={day} size="sm" variant={workingHoursForm.workingDays.includes(day) ? 'default' : 'outline'} onClick={() => handleWorkingDaysChange(day)}>
                      {day.charAt(0).toUpperCase() + day.slice(1,3)}
                    </Button>
                  ))}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label>Break Duration (min)</Label>
                  <Input type="number" value={workingHoursForm.breakDuration} onChange={e => handleWorkingHoursChange('breakDuration', Number(e.target.value))} />
                </div>
                <div>
                  <Label>Overtime Threshold (min)</Label>
                  <Input type="number" value={workingHoursForm.overtimeThreshold} onChange={e => handleWorkingHoursChange('overtimeThreshold', Number(e.target.value))} />
                </div>
              </div>
              <div>
                <Label>Early Checkout Threshold (min)</Label>
                <Input type="number" value={workingHoursForm.earlyCheckoutThreshold} onChange={e => handleWorkingHoursChange('earlyCheckoutThreshold', Number(e.target.value))} />
              </div>
              {workingHoursErrors.length > 0 && (
                <div className="text-red-600 text-xs">
                  {workingHoursErrors.map((err, i) => <div key={i}>{err}</div>)}
                </div>
              )}
              <div className="flex gap-2 justify-end mt-2">
                <Button variant="outline" onClick={() => setIsWorkingHoursModalOpen(false)}>Cancel</Button>
                <Button onClick={handleSaveWorkingHours}>Save</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StaffManagement;
