import React, { useState, useEffect } from 'react';
import { Search, Plus, User, Phone, Star, X, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Customer, customerManager, getCustomerDisplayName } from '@/utils/customerStorage';

interface CustomerSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCustomerSelect: (customer: Customer | null) => void;
  title?: string;
  description?: string;
  showEditButtons?: boolean;
  showPhoneNumbers?: boolean;
}

const CustomerSelectionDialog: React.FC<CustomerSelectionDialogProps> = ({
  isOpen,
  onClose,
  onCustomerSelect,
  title = "Select Customer",
  description = "Choose an existing customer or add a new one for this order",
  showEditButtons = true,
  showPhoneNumbers = true
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [isEditingCustomer, setIsEditingCustomer] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [newCustomerData, setNewCustomerData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    notes: ''
  });

  useEffect(() => {
    if (isOpen) {
      loadCustomers();
      setSearchQuery('');
      setSelectedCustomer(null);
      setIsAddingNew(false);
      setNewCustomerData({ name: '', phone: '', email: '', address: '', notes: '' });
    }
  }, [isOpen]);

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = customerManager.searchCustomers(searchQuery);
      setFilteredCustomers(filtered);
    } else {
      setFilteredCustomers(customers);
    }
  }, [searchQuery, customers]);

  const loadCustomers = () => {
    const allCustomers = customerManager.getAllCustomers();
    setCustomers(allCustomers);
    setFilteredCustomers(allCustomers);
  };

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
  };

  const handleConfirmSelection = () => {
    onCustomerSelect(selectedCustomer);
    onClose();
  };

  const handleSkipCustomer = () => {
    onCustomerSelect(null);
    onClose();
  };

  const handleAddNewCustomer = () => {
    if (!newCustomerData.name.trim()) {
      return;
    }

    // Check if customer with same name already exists
    const existingCustomer = customerManager.getCustomerByName(newCustomerData.name);
    if (existingCustomer) {
      setSelectedCustomer(existingCustomer);
      setIsAddingNew(false);
      return;
    }

    const newCustomer = customerManager.addCustomer({
      name: newCustomerData.name.trim(),
      phone: newCustomerData.phone.trim() || undefined,
      email: newCustomerData.email.trim() || undefined,
      address: newCustomerData.address.trim() || undefined,
      notes: newCustomerData.notes.trim() || undefined
    });

    setSelectedCustomer(newCustomer);
    setIsAddingNew(false);
    loadCustomers();
  };

  const handleNewCustomerInputChange = (field: string, value: string) => {
    setNewCustomerData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditCustomer = (customer: Customer, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingCustomer(customer);
    setNewCustomerData({
      name: customer.name,
      phone: customer.phone,
      email: customer.email || '',
      address: customer.address || '',
      notes: customer.notes || ''
    });
    setIsEditingCustomer(true);
  };

  const handleUpdateCustomer = () => {
    if (!editingCustomer || !newCustomerData.name.trim() || !newCustomerData.phone.trim()) {
      return;
    }

    const updates = {
      name: newCustomerData.name.trim(),
      phone: newCustomerData.phone.trim(),
      email: newCustomerData.email.trim() || undefined,
      address: newCustomerData.address.trim() || undefined,
      notes: newCustomerData.notes.trim() || undefined,
      updatedAt: new Date().toISOString()
    };

    customerManager.updateCustomer(editingCustomer.id, updates);
    loadCustomers();
    setIsEditingCustomer(false);
    setEditingCustomer(null);
    setNewCustomerData({ name: '', phone: '', email: '', address: '', notes: '' });
  };

  const getRecentCustomers = () => {
    return customerManager.getRecentCustomers();
  };

  const getRegularCustomers = () => {
    return customerManager.getRegularCustomers();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[95vh] p-0 gap-0 flex flex-col mx-auto">
        <DialogHeader className="p-4 sm:p-6 pb-2 sm:pb-4 shrink-0">
          <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <User className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription className="text-sm sm:text-base mt-1">
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="px-4 sm:px-6 flex-1 overflow-hidden flex flex-col">
          <div className="flex-1 overflow-y-auto space-y-4 pb-4">
            {!isAddingNew && !isEditingCustomer ? (
            <>
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search customers by name or phone..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-10 sm:h-11"
                />
              </div>

              {/* Customer Tabs */}
              <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid w-full grid-cols-3 h-9 sm:h-10">
                  <TabsTrigger value="all" className="text-xs sm:text-sm">All Customers</TabsTrigger>
                  <TabsTrigger value="recent" className="text-xs sm:text-sm">Recent</TabsTrigger>
                  <TabsTrigger value="regular" className="text-xs sm:text-sm">Regular</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-3 sm:mt-4">
                  <ScrollArea className="h-48 sm:h-64">
                    <div className="space-y-2 pr-2">
                      {filteredCustomers.length === 0 ? (
                        <div className="text-center py-6 sm:py-8 text-gray-500">
                          <User className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-2 text-gray-300" />
                          <p className="text-sm sm:text-base">No customers found</p>
                          {searchQuery && (
                            <p className="text-xs sm:text-sm mt-1">Try a different search term</p>
                          )}
                        </div>
                      ) : (
                        filteredCustomers.map((customer) => (
                          <div
                            key={customer.id}
                            className={`p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors ${
                              selectedCustomer?.id === customer.id
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                            }`}
                            onClick={() => handleCustomerSelect(customer)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-medium text-sm sm:text-base truncate">{customer.name}</h4>
                                </div>
                                <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs sm:text-sm text-gray-600">
                                  {showPhoneNumbers && customer.phone && (
                                    <span className="flex items-center gap-1">
                                      <Phone className="h-3 w-3 shrink-0" />
                                      <span className="truncate">{customer.phone}</span>
                                    </span>
                                  )}
                                  <div className="flex items-center gap-3 sm:gap-4">
                                    <span>{customer.totalOrders} orders</span>
                                    <span>₹{customer.totalSpent} spent</span>
                                  </div>
                                </div>
                              </div>
                              {showEditButtons && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => handleEditCustomer(customer, e)}
                                  className="ml-2 h-8 w-8 p-0 shrink-0"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="recent" className="mt-3 sm:mt-4">
                  <ScrollArea className="h-48 sm:h-64">
                    <div className="space-y-2 pr-2">
                      {getRecentCustomers().length === 0 ? (
                        <div className="text-center py-6 sm:py-8 text-gray-500">
                          <User className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-2 text-gray-300" />
                          <p className="text-sm sm:text-base">No recent customers</p>
                          <p className="text-xs sm:text-sm mt-1">Recent customers will appear here</p>
                        </div>
                      ) : (
                        getRecentCustomers().map((customer) => (
                        <div
                          key={customer.id}
                          className={`p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors ${
                            selectedCustomer?.id === customer.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          onClick={() => handleCustomerSelect(customer)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-sm sm:text-base truncate">{getCustomerDisplayName(customer)}</h4>
                              <p className="text-xs sm:text-sm text-gray-600 mt-1">
                                Last visit: {new Date(customer.lastVisit).toLocaleDateString()}
                              </p>
                            </div>
                            {showEditButtons && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => handleEditCustomer(customer, e)}
                                className="ml-2 h-8 w-8 p-0 shrink-0"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="regular" className="mt-3 sm:mt-4">
                  <ScrollArea className="h-48 sm:h-64">
                    <div className="space-y-2 pr-2">
                      {getRegularCustomers().length === 0 ? (
                        <div className="text-center py-6 sm:py-8 text-gray-500">
                          <Star className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-2 text-gray-300" />
                          <p className="text-sm sm:text-base">No regular customers yet</p>
                          <p className="text-xs sm:text-sm mt-1">Customers with 3+ orders become regular</p>
                        </div>
                      ) : (
                        getRegularCustomers().map((customer) => (
                        <div
                          key={customer.id}
                          className={`p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors ${
                            selectedCustomer?.id === customer.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          onClick={() => handleCustomerSelect(customer)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm sm:text-base truncate">{customer.name}</h4>
                              </div>
                              <p className="text-xs sm:text-sm text-gray-600">
                                {customer.totalOrders} orders • ₹{customer.totalSpent} total
                              </p>
                            </div>
                            {showEditButtons && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => handleEditCustomer(customer, e)}
                                className="ml-2 h-8 w-8 p-0 shrink-0"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </>
          ) : (
            /* Add New Customer or Edit Customer Form */
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-base sm:text-lg font-medium">
                  {isEditingCustomer ? 'Edit Customer' : 'Add New Customer'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsAddingNew(false);
                    setIsEditingCustomer(false);
                    setEditingCustomer(null);
                    setNewCustomerData({ name: '', phone: '', email: '', address: '', notes: '' });
                  }}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-3">
                <div>
                  <Label htmlFor="customer-name" className="text-sm font-medium">Name *</Label>
                  <Input
                    id="customer-name"
                    placeholder="Customer name"
                    value={newCustomerData.name}
                    onChange={(e) => handleNewCustomerInputChange('name', e.target.value)}
                    className="mt-1 h-9 sm:h-10"
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="customer-phone" className="text-sm font-medium">Phone</Label>
                    <Input
                      id="customer-phone"
                      placeholder="Phone number"
                      value={newCustomerData.phone}
                      onChange={(e) => handleNewCustomerInputChange('phone', e.target.value)}
                      className="mt-1 h-9 sm:h-10"
                    />
                  </div>
                  <div>
                    <Label htmlFor="customer-email" className="text-sm font-medium">Email</Label>
                    <Input
                      id="customer-email"
                      type="email"
                      placeholder="Email address"
                      value={newCustomerData.email}
                      onChange={(e) => handleNewCustomerInputChange('email', e.target.value)}
                      className="mt-1 h-9 sm:h-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="customer-address" className="text-sm font-medium">Address</Label>
                  <Input
                    id="customer-address"
                    placeholder="Customer address"
                    value={newCustomerData.address}
                    onChange={(e) => handleNewCustomerInputChange('address', e.target.value)}
                    className="mt-1 h-9 sm:h-10"
                  />
                </div>

                <div>
                  <Label htmlFor="customer-notes" className="text-sm font-medium">Notes</Label>
                  <Input
                    id="customer-notes"
                    placeholder="Any special notes"
                    value={newCustomerData.notes}
                    onChange={(e) => handleNewCustomerInputChange('notes', e.target.value)}
                    className="mt-1 h-9 sm:h-10"
                  />
                </div>
              </div>
            </div>
            )}
          </div>

          {/* Fixed Action Buttons Footer */}
          <div className="shrink-0 p-4 sm:p-6 pt-3 border-t bg-white space-y-3">
            {/* Top row - Skip and Add New buttons */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button
                variant="outline"
                onClick={handleSkipCustomer}
                className="flex-1 h-10 sm:h-11 text-sm"
              >
                Skip Customer
              </Button>
              {!isAddingNew && !isEditingCustomer && (
                <Button
                  variant="outline"
                  onClick={() => setIsAddingNew(true)}
                  className="flex-1 h-10 sm:h-11 text-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New
                </Button>
              )}
            </div>

            {/* Bottom row - Cancel and Action buttons */}
            <div className="flex gap-2 sm:gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                className="flex-1 h-10 sm:h-11 text-sm"
              >
                Cancel
              </Button>
              {isAddingNew ? (
                <Button
                  onClick={handleAddNewCustomer}
                  disabled={!newCustomerData.name.trim()}
                  className="flex-1 h-10 sm:h-11 text-sm"
                >
                  Add Customer
                </Button>
              ) : isEditingCustomer ? (
                <Button
                  onClick={handleUpdateCustomer}
                  disabled={!newCustomerData.name.trim()}
                  className="flex-1 h-10 sm:h-11 text-sm"
                >
                  Update Customer
                </Button>
              ) : (
                <Button
                  onClick={handleConfirmSelection}
                  disabled={!selectedCustomer}
                  className="flex-1 h-10 sm:h-11 text-sm"
                >
                  Select Customer
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CustomerSelectionDialog;
