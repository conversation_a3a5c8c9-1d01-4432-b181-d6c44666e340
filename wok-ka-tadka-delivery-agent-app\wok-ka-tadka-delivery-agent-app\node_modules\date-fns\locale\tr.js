"use strict";
exports.tr = void 0;
var _index = require("./tr/_lib/formatDistance.js");
var _index2 = require("./tr/_lib/formatLong.js");
var _index3 = require("./tr/_lib/formatRelative.js");
var _index4 = require("./tr/_lib/localize.js");
var _index5 = require("./tr/_lib/match.js");

/**
 * @category Locales
 * @summary Turkish locale.
 * @language Turkish
 * @iso-639-2 tur
 * <AUTHOR> [@alpcanaydin](https://github.com/alpcanaydin)
 * <AUTHOR> [@berkaey](https://github.com/berkaey)
 * <AUTHOR> [@bulutfatih](https://github.com/bulutfatih)
 * <AUTHOR> [@dbtek](https://github.com/dbtek)
 * <AUTHOR> [@ikayar](https://github.com/ikayar)
 *
 *
 */
const tr = (exports.tr = {
  code: "tr",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    week<PERSON>tartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
