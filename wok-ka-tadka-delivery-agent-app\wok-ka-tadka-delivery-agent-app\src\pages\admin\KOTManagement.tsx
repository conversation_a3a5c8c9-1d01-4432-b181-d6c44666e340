import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Search,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Printer,
  Eye,
  RefreshCw,
  Utensils,
  FileText,
  Users,
  Package,
  X
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import PrintService from "@/services/printService";

const AdminKOTManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedTab, setSelectedTab] = useState("active");
  const [selectedKOT, setSelectedKOT] = useState<any>(null);
  const [isKOTDetailsOpen, setIsKOTDetailsOpen] = useState(false);

  // Mock KOT data
  const kots = [
    {
      id: "KOT001",
      tableNumber: 3,
      orderNumber: "#ORD001",
      customerName: "Raj Kumar",
      waiter: "Sunita Devi",
      items: [
        { name: "Chicken Biryani", quantity: 2, price: 320, status: "preparing", priority: "normal" },
        { name: "Paneer Butter Masala", quantity: 1, price: 280, status: "ready", priority: "normal" },
        { name: "Garlic Naan", quantity: 3, price: 45, status: "preparing", priority: "normal" }
      ],
      totalItems: 6,
      totalAmount: 1055, // (320*2) + (280*1) + (45*3) = 640 + 280 + 135 = 1055
      status: "preparing",
      createdAt: "12:30 PM",
      orderTime: "12:30 PM",
      estimatedTime: "15 mins",
      specialInstructions: "Less spicy biryani",
      priority: "normal"
    },
    {
      id: "KOT002",
      tableNumber: 7,
      orderNumber: "#ORD002",
      customerName: "Priya Sharma",
      waiter: "Raj Kumar",
      items: [
        { name: "Mutton Curry", quantity: 1, price: 450, status: "ready", priority: "high" },
        { name: "Jeera Rice", quantity: 2, price: 120, status: "ready", priority: "normal" },
        { name: "Raita", quantity: 1, price: 80, status: "ready", priority: "normal" }
      ],
      totalItems: 4,
      totalAmount: 770, // (450*1) + (120*2) + (80*1) = 450 + 240 + 80 = 770
      status: "ready",
      createdAt: "12:45 PM",
      orderTime: "12:45 PM",
      completedAt: "1:15 PM",
      specialInstructions: "",
      priority: "high"
    },
    {
      id: "KOT003",
      tableNumber: 5,
      orderNumber: "#ORD003",
      customerName: "Rohit Gupta",
      waiter: "Sunita Devi",
      items: [
        { name: "Dal Makhani", quantity: 2, price: 240, status: "completed", priority: "normal" },
        { name: "Butter Naan", quantity: 4, price: 55, status: "completed", priority: "normal" },
        { name: "Lassi", quantity: 2, price: 90, status: "completed", priority: "normal" }
      ],
      totalItems: 8,
      totalAmount: 880, // (240*2) + (55*4) + (90*2) = 480 + 220 + 180 = 880
      status: "completed",
      createdAt: "11:15 AM",
      orderTime: "11:15 AM",
      completedAt: "11:45 AM",
      specialInstructions: "Extra butter on naan",
      priority: "normal"
    },
    {
      id: "KOT004",
      tableNumber: 2,
      orderNumber: "#ORD004",
      customerName: "Anjali Singh",
      waiter: "Raj Kumar",
      items: [
        { name: "Chicken Tikka", quantity: 1, price: 350, status: "cancelled", priority: "normal" },
        { name: "Mint Chutney", quantity: 2, price: 25, status: "cancelled", priority: "normal" }
      ],
      totalItems: 3,
      totalAmount: 400, // (350*1) + (25*2) = 350 + 50 = 400
      status: "cancelled",
      createdAt: "1:20 PM",
      orderTime: "1:20 PM",
      cancelledAt: "1:25 PM",
      cancelReason: "Customer cancelled order",
      specialInstructions: "",
      priority: "normal"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "preparing": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "ready": return "bg-green-100 text-green-800 border-green-200";
      case "completed": return "bg-blue-100 text-blue-800 border-blue-200";
      case "cancelled": return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "preparing": return <Clock className="h-4 w-4" />;
      case "ready": return <CheckCircle className="h-4 w-4" />;
      case "completed": return <CheckCircle className="h-4 w-4" />;
      case "cancelled": return <XCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-500";
      case "medium": return "bg-yellow-500";
      case "low": return "bg-green-500";
      default: return "bg-gray-500";
    }
  };

  const getFilteredKOTs = () => {
    let filtered = kots;
    
    if (selectedTab === "active") {
      filtered = kots.filter(kot => kot.status === "preparing" || kot.status === "ready");
    } else if (selectedTab === "completed") {
      filtered = kots.filter(kot => kot.status === "completed");
    } else if (selectedTab === "cancelled") {
      filtered = kots.filter(kot => kot.status === "cancelled");
    }

    if (searchTerm) {
      filtered = filtered.filter(kot => 
        kot.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kot.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        kot.orderNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedStatus !== "all") {
      filtered = filtered.filter(kot => kot.status === selectedStatus);
    }

    return filtered;
  };

  const kotStats = {
    total: kots.length,
    preparing: kots.filter(k => k.status === "preparing").length,
    ready: kots.filter(k => k.status === "ready").length,
    completed: kots.filter(k => k.status === "completed").length,
    cancelled: kots.filter(k => k.status === "cancelled").length
  };

  const handleStatusUpdate = (kotId: string, newStatus: string) => {
    // Mock status update
    alert(`KOT ${kotId} status updated to ${newStatus}`);
  };

  // Generate KOT content for printing
  const generateKOTContent = (kot: any): string => {
    const currentDate = new Date().toLocaleDateString('en-IN');
    const currentTime = new Date().toLocaleTimeString('en-IN');

    const itemsSection = kot.items.map((item: any) =>
      `<tr>
        <td>${item.name}</td>
        <td>${item.quantity}</td>
        <td>-</td>
      </tr>`
    ).join('');

    const instructionsSection = kot.specialInstructions ?
      `<div style="margin: 20px 0;">
        <strong>Special Instructions:</strong> ${kot.specialInstructions}
      </div>` : '';

    return `<!DOCTYPE html>
<html>
<head>
  <title>KOT - Table ${kot.tableNumber}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { text-align: center; margin-bottom: 20px; }
    .restaurant-name { font-size: 24px; font-weight: bold; }
    .kot-info { margin: 20px 0; }
    table { width: 100%; border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    @media print { body { margin: 0; } }
  </style>
</head>
<body>
  <div class="header">
    <div class="restaurant-name">🥢 Wok Ka Tadka</div>
    <div>Mumbai Style Chinese & Indian</div>
    <div style="margin-top: 10px; font-size: 16px; font-weight: bold;">KITCHEN ORDER TICKET</div>
  </div>

  <div class="kot-info">
    <strong>KOT No:</strong> ${kot.id}<br>
    <strong>Table:</strong> ${kot.tableNumber}<br>
    <strong>Date:</strong> ${currentDate}<br>
    <strong>Time:</strong> ${currentTime}
  </div>

  <table>
    <thead>
      <tr>
        <th>Item</th>
        <th>Qty</th>
        <th>Special Notes</th>
      </tr>
    </thead>
    <tbody>
      ${itemsSection}
    </tbody>
  </table>

  ${instructionsSection}

  <div style="text-align: center; margin-top: 20px;">
    <p><strong>Total Items: ${kot.items.reduce((sum: number, item: any) => sum + item.quantity, 0)}</strong></p>
    <p>Please prepare items as per order</p>
  </div>
</body>
</html>`;
  };

  const handlePrintKOT = async (kotId: string) => {
    try {
      const kot = kots.find(k => k.id === kotId);
      if (!kot) {
        toast({
          title: "Error",
          description: "KOT not found",
          variant: "destructive"
        });
        return;
      }

      // Print the KOT using unified print service
      const success = await PrintService.printKOT(kot);

      if (success) {
        toast({
          title: "KOT Printed Successfully!",
          description: `KOT ${kot.id} printed for Table ${kot.tableNumber}`,
        });
      } else {
        throw new Error("Print service failed");
      }
    } catch (error) {
      toast({
        title: "Print Error",
        description: "Failed to print KOT. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleViewDetails = (kotId: string) => {
    const kot = kots.find(k => k.id === kotId);
    if (kot) {
      setSelectedKOT(kot);
      setIsKOTDetailsOpen(true);
    }
  };

  const filteredKOTs = getFilteredKOTs();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="flex items-center justify-between p-3 sm:p-4">
          <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/dashboard")}
              className="text-gray-600 shrink-0"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">KOT Management</h1>
              <p className="text-xs sm:text-sm text-gray-500 truncate">Kitchen Order Ticket management</p>
            </div>
          </div>
          <div className="flex items-center gap-2 shrink-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.location.reload()}
              className="text-gray-600"
            >
              <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-3 sm:p-6 space-y-4 sm:space-y-6">
        {/* KOT Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{kotStats.total}</p>
              <p className="text-xs sm:text-sm text-blue-100">Total KOTs</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{kotStats.preparing}</p>
              <p className="text-xs sm:text-sm text-yellow-100">Preparing</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{kotStats.ready}</p>
              <p className="text-xs sm:text-sm text-green-100">Ready</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{kotStats.completed}</p>
              <p className="text-xs sm:text-sm text-blue-100">Completed</p>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
            <CardContent className="p-3 sm:p-4 text-center">
              <p className="text-lg sm:text-2xl font-bold">{kotStats.cancelled}</p>
              <p className="text-xs sm:text-sm text-red-100">Cancelled</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by KOT ID, customer name, or order number..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="preparing">Preparing</option>
                  <option value="ready">Ready</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* KOT Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All KOTs</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
          </TabsList>

          <TabsContent value={selectedTab} className="mt-6">
            <div className="space-y-4">
              {filteredKOTs.map((kot) => (
                <Card key={kot.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-4 gap-3 sm:gap-4">
                      <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                        <div className="flex items-center gap-2 shrink-0">
                          <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-gray-600" />
                          <span className="font-bold text-base sm:text-lg">{kot.id}</span>
                          <div className={`w-2 h-2 rounded-full ${getPriorityColor(kot.priority)}`} />
                        </div>
                        <Badge className={`${getStatusColor(kot.status)} flex items-center gap-1 text-xs`}>
                          {getStatusIcon(kot.status)}
                          {kot.status.charAt(0).toUpperCase() + kot.status.slice(1)}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 shrink-0 justify-end sm:justify-start">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(kot.id)}
                          className="flex items-center gap-1 text-xs sm:text-sm h-8 sm:h-9"
                        >
                          <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span className="hidden sm:inline">View</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePrintKOT(kot.id)}
                          className="flex items-center gap-1 text-xs sm:text-sm h-8 sm:h-9"
                        >
                          <Printer className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span className="hidden sm:inline">Print</span>
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-xs sm:text-sm text-gray-500">Table & Order</p>
                        <p className="font-medium text-sm sm:text-base">Table {kot.tableNumber}</p>
                        <p className="text-xs sm:text-sm text-gray-600">{kot.orderNumber}</p>
                      </div>

                      <div>
                        <p className="text-xs sm:text-sm text-gray-500">Customer & Waiter</p>
                        <p className="font-medium text-sm sm:text-base truncate">{kot.customerName}</p>
                        <p className="text-xs sm:text-sm text-gray-600 truncate">Waiter: {kot.waiter}</p>
                      </div>

                      <div className="sm:col-span-2 lg:col-span-1">
                        <p className="text-xs sm:text-sm text-gray-500">Timing</p>
                        <p className="font-medium text-sm sm:text-base">{kot.createdAt}</p>
                        {kot.estimatedTime && (
                          <p className="text-xs sm:text-sm text-orange-600">ETA: {kot.estimatedTime}</p>
                        )}
                        {kot.completedAt && (
                          <p className="text-xs sm:text-sm text-green-600">Completed: {kot.completedAt}</p>
                        )}
                        {kot.cancelledAt && (
                          <p className="text-xs sm:text-sm text-red-600">Cancelled: {kot.cancelledAt}</p>
                        )}
                      </div>
                    </div>

                    <div className="mb-4">
                      <p className="text-xs sm:text-sm text-gray-500 mb-2">Items ({kot.totalItems})</p>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
                        {kot.items.map((item, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center gap-2 min-w-0 flex-1 pr-2">
                              <span className="text-xs sm:text-sm font-medium shrink-0">{item.quantity}x</span>
                              <span className="text-xs sm:text-sm truncate">{item.name}</span>
                            </div>
                            <Badge className={`${getStatusColor(item.status)} text-xs shrink-0`}>
                              {item.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {kot.specialInstructions && (
                      <div className="mb-4">
                        <p className="text-xs sm:text-sm text-gray-500">Special Instructions</p>
                        <p className="text-xs sm:text-sm text-orange-600 bg-orange-50 p-2 rounded">
                          {kot.specialInstructions}
                        </p>
                      </div>
                    )}

                    {kot.cancelReason && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-500">Cancel Reason</p>
                        <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                          {kot.cancelReason}
                        </p>
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row sm:items-center justify-between pt-4 border-t gap-3 sm:gap-4">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <Users className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 shrink-0" />
                        <span className="text-xs sm:text-sm text-gray-600 truncate">Table {kot.tableNumber}</span>
                        <Package className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 ml-2 shrink-0" />
                        <span className="text-xs sm:text-sm text-gray-600 truncate">{kot.totalItems} items</span>
                      </div>

                      {kot.status === "preparing" && (
                        <div className="flex gap-2 shrink-0 justify-end sm:justify-start">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusUpdate(kot.id, "ready")}
                            className="text-green-600 border-green-600 hover:bg-green-50 text-xs sm:text-sm h-8 sm:h-9"
                          >
                            <span className="hidden sm:inline">Mark Ready</span>
                            <span className="sm:hidden">Ready</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusUpdate(kot.id, "cancelled")}
                            className="text-red-600 border-red-600 hover:bg-red-50 text-xs sm:text-sm h-8 sm:h-9"
                          >
                            <span className="hidden sm:inline">Cancel</span>
                            <span className="sm:hidden">Cancel</span>
                          </Button>
                        </div>
                      )}

                      {kot.status === "ready" && (
                        <div className="shrink-0 flex justify-end sm:justify-start">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusUpdate(kot.id, "completed")}
                            className="text-blue-600 border-blue-600 hover:bg-blue-50 text-xs sm:text-sm h-8 sm:h-9"
                          >
                            <span className="hidden sm:inline">Mark Served</span>
                            <span className="sm:hidden">Served</span>
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredKOTs.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No KOTs found matching your criteria</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* KOT Details Dialog */}
      <Dialog open={isKOTDetailsOpen} onOpenChange={setIsKOTDetailsOpen}>
        <DialogContent className="max-w-lg sm:max-w-2xl max-h-[95vh] w-[95vw] sm:w-full overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0 pb-4">
            <DialogTitle className="flex items-center gap-2 text-lg">
              <FileText className="h-5 w-5" />
              KOT Details - {selectedKOT?.id}
            </DialogTitle>
          </DialogHeader>

          {selectedKOT && (
            <>
              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto space-y-4 pr-2">
              {/* KOT Header Info */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-600">Table Number</p>
                  <p className="font-semibold">Table {selectedKOT.tableNumber}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Status</p>
                  <Badge className={`${getStatusColor(selectedKOT.status)}`}>
                    {selectedKOT.status.charAt(0).toUpperCase() + selectedKOT.status.slice(1)}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Order Time</p>
                  <p className="font-semibold">{selectedKOT.orderTime}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Amount</p>
                  <p className="font-semibold text-green-600">₹{selectedKOT.totalAmount}</p>
                </div>
              </div>

              {/* Items List */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Utensils className="h-4 w-4" />
                  Ordered Items ({selectedKOT.totalItems})
                </h3>
                <div className="space-y-2">
                  {selectedKOT.items.map((item: any, index: number) => (
                    <div key={index} className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 bg-white border rounded-lg gap-2">
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm sm:text-base truncate">{item.name}</p>
                        <p className="text-xs sm:text-sm text-gray-600">Qty: {item.quantity}</p>
                        {item.specialInstructions && (
                          <p className="text-xs text-orange-600 mt-1">Note: {item.specialInstructions}</p>
                        )}
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <p className="font-semibold text-sm sm:text-base">₹{item.price * item.quantity}</p>
                        <p className="text-xs sm:text-sm text-gray-600">₹{item.price} each</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Special Instructions */}
              {selectedKOT.specialInstructions && (
                <div className="p-4 bg-orange-50 rounded-lg">
                  <h3 className="font-semibold mb-2 text-orange-800">Special Instructions</h3>
                  <p className="text-orange-700">{selectedKOT.specialInstructions}</p>
                </div>
              )}
              </div>

              {/* Fixed Action Buttons */}
              <div className="flex-shrink-0 flex flex-wrap gap-2 pt-4 border-t mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePrintKOT(selectedKOT.id)}
                  className="flex items-center gap-1 text-xs sm:text-sm"
                >
                  <Printer className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Print KOT</span>
                  <span className="sm:hidden">Print</span>
                </Button>
                {selectedKOT.status === "preparing" && (
                  <>
                    <Button
                      size="sm"
                      onClick={() => {
                        handleStatusUpdate(selectedKOT.id, "ready");
                        setIsKOTDetailsOpen(false);
                      }}
                      className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-xs sm:text-sm"
                    >
                      <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Mark Ready</span>
                      <span className="sm:hidden">Ready</span>
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        handleStatusUpdate(selectedKOT.id, "cancelled");
                        setIsKOTDetailsOpen(false);
                      }}
                      className="flex items-center gap-1 text-xs sm:text-sm"
                    >
                      <XCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Cancel</span>
                      <span className="sm:hidden">Cancel</span>
                    </Button>
                  </>
                )}
                {selectedKOT.status === "ready" && (
                  <Button
                    size="sm"
                    onClick={() => {
                      handleStatusUpdate(selectedKOT.id, "completed");
                      setIsKOTDetailsOpen(false);
                    }}
                    className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-xs sm:text-sm"
                  >
                    <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden sm:inline">Mark Served</span>
                    <span className="sm:hidden">Served</span>
                  </Button>
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminKOTManagement;
