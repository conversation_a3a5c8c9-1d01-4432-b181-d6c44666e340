@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Wok Ka Tadka Orange Theme */
    --primary: 16 89% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 16 89% 68%;
    --primary-dark: 16 89% 48%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 39 100% 97%;
    --accent-foreground: 16 89% 58%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 16 89% 58%;

    /* Custom tokens for delivery app */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--primary)),
      hsl(var(--primary-light))
    );
    --shadow-soft: 0 4px 20px hsla(var(--primary), 0.15);
    --shadow-card: 0 2px 10px hsla(0, 0%, 0%, 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }

  body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

@layer utilities {
  .bg-gradient-primary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
  }

  .shadow-soft {
    box-shadow: 0 4px 20px hsla(var(--primary), 0.15);
  }

  .shadow-card {
    box-shadow: 0 2px 10px hsla(0, 0%, 0%, 0.1);
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  /* Mobile-specific utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Improved mobile tap targets */
  .tap-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better mobile text rendering */
  .text-rendering-optimized {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Simple and effective mobile fixes */
  .mobile-viewport {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  /* Login page specific fix to prevent white space */
  .login-container {
    height: 100vh;
    height: -webkit-fill-available;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
  }

  /* Comprehensive Safe Area Handling for APK */
  .mobile-safe-header {
    position: sticky;
    top: 0;
    z-index: 50;
    padding-top: env(safe-area-inset-top);
  }

  .mobile-safe-content {
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .mobile-button-container {
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* APK-specific safe area classes */
  .apk-safe-top {
    padding-top: env(safe-area-inset-top);
    margin-top: env(safe-area-inset-top);
  }

  .apk-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
  }

  .apk-safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .apk-safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Fixed header that respects safe areas */
  .apk-header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Content that accounts for fixed header */
  .apk-content-with-header {
    padding-top: calc(
      env(safe-area-inset-top) + 64px
    ); /* 64px is typical header height */
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Content with no gap between header and content */
  .apk-content-with-header-no-gap {
    padding-top: calc(env(safe-area-inset-top) + 80px);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Fixed footer/button that respects safe areas */
  .apk-footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding-bottom: calc(env(safe-area-inset-bottom) + 8px);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Content that accounts for fixed footer */
  .apk-content-with-footer {
    padding-bottom: calc(
      env(safe-area-inset-bottom) + 80px
    ); /* 80px is typical footer height */
  }

  /* Content with large footer (for KOT view mode with GST toggle and multiple buttons) */
  .apk-content-with-large-footer {
    padding-bottom: calc(
      env(safe-area-inset-bottom) + 450px
    ) !important; /* 450px for large footer with GST toggle and multiple buttons */
  }

  /* Content with extra large footer (for waiter dashboard KOT view with more buttons) */
  .apk-content-with-extra-large-footer {
    padding-bottom: calc(
      env(safe-area-inset-bottom) + 550px
    ) !important; /* 550px for waiter dashboard KOT view with GST toggle and multiple buttons */
  }

  /* Clean Capacitor app container */
  .capacitor-app {
    height: 100vh;
    height: -webkit-fill-available;
    max-height: 100vh;
    max-height: -webkit-fill-available;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Android back button visual feedback */
  .back-button-highlight {
    transition: background-color 0.15s ease;
  }

  /* Prevent content from going behind system UI */
  .apk-page-container {
    min-height: 100vh;
    min-height: -webkit-fill-available;
    display: flex;
    flex-direction: column;
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    overflow-x: hidden;
  }

  /* Ensure buttons are always accessible */
  .apk-button-safe {
    margin-bottom: calc(env(safe-area-inset-bottom) + 8px);
    margin-left: env(safe-area-inset-left);
    margin-right: env(safe-area-inset-right);
  }

  .back-button-highlight:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseSoft {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* APK-specific styles for mobile navigation safety */
.apk-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.apk-button-safe {
  margin-bottom: env(safe-area-inset-bottom, 20px);
}

.apk-content-with-header {
  padding-top: calc(env(safe-area-inset-top) + 80px) !important;
  margin-top: 0 !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
  padding-left: env(safe-area-inset-left) !important;
  padding-right: env(safe-area-inset-right) !important;
}

/* Content with medium header (for table order with search + filter) */
.apk-content-with-medium-header {
  padding-top: calc(env(safe-area-inset-top) + 160px) !important;
  margin-top: 0 !important;
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Content with tall header (for pages with search + tabs) */
.apk-content-with-tall-header {
  padding-top: calc(env(safe-area-inset-top) + 240px) !important;
  margin-top: 0 !important;
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.apk-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000 !important;
}

/* Ensure modals appear above fixed headers */
.apk-modal-above-header {
  z-index: 1100 !important;
}

/* Ensure dialog overlays appear above fixed headers */
[data-radix-popper-content-wrapper],
[data-radix-dialog-overlay],
[data-radix-dialog-content] {
  z-index: 1100 !important;
}

/* Dialog and modal z-index fixes */
.dialog-overlay {
  z-index: 9998 !important;
}

/* Hide scrollbar for horizontal scroll */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

.dialog-content {
  z-index: 9999 !important;
}

/* Select dropdown z-index fixes for dialogs */
[data-radix-select-content] {
  z-index: 10000 !important;
}

[data-radix-popper-content-wrapper] {
  z-index: 10000 !important;
}

/* Sheet/Drawer z-index fixes to appear above fixed header */
[data-radix-dialog-overlay] {
  z-index: 10001 !important;
}

[data-radix-dialog-content] {
  z-index: 10002 !important;
}

/* Date and time input styling for better visibility */
input[type="date"]::-webkit-calendar-picker-indicator,
input[type="time"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 12px;
  cursor: pointer;
  opacity: 0.8;
  z-index: 10;
  width: 20px;
  height: 20px;
  background-size: 16px;
  background-repeat: no-repeat;
  background-position: center;
}

/* Toast notifications z-index fixes to appear above fixed headers */
.toaster,
[data-sonner-toaster] {
  z-index: 10003 !important;
}

[data-radix-toast-viewport] {
  z-index: 10003 !important;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover,
input[type="time"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

/* Ensure date/time inputs have proper spacing */
input[type="date"],
input[type="time"] {
  padding-right: 3rem !important;
  position: relative;
}

/* Fix for mobile date/time pickers */
@media (max-width: 768px) {
  input[type="date"]::-webkit-calendar-picker-indicator,
  input[type="time"]::-webkit-calendar-picker-indicator {
    right: 8px;
    width: 24px;
    height: 24px;
  }
}
