import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { 
  Banknote, 
  CreditCard, 
  Smartphone, 
  Globe,
  QrCode,
  Wallet
} from "lucide-react";

export type PaymentMethod = 'cash' | 'card' | 'upi' | 'online' | 'wallet' | 'other';

interface PaymentMethodDialogProps {
  open: boolean;
  onClose: () => void;
  onSelect: (paymentMethod: PaymentMethod) => void;
  title?: string;
}

const PaymentMethodDialog: React.FC<PaymentMethodDialogProps> = ({
  open,
  onClose,
  onSelect,
  title = "Select Payment Method"
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>('cash');

  const paymentMethods = [
    {
      id: 'cash' as PaymentMethod,
      label: 'Cash',
      icon: Banknote,
      description: 'Cash payment',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      id: 'card' as PaymentMethod,
      label: 'Card',
      icon: CreditCard,
      description: 'Credit/Debit Card',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      id: 'upi' as PaymentMethod,
      label: 'UPI',
      icon: Smartphone,
      description: 'UPI Payment',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      id: 'online' as PaymentMethod,
      label: 'Online',
      icon: Globe,
      description: 'Net Banking/Online',
      color: 'bg-orange-500 hover:bg-orange-600'
    },
    {
      id: 'wallet' as PaymentMethod,
      label: 'Wallet',
      icon: Wallet,
      description: 'Digital Wallet',
      color: 'bg-indigo-500 hover:bg-indigo-600'
    },
    {
      id: 'other' as PaymentMethod,
      label: 'Other',
      icon: QrCode,
      description: 'Other methods',
      color: 'bg-gray-500 hover:bg-gray-600'
    }
  ];

  const handleSelect = () => {
    onSelect(selectedMethod);
    onClose();
  };

  const handleMethodClick = (method: PaymentMethod) => {
    setSelectedMethod(method);
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">{title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-3">
            {paymentMethods.map((method) => {
              const IconComponent = method.icon;
              const isSelected = selectedMethod === method.id;
              
              return (
                <button
                  key={method.id}
                  onClick={() => handleMethodClick(method.id)}
                  className={`
                    p-4 rounded-lg border-2 transition-all duration-200 text-left
                    ${isSelected 
                      ? 'border-primary bg-primary/10 shadow-md' 
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }
                  `}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`
                      p-2 rounded-full text-white
                      ${isSelected ? method.color : 'bg-gray-400'}
                    `}>
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{method.label}</div>
                      <div className="text-xs text-gray-500">{method.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSelect}
              className="flex-1"
            >
              Confirm Payment Method
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentMethodDialog;
