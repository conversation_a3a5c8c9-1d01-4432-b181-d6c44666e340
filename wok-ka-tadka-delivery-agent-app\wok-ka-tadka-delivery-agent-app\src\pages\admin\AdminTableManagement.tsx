import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  Users,
  Clock,
  Plus,
  CheckCircle,
  AlertCircle,
  Coffee,
  Utensils,
  Trash2,
  MoreVertical
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { kotStorage } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";

const AdminTableManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Load tables from localStorage or use default
  const loadTables = () => {
    try {
      const stored = localStorage.getItem('restaurant_tables');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading tables:', error);
    }
    // Default tables
    return [
      { id: "1", capacity: 4, status: "available", orderNumber: null },
      { id: "2", capacity: 2, status: "available", orderNumber: null },
      { id: "3", capacity: 6, status: "available", orderNumber: null },
      { id: "4", capacity: 4, status: "available", orderNumber: null },
      { id: "5", capacity: 2, status: "available", orderNumber: null },
      { id: "6", capacity: 4, status: "available", orderNumber: null },
      { id: "7", capacity: 8, status: "available", orderNumber: null },
      { id: "8", capacity: 4, status: "available", orderNumber: null },
    ];
  };

  const [tables, setTables] = useState(loadTables);
  const [isAddTableDialogOpen, setIsAddTableDialogOpen] = useState(false);
  const [newTableCapacity, setNewTableCapacity] = useState("4");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [tableToDelete, setTableToDelete] = useState<any>(null);

  // Save tables to localStorage
  const saveTables = (updatedTables: any[]) => {
    try {
      localStorage.setItem('restaurant_tables', JSON.stringify(updatedTables));
      setTables(updatedTables);
    } catch (error) {
      console.error('Error saving tables:', error);
    }
  };

  // Update table statuses based on active KOTs
  useEffect(() => {
    const updateTableStatuses = () => {
      const updatedTables = tables.map(table => {
        const activeKOT = kotStorage.getActiveKOTForTable(table.id);
        if (activeKOT) {
          return {
            ...table,
            status: "occupied",
            orderNumber: activeKOT.kotNumber,
            orderTime: new Date(activeKOT.createdAt).toLocaleTimeString('en-IN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            }),
            guests: activeKOT.items.length // Using items count as guest count for now
          };
        }
        return { ...table, status: "available", orderNumber: null };
      });
      setTables(updatedTables);
    };

    updateTableStatuses();
    // Refresh every 30 seconds to check for updates
    const interval = setInterval(updateTableStatuses, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available": return "bg-white border-2 border-gray-300 text-gray-900";
      case "occupied": return "bg-red-500 text-white";
      case "preparing": return "bg-yellow-500 text-white";
      default: return "bg-gray-400 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "available": return "Free";
      case "occupied": return "Occupied";
      case "preparing": return "Preparing";
      default: return status;
    }
  };

  const stats = {
    total: tables.length,
    available: tables.filter(t => t.status === "available").length,
    occupied: tables.filter(t => t.status === "occupied").length,
    preparing: tables.filter(t => t.status === "preparing").length,
  };

  // Add new table
  const handleAddTable = () => {
    const capacity = parseInt(newTableCapacity);
    if (capacity < 1 || capacity > 20) {
      toast({
        title: "Invalid Capacity",
        description: "Table capacity must be between 1 and 20 seats",
        variant: "destructive",
      });
      return;
    }

    const newTableId = (Math.max(...tables.map(t => parseInt(t.id))) + 1).toString();
    const newTable = {
      id: newTableId,
      capacity: capacity,
      status: "available",
      orderNumber: null
    };

    const updatedTables = [...tables, newTable];
    saveTables(updatedTables);

    setIsAddTableDialogOpen(false);
    setNewTableCapacity("4");

    toast({
      title: "Table Added",
      description: `Table ${newTableId} with ${capacity} seats has been added successfully`,
    });
  };

  // Delete table
  const handleDeleteTable = (table: any, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent table click

    // Check if table is occupied
    if (table.status === "occupied" || table.status === "preparing") {
      toast({
        title: "Cannot Delete Table",
        description: "Cannot delete a table that is currently occupied or has orders in preparation",
        variant: "destructive",
      });
      return;
    }

    setTableToDelete(table);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTable = () => {
    if (!tableToDelete) return;

    const updatedTables = tables.filter(t => t.id !== tableToDelete.id);
    saveTables(updatedTables);

    setIsDeleteDialogOpen(false);
    setTableToDelete(null);

    toast({
      title: "Table Deleted",
      description: `Table ${tableToDelete.id} has been deleted successfully`,
    });
  };

  const handleTableClick = (table: any) => {
    if (table.status === "available") {
      // Go directly to order taking for available tables
      navigate(`/admin/take-order?table=${table.id}&dine-in=true`);
    } else if (table.status === "occupied" || table.status === "preparing") {
      // Go to table details for occupied tables
      navigate(`/admin/table-details/${table.id}`);
    }
  };

  return (
    <div className="apk-page-container bg-gray-50 min-h-screen" style={{paddingTop: '80px'}}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b apk-header-fixed" style={{zIndex: 1000}}>
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center gap-2 sm:gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/admin/dashboard")}
              className="flex items-center gap-1 sm:gap-2 shrink-0"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back</span>
            </Button>
            <Logo className="h-6 sm:h-8 shrink-0" />
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Table Management</h1>
              <p className="text-xs sm:text-sm text-gray-600 truncate">Select a table to take order or view details</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-3 sm:p-4 space-y-4 sm:space-y-6">
        {/* Add New Table Button */}
        <div className="flex justify-end">
          <Button
            onClick={() => setIsAddTableDialogOpen(true)}
            className="flex items-center gap-2 apk-button-safe"
          >
            <Plus className="h-4 w-4" />
            Add New Table
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <Card className="shadow-sm">
            <CardContent className="p-3 sm:p-4 text-center">
              <div className="text-xl sm:text-2xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Tables</div>
            </CardContent>
          </Card>
          <Card className="shadow-sm">
            <CardContent className="p-3 sm:p-4 text-center">
              <div className="text-xl sm:text-2xl font-bold text-green-600">{stats.available}</div>
              <div className="text-xs sm:text-sm text-gray-600">Available</div>
            </CardContent>
          </Card>
          <Card className="shadow-sm">
            <CardContent className="p-3 sm:p-4 text-center">
              <div className="text-xl sm:text-2xl font-bold text-red-600">{stats.occupied}</div>
              <div className="text-xs sm:text-sm text-gray-600">Occupied</div>
            </CardContent>
          </Card>
          <Card className="shadow-sm">
            <CardContent className="p-3 sm:p-4 text-center">
              <div className="text-xl sm:text-2xl font-bold text-yellow-600">{stats.preparing}</div>
              <div className="text-xs sm:text-sm text-gray-600">Preparing</div>
            </CardContent>
          </Card>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center gap-3 sm:gap-6 bg-white rounded-lg p-3 shadow-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 sm:w-4 sm:h-4 rounded border-2 border-gray-300 bg-white"></div>
            <span className="text-xs sm:text-sm text-gray-600">Free</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 sm:w-4 sm:h-4 rounded bg-red-500"></div>
            <span className="text-xs sm:text-sm text-gray-600">Occupied</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 sm:w-4 sm:h-4 rounded bg-yellow-500"></div>
            <span className="text-xs sm:text-sm text-gray-600">Preparing</span>
          </div>
        </div>

        {/* Tables Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
          {tables.map((table) => (
            <Card
              key={table.id}
              className={`
                cursor-pointer transition-all duration-200 hover:scale-105 shadow-sm border-2 relative
                ${table.status === "available" ? "border-gray-300 bg-white hover:border-gray-400" : ""}
                ${table.status === "occupied" ? "border-red-500 bg-red-500 text-white" : ""}
                ${table.status === "preparing" ? "border-yellow-500 bg-yellow-500 text-white" : ""}
              `}
              onClick={() => handleTableClick(table)}
            >
              {/* Delete Button - Only show for available tables */}
              {table.status === "available" && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-1 right-1 h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 z-10"
                  onClick={(e) => handleDeleteTable(table, e)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}

              <CardContent className="p-3 sm:p-4 text-center min-h-[120px] sm:min-h-[140px] flex flex-col justify-center">
                <div className="mb-2">
                  <h3 className="text-sm sm:text-base font-bold mb-1">Table</h3>
                  <h2 className="text-xl sm:text-2xl font-bold">{table.id}</h2>
                </div>

                <div className="flex items-center justify-center gap-1 mb-2">
                  <Users className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="text-xs sm:text-sm font-medium">{table.capacity} seats</span>
                </div>

                {table.orderNumber && (
                  <div className="bg-white/20 rounded-full px-2 py-1 mb-1">
                    <span className="text-xs font-medium">KOT</span>
                    <div className="text-xs">#{table.orderNumber}</div>
                  </div>
                )}

                {table.orderTime && (
                  <div className="bg-white/20 rounded-full px-2 py-1 mb-2">
                    <div className="text-xs">{table.orderTime}</div>
                  </div>
                )}

                <div className="text-xs sm:text-sm font-medium">
                  {getStatusText(table.status)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Add New Table Dialog */}
      <Dialog open={isAddTableDialogOpen} onOpenChange={setIsAddTableDialogOpen}>
        <DialogContent className="max-w-md" style={{zIndex: 9999}}>
          <DialogHeader>
            <DialogTitle>Add New Table</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="capacity">Table Capacity (Number of Seats)</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                max="20"
                value={newTableCapacity}
                onChange={(e) => setNewTableCapacity(e.target.value)}
                placeholder="Enter number of seats"
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Enter the number of seats for this table (1-20)
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTableDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTable}>
              Add Table
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent style={{zIndex: 10000}}>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this table?</AlertDialogTitle>
            <AlertDialogDescription>
              {tableToDelete && (
                <>
                  You are about to delete <strong>Table {tableToDelete.id}</strong> with {tableToDelete.capacity} seats.
                  This action cannot be undone.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteDialogOpen(false)}>
              No, Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTable}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Yes, Delete Table
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminTableManagement;
