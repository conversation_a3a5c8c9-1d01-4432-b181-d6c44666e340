import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  ArrowLeft,
  Plus,
  Minus,
  ShoppingCart,
  Search,
  Star,
  Clock,
  Flame,
  Leaf,
  Filter,
  X,
  Check,
  AlertCircle,
  FileText,
  CheckCircle
} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import { Logo } from "@/components/ui/logo";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { kotStorage, KOT } from "@/utils/kotStorage";
import { useToast } from "@/hooks/use-toast";

const TableOrder = () => {
  const navigate = useNavigate();
  const { tableId } = useParams();
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState("biryani");
  const [cart, setCart] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterVeg, setFilterVeg] = useState<boolean | null>(null); // null = all, true = veg, false = non-veg
  const [filterSpicy, setFilterSpicy] = useState<number | null>(null); // null = all, 0-4 = spicy level
  const [filterPopular, setFilterPopular] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    allItems: true,
    popular: false,
    bestseller: false,
    // Vegetarian categories
    biryani: false,
    riceVeg: false,
    indianPunjabiVeg: false,
    indianVegSpecialDishes: false,
    gravyChinese: false,
    tandooriBreads: false,
    bulkOrder: false,
    tandooriVeg: false,
    papadSalad: false,
    soupsVeg: false,
    vegStarters: false,
    noodlesVeg: false,
    // Non-Vegetarian categories
    biryaniNonVeg: false,
    riceNonVeg: false,
    chickenGravy: false,
    bulkOrderNonVeg: false,
    muttonGravy: false,
    tandooriNonVeg: false,
    seaFood: false,
    soupsNonVeg: false,
    chickenStarters: false,
    noodlesNonVeg: false,
    // Egg Items
    eggDishes: false
  });

  // KOT Management State
  const [existingKOT, setExistingKOT] = useState<KOT | null>(null);
  const [showKOTOptions, setShowKOTOptions] = useState(false);

  // Check for existing KOT when component mounts or tableId changes
  useEffect(() => {
    if (tableId) {
      const activeKOT = kotStorage.getActiveKOTForTable(tableId);
      setExistingKOT(activeKOT);
    }
  }, [tableId]);

  const categories = [
    { id: "biryani", name: "Biryani", icon: "🥘", count: 14 },
    { id: "tandoori-breads", name: "Tandoori Breads", icon: "🍛", count: 14 },
    { id: "bulk-order", name: "Bulk Order", icon: "🍱", count: 8 },
    { id: "mutton-gravy", name: "Mutton Gravy", icon: "🍖", count: 8 },
    { id: "tandoori-veg", name: "Tandoori Veg", icon: "🧀", count: 6 },
    { id: "tandoori-chicken", name: "Tandoori Chicken", icon: "🍗", count: 16 },
    { id: "seafood", name: "Sea Food", icon: "🐟", count: 10 },
    { id: "papad-salad", name: "Papad & Salad", icon: "🥗", count: 8 },
    { id: "soups-veg", name: "Soups Veg", icon: "🍜", count: 5 },
    { id: "soups-nonveg", name: "Soups Non-Veg", icon: "🍜", count: 6 },
    { id: "starters-veg", name: "Starters Veg", icon: "🥟", count: 15 },
    { id: "starters-nonveg", name: "Starters Non-Veg", icon: "🍗", count: 14 },
    { id: "noodles-veg", name: "Noodles Veg", icon: "🍜", count: 9 },
    { id: "noodles-nonveg", name: "Noodles Non-Veg", icon: "🍜", count: 13 },
    { id: "rice-veg", name: "Rice Veg", icon: "🍚", count: 14 },
    { id: "rice-nonveg", name: "Rice Non-Veg", icon: "🍚", count: 13 },
    { id: "indian-veg", name: "Indian Veg", icon: "🥘", count: 10 },
    { id: "chicken-gravy", name: "Chicken Gravy", icon: "🍗", count: 19 },
    { id: "egg-dishes", name: "Egg Dishes", icon: "🥚", count: 8 },
    { id: "chinese-gravy", name: "Chinese Gravy", icon: "🍛", count: 9 },
    { id: "punjabi-special", name: "Punjabi Special", icon: "🥘", count: 14 },
  ];

  const menuItems = {
    biryani: [
      { id: 1, name: "Steam Basmati Rice", price: 120, image: "/Menu_Images/Steam_Basmati_Rice.jpg", rating: 4.5, time: "15 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "rice-veg" },
      { id: 2, name: "Jeera Rice", price: 140, image: "/Menu_Images/Jeera_Rice.jpg", rating: 4.6, time: "15 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "rice-veg" },
      { id: 3, name: "Veg Pulav", price: 180, image: "/Menu_Images/Veg_Pulav.jpg", rating: 4.4, time: "20 mins", veg: true, spicy: 1, popular: false, bestseller: false, egg: false, category: "biryani-veg" },
      { id: 4, name: "Veg Biryani", price: 220, image: "/Menu_Images/Veg_Biryani.jpg", rating: 4.8, time: "25 mins", veg: true, spicy: 2, popular: true, bestseller: false, egg: false, category: "biryani-veg" },
      { id: 5, name: "Egg Biryani", price: 200, image: "/Menu_Images/Egg_Biryani.jpg", rating: 4.5, time: "25 mins", veg: false, spicy: 2, popular: false, bestseller: false, egg: true, category: "egg-dishes" },
      { id: 6, name: "Paneer Pulav", price: 240, image: "/Menu_Images/Paneer_Pulav.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 1, popular: false, bestseller: false, egg: false, category: "biryani-veg" },
      { id: 7, name: "Paneer Biryani", price: 260, image: "/Menu_Images/Paneer_Biryani.jpg", rating: 4.8, time: "25 mins", veg: true, spicy: 2, popular: false, bestseller: false, egg: false, category: "biryani-veg" },
      { id: 8, name: "Chicken Biryani", price: 280, image: "/Menu_Images/mutton_biryani.jpeg", rating: 4.8, time: "30 mins", veg: false, spicy: 2, popular: true, bestseller: false, egg: false, category: "biryani-non-veg" },
      { id: 9, name: "Chicken Dum Biryani", price: 320, image: "/Menu_Images/Chicken_Dum_Biryani.jpg", rating: 4.9, time: "35 mins", veg: false, spicy: 2, popular: true, bestseller: false, egg: false, category: "biryani-non-veg" },
      { id: 10, name: "Chicken Hyderabadi Biryani", price: 350, image: "/Menu_Images/Chicken_Hyderabadi_Biryani.jpg", rating: 4.9, time: "40 mins", veg: false, spicy: 3, popular: false, bestseller: true, egg: false, category: "biryani-non-veg" },
      { id: 11, name: "Chicken Tikka Biryani", price: 380, image: "/Menu_Images/chicken_tikka_biryani.jpg", rating: 4.8, time: "35 mins", veg: false, spicy: 3, popular: false, bestseller: false, egg: false, category: "biryani-non-veg" },
      { id: 12, name: "Mutton Biryani", price: 420, image: "/Menu_Images/Mutton_Biryani.jpg", rating: 4.9, time: "45 mins", veg: false, spicy: 3, popular: true, bestseller: false, egg: false, category: "biryani-non-veg" },
      { id: 13, name: "Mutton Dum Biryani", price: 450, image: "/Menu_Images/Mutton_Biryani.jpg", rating: 4.9, time: "50 mins", veg: false, spicy: 3, popular: false, bestseller: false, egg: false, category: "biryani-non-veg" },
      { id: 14, name: "Mutton Hyderabadi Biryani", price: 480, image: "/Menu_Images/Mutton_Hyderabadi_Biryani.jpg", rating: 5.0, time: "50 mins", veg: false, spicy: 3, popular: false, bestseller: true, egg: false, category: "biryani-non-veg" },
    ],
    "tandoori-breads": [
      { id: 15, name: "Roti", price: 25, image: "/Menu_Images/roti.jpeg", rating: 4.3, time: "5 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 16, name: "Butter Roti", price: 30, image: "/Menu_Images/Butter_Roti.jpg", rating: 4.4, time: "5 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 17, name: "Naan", price: 40, image: "/Menu_Images/Naan.jpg", rating: 4.5, time: "8 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 18, name: "Butter Naan", price: 45, image: "/Menu_Images/Butter_Naan.jpg", rating: 4.6, time: "8 mins", veg: true, spicy: 0, popular: true, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 19, name: "Garlic Naan", price: 50, image: "/Menu_Images/Garlic_Naan.jpg", rating: 4.7, time: "10 mins", veg: true, spicy: 1, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 20, name: "Khulcha", price: 35, image: "/Menu_Images/Khulcha.jpg", rating: 4.4, time: "8 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 21, name: "Butter Khulcha", price: 40, image: "/Menu_Images/Butter_Khulcha.jpg", rating: 4.5, time: "8 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 22, name: "Lacha Paratha", price: 45, image: "/Menu_Images/Lacha_Paratha.jpg", rating: 4.6, time: "10 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 23, name: "Butter Paratha", price: 50, image: "/Menu_Images/Butter_Paratha.jpg", rating: 4.6, time: "10 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 24, name: "Aloo Paratha", price: 60, image: "/Menu_Images/Aloo_Paratha.jpg", rating: 4.7, time: "12 mins", veg: true, spicy: 1, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 25, name: "Chicken Kheema Paratha", price: 80, image: "/Menu_Images/chicken_keema_paratha.jpg", rating: 4.8, time: "15 mins", veg: false, spicy: 2, popular: false, bestseller: false, egg: false, category: "tandoori-non-veg" },
      { id: 26, name: "Paneer Paratha", price: 70, image: "/Menu_Images/Paneer_Paratha.jpg", rating: 4.7, time: "12 mins", veg: true, spicy: 1, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 27, name: "Veg Kheema Paratha", price: 65, image: "/Menu_Images/Veg_Kheema_Paratha.jpg", rating: 4.6, time: "12 mins", veg: true, spicy: 1, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
      { id: 28, name: "Tandoori Roti", price: 30, image: "/Menu_Images/Tandoori_Roti.jpg", rating: 4.4, time: "8 mins", veg: true, spicy: 0, popular: false, bestseller: false, egg: false, category: "tandoori-breads" },
    ],
    "bulk-order": [
      { id: 29, name: "Paneer Pulav (Per KG)", price: 800, image: "/Menu_Images/Paneer_Pulav.jpg", rating: 4.6, time: "30 mins", veg: true, spicy: 1, popular: false },
      { id: 30, name: "Veg Biryani (Per KG)", price: 750, image: "/Menu_Images/Veg_Biryani.jpg", rating: 4.7, time: "35 mins", veg: true, spicy: 2, popular: false },
      { id: 31, name: "Chicken Biryani (Per KG)", price: 1200, image: "/Menu_Images/Chicken_Biryani.jpg", rating: 4.8, time: "40 mins", veg: false, spicy: 2, popular: false },
      { id: 32, name: "Mutton Biryani (Per KG)", price: 1800, image: "/Menu_Images/Mutton_Biryani.jpg", rating: 4.9, time: "50 mins", veg: false, spicy: 3, popular: false },
      { id: 33, name: "Veg Pulav (Per KG)", price: 650, image: "/Menu_Images/Veg_Pulav.jpg", rating: 4.4, time: "30 mins", veg: true, spicy: 1, popular: false },
      { id: 34, name: "Chicken Masala (Per KG)", price: 1000, image: "/Menu_Images/Chicken_Masala.jpg", rating: 4.7, time: "35 mins", veg: false, spicy: 2, popular: false },
      { id: 35, name: "Jeera Rice (Per KG)", price: 500, image: "/Menu_Images/Jeera_Rice.jpg", rating: 4.5, time: "25 mins", veg: true, spicy: 0, popular: false },
      { id: 36, name: "Steam Rice (Per KG)", price: 400, image: "/Menu_Images/Steam_Basmati_Rice.jpg", rating: 4.3, time: "20 mins", veg: true, spicy: 0, popular: false },
    ],
    "mutton-gravy": [
      { id: 37, name: "Mutton Masala", price: 380, image: "/Menu_Images/Mutton-Masala.jpg", rating: 4.8, time: "45 mins", veg: false, spicy: 3, popular: false },
      { id: 38, name: "Mutton Kadhai", price: 400, image: "/Menu_Images/Mutton_Kadhai.jpg", rating: 4.7, time: "40 mins", veg: false, spicy: 3, popular: false },
      { id: 39, name: "Mutton Kolhapuri", price: 420, image: "/Menu_Images/Kolhapuri-mutton.jpg", rating: 4.9, time: "45 mins", veg: false, spicy: 4, popular: false },
      { id: 40, name: "Mutton Hyderabadi", price: 450, image: "/Menu_Images/Mutton_Hyderabadi_Biryani.jpg", rating: 4.9, time: "50 mins", veg: false, spicy: 3, popular: false },
      { id: 41, name: "Mutton Handi (Half)", price: 220, image: "/Menu_Images/mutton-handi.jpeg", rating: 4.8, time: "35 mins", veg: false, spicy: 2, popular: false },
      { id: 42, name: "Mutton Handi (Full)", price: 400, image: "/Menu_Images/mutton-handi.jpeg", rating: 4.8, time: "40 mins", veg: false, spicy: 2, popular: false },
      { id: 43, name: "Mutton Do Pyaza", price: 390, image: "/Menu_Images/Mutton_Do_Pyaza.jpg", rating: 4.7, time: "40 mins", veg: false, spicy: 2, popular: false },
      { id: 44, name: "Mutton Shukha", price: 410, image: "/Menu_Images/Mutton_Shukha.jpg", rating: 4.8, time: "45 mins", veg: false, spicy: 3, popular: false },
    ],
    "tandoori-veg": [
      { id: 45, name: "Paneer Tikka", price: 220, image: "/Menu_Images/Paneer_Tikka.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 2, popular: true },
      { id: 46, name: "Paneer Pahadi Tikka", price: 240, image: "/Menu_Images/Paneer_Pahadi_Tikka.jpg", rating: 4.8, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 47, name: "Veg Seekh Kabab", price: 180, image: "/Menu_Images/Veg_Seekh_Kabab.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 48, name: "Mushroom Tikka", price: 200, image: "/Menu_Images/Mushroom_Tikka.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 49, name: "Baby Corn Tikka", price: 190, image: "/Menu_Images/baby corn tikka.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 50, name: "Chilly Milly Kabab", price: 210, image: "/Menu_Images/Chilly_Milly_Kabab.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 3, popular: false },
    ],
    "tandoori-chicken": [
      { id: 51, name: "Chicken Tikka", price: 280, image: "/Menu_Images/Chicken_Tikka.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 2, popular: true },
      { id: 52, name: "Chicken Tandoori Half", price: 200, image: "/Menu_Images/chicken tandori.jpg", rating: 4.7, time: "30 mins", veg: false, spicy: 2, popular: false },
      { id: 53, name: "Chicken Tandoori Full", price: 380, image: "/Menu_Images/chicken tandori.jpg", rating: 4.8, time: "35 mins", veg: false, spicy: 2, popular: false },
      { id: 54, name: "Chicken Pahadi Tandoori Half", price: 220, image: "/Menu_Images/Chicken_Pahadi_Tandoori.jpg", rating: 4.7, time: "30 mins", veg: false, spicy: 2, popular: false },
      { id: 55, name: "Chicken Pahadi Tandoori Full", price: 400, image: "/Menu_Images/Chicken_Pahadi_Tandoori.jpg", rating: 4.8, time: "35 mins", veg: false, spicy: 2, popular: false },
      { id: 56, name: "Chicken Lemon Tandoori Half", price: 230, image: "/Menu_Images/Chicken_Lemon_Tandoori.jpg", rating: 4.6, time: "30 mins", veg: false, spicy: 1, popular: false },
      { id: 57, name: "Chicken Lemon Tandoori Full", price: 420, image: "/Menu_Images/Chicken_Lemon_Tandoori.jpg", rating: 4.7, time: "35 mins", veg: false, spicy: 1, popular: false },
      { id: 58, name: "Chicken Pahadi Kabab", price: 300, image: "/Menu_Images/Chicken_Pahadi_Kabab.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 59, name: "Chicken Malai Kabab", price: 320, image: "/Menu_Images/chicken malai kabab.jpg", rating: 4.9, time: "25 mins", veg: false, spicy: 1, popular: false },
      { id: 60, name: "Chicken Kalimiri Kabab", price: 310, image: "/Menu_Images/Chicken_Kalimiri_Kabab.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 3, popular: false },
      { id: 61, name: "Chicken Banjara Kabab", price: 290, image: "/Menu_Images/Chicken_Banjara_Kabab.jpg", rating: 4.7, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 62, name: "Chicken Sholay Kabab", price: 330, image: "/Menu_Images/Chicken_Sholay_Kabab.jpg", rating: 4.8, time: "28 mins", veg: false, spicy: 3, popular: false },
      { id: 63, name: "Chicken Sikkh Kabab", price: 300, image: "/Menu_Images/Chicken_Sikkh_Kabab.jpg", rating: 4.7, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 64, name: "Chicken Tiranga Kabab", price: 340, image: "/Menu_Images/Chicken_Tiranga_Kabab.jpg", rating: 4.8, time: "28 mins", veg: false, spicy: 2, popular: false },
      { id: 65, name: "Chicken Lemon Kabab", price: 290, image: "/Menu_Images/Chicken_Lemon_Kabab.jpg", rating: 4.6, time: "25 mins", veg: false, spicy: 1, popular: false },
      { id: 66, name: "Chicken Rujali Kabab", price: 320, image: "/Menu_Images/Chicken_Rujali_Kabab.jpg", rating: 4.7, time: "28 mins", veg: false, spicy: 3, popular: false },
    ],
    seafood: [
      { id: 67, name: "Bangda Fry", price: 180, image: "/Menu_Images/Bangda_Fry.jpg", rating: 4.5, time: "20 mins", veg: false, spicy: 2, popular: false },
      { id: 68, name: "Bangda Masala", price: 200, image: "/Menu_Images/Bangda_Masala.jpg", rating: 4.6, time: "25 mins", veg: false, spicy: 3, popular: false },
      { id: 69, name: "Mandeli Oil Fry", price: 160, image: "/Menu_Images/Mandeli_Oil_Fry.jpg", rating: 4.4, time: "18 mins", veg: false, spicy: 2, popular: false },
      { id: 70, name: "Surmai Tawa Fry", price: 280, image: "/Menu_Images/surmai tawa fry.jpg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 71, name: "Surmai Koliwada", price: 300, image: "/Menu_Images/Surmai_Koliwada.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 3, popular: false },
      { id: 72, name: "Prawns Masala", price: 320, image: "/Menu_Images/prawns masala.jpeg", rating: 4.8, time: "30 mins", veg: false, spicy: 3, popular: false },
      { id: 73, name: "Prawns Tawa Fry", price: 300, image: "/Menu_Images/Prawns_Tawa_Fry.jpg", rating: 4.7, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 74, name: "Prawns Koliwada", price: 340, image: "/Menu_Images/Prawns_Koliwada.jpg", rating: 4.8, time: "28 mins", veg: false, spicy: 3, popular: false },
      { id: 75, name: "Surmai Gavan Curry", price: 350, image: "/Menu_Images/Surmai_Gavan_Curry.jpg", rating: 4.9, time: "35 mins", veg: false, spicy: 3, popular: false },
      { id: 76, name: "Surmai Masala", price: 320, image: "/Menu_Images/Surmai_Masala.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 3, popular: false },
    ],
    "papad-salad": [
      { id: 77, name: "Roasted Papad", price: 30, image: "/Menu_Images/Roasted_Papad.jpg", rating: 4.2, time: "5 mins", veg: true, spicy: 0, popular: false },
      { id: 78, name: "Fry Papad", price: 35, image: "/Menu_Images/Fry_Papad.jpg", rating: 4.3, time: "5 mins", veg: true, spicy: 0, popular: false },
      { id: 79, name: "Masala Papad", price: 50, image: "/Menu_Images/Masala_Papad.jpg", rating: 4.5, time: "8 mins", veg: true, spicy: 2, popular: false },
      { id: 80, name: "Green Salad", price: 60, image: "/Menu_Images/Green_Salad.jpg", rating: 4.4, time: "5 mins", veg: true, spicy: 0, popular: false },
      { id: 81, name: "Raita", price: 50, image: "/Menu_Images/Raita.jpg", rating: 4.3, time: "5 mins", veg: true, spicy: 0, popular: false },
      { id: 82, name: "Boondi Raita", price: 60, image: "/Menu_Images/Boondi_Raita.jpg", rating: 4.4, time: "5 mins", veg: true, spicy: 0, popular: false },
      { id: 83, name: "Schezwan Sauce Extra", price: 20, image: "/Menu_Images/Schezwan_Sauce.jpg", rating: 4.0, time: "2 mins", veg: true, spicy: 3, popular: false },
      { id: 84, name: "Fry Noodles Extra", price: 40, image: "/Menu_Images/Fry_Noodles.jpg", rating: 4.2, time: "5 mins", veg: true, spicy: 1, popular: false },
    ],
    "soups-veg": [
      { id: 85, name: "Manchow Soup", price: 80, image: "/Menu_Images/Manchow_Soup.jpg", rating: 4.5, time: "12 mins", veg: true, spicy: 2, popular: false },
      { id: 86, name: "Schezwan Soup", price: 90, image: "/Menu_Images/Schezwan_Soup.jpeg", rating: 4.6, time: "12 mins", veg: true, spicy: 3, popular: false },
      { id: 87, name: "Noodles Soup", price: 100, image: "/Menu_Images/Noodles_Soup.jpg", rating: 4.4, time: "15 mins", veg: true, spicy: 1, popular: false },
      { id: 88, name: "Clear Soup", price: 70, image: "/Menu_Images/Clear_Soup.jpg", rating: 4.2, time: "10 mins", veg: true, spicy: 0, popular: false },
      { id: 89, name: "Hot & Sour Soup", price: 85, image: "/Menu_Images/hot and sour soup.jpg", rating: 4.5, time: "12 mins", veg: true, spicy: 2, popular: false },
    ],
    "soups-nonveg": [
      { id: 90, name: "Chicken Manchow Soup", price: 120, image: "/Menu_Images/chicken manchow soup.jpeg", rating: 4.7, time: "15 mins", veg: false, spicy: 2, popular: false },
      { id: 91, name: "Chicken Hot & Sour Soup", price: 110, image: "/Menu_Images/Chicken_Hot_&_Sour_Soup.jpg", rating: 4.6, time: "15 mins", veg: false, spicy: 2, popular: false },
      { id: 92, name: "Chicken Lung Fung Soup", price: 130, image: "/Menu_Images/Chicken_Lung_Fung_Soup.jpg", rating: 4.8, time: "18 mins", veg: false, spicy: 1, popular: false },
      { id: 93, name: "Chicken Schezwan Soup", price: 125, image: "/Menu_Images/chicken schezhwan soup.jpg", rating: 4.7, time: "15 mins", veg: false, spicy: 3, popular: false },
      { id: 94, name: "Chicken Noodles Soup", price: 140, image: "/Menu_Images/Chicken_Noodles_Soup.jpg", rating: 4.6, time: "18 mins", veg: false, spicy: 1, popular: false },
      { id: 95, name: "Chicken Clear Soup", price: 100, image: "/Menu_Images/chicken-clear-soup.jpg", rating: 4.4, time: "12 mins", veg: false, spicy: 0, popular: false },
    ],
    "starters-veg": [
      { id: 96, name: "Veg Manchurian / Chilly", price: 160, image: "/Menu_Images/Veg_Manchurian.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 97, name: "Veg Chinese Bhel", price: 140, image: "/Menu_Images/Veg_Chinese_Bhel.jpg", rating: 4.4, time: "15 mins", veg: true, spicy: 2, popular: false },
      { id: 98, name: "Veg 65", price: 170, image: "/Menu_Images/Veg_65.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 3, popular: false },
      { id: 99, name: "Mushroom Chilly / Manchurian", price: 180, image: "/Menu_Images/Mushroom_Chilly.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 100, name: "Paneer Chilly / Manchurian", price: 200, image: "/Menu_Images/Paneer_Chilly.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 101, name: "Paneer Crispy", price: 220, image: "/Menu_Images/Paneer_Crispy.jpeg", rating: 4.8, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 102, name: "Paneer 65", price: 210, image: "/Menu_Images/Paneer_65.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 3, popular: false },
      { id: 103, name: "Paneer Singapore", price: 230, image: "/Menu_Images/Paneer_Singapore.jpg", rating: 4.8, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 104, name: "Veg Crispy", price: 150, image: "/Menu_Images/Veg_Crispy.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 105, name: "Crispy Chilly Potato", price: 140, image: "/Menu_Images/Crispy_Chilly_Potato.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 3, popular: false },
      { id: 106, name: "Honey Chilly Potato", price: 160, image: "/Menu_Images/Honey_Chilly_Potato.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 107, name: "Paneer Shanghai Wok", price: 240, image: "/Menu_Images/Paneer_Shanghai_Wok.jpg", rating: 4.8, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 108, name: "Paneer Schezwan Wok", price: 250, image: "/Menu_Images/Paneer_Schezwan_Wok.jpg", rating: 4.8, time: "22 mins", veg: true, spicy: 3, popular: false },
      { id: 109, name: "Paneer Chilly Basil Wok", price: 260, image: "/Menu_Images/Paneer_Chilly_Basil_Wok.jpg", rating: 4.9, time: "25 mins", veg: true, spicy: 2, popular: false },
      { id: 110, name: "Paneer Honey Chilly Wok", price: 270, image: "/Menu_Images/Paneer_Honey_Chilly_Wok.jpg", rating: 4.8, time: "25 mins", veg: true, spicy: 2, popular: false },
      { id: 111, name: "Paneer Kum Pav Wok", price: 250, image: "/Menu_Images/paneer kum pav.jpg", rating: 4.7, time: "22 mins", veg: true, spicy: 2, popular: false },
    ],
    "starters-nonveg": [
      { id: 112, name: "Chicken Chinese Bhel", price: 180, image: "/Menu_Images/Chicken_Chinese_Bhel.jpg", rating: 4.6, time: "18 mins", veg: false, spicy: 2, popular: false },
      { id: 113, name: "Chicken Chilly / Manchurian", price: 220, image: "/Menu_Images/Chicken_Chilly.jpg", rating: 4.7, time: "20 mins", veg: false, spicy: 2, popular: false },
      { id: 114, name: "Chicken Schezwan", price: 240, image: "/Menu_Images/Chicken_Schezwan.jpg", rating: 4.8, time: "22 mins", veg: false, spicy: 3, popular: false },
      { id: 115, name: "Chicken Chilly Garlic Wok", price: 260, image: "/Menu_Images/Chicken_Chilly_Garlic_Wok.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 3, popular: false },
      { id: 116, name: "Chicken Kum Pav Wok", price: 250, image: "/Menu_Images/Chicken_Kum_Pav_Wok.jpg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 117, name: "Chicken Crispy", price: 230, image: "/Menu_Images/Chicken_Crispy.jpg", rating: 4.7, time: "20 mins", veg: false, spicy: 2, popular: false },
      { id: 118, name: "Chicken 65", price: 240, image: "/Menu_Images/Chicken_65.jpg", rating: 4.8, time: "22 mins", veg: false, spicy: 3, popular: false },
      { id: 119, name: "Chicken Lamba", price: 250, image: "/Menu_Images/Chicken_Lamba.jpg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 120, name: "Chicken Oyster Sauce", price: 270, image: "/Menu_Images/Chicken_Oyster_Sauce.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 1, popular: false },
      { id: 121, name: "Chicken Black Pepper Wok", price: 280, image: "/Menu_Images/Chicken_Black_Pepper_Wok.jpg", rating: 4.9, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 122, name: "Chicken Lollipop (8 pcs)", price: 200, image: "/Menu_Images/Chicken_Lollipop.jpg", rating: 4.8, time: "20 mins", veg: false, spicy: 2, popular: false },
      { id: 123, name: "Chicken Schezwan Lollipop", price: 220, image: "/Menu_Images/Chicken_Schezwan_Lollipop.jpg", rating: 4.8, time: "22 mins", veg: false, spicy: 3, popular: false },
      { id: 124, name: "Chicken Honey Chilly", price: 260, image: "/Menu_Images/Chicken_Honey_Chilly.jpg", rating: 4.8, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 125, name: "Chicken Chilly Basil Wok", price: 290, image: "/Menu_Images/Chicken_Chilly_Basil_Wok.jpg", rating: 4.9, time: "25 mins", veg: false, spicy: 2, popular: false },
    ],
    "noodles-veg": [
      { id: 126, name: "Veg Hakka Noodles", price: 140, image: "/Menu_Images/Veg_Hakka_Noodles.jpg", rating: 4.5, time: "15 mins", veg: true, spicy: 1, popular: true },
      { id: 127, name: "Veg Schezwan Noodles", price: 160, image: "/Menu_Images/Veg_Schezwan_Noodles.jpg", rating: 4.6, time: "15 mins", veg: true, spicy: 3, popular: false },
      { id: 128, name: "Veg Singapore Noodles", price: 170, image: "/Menu_Images/Veg_Singapore_Noodles.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 129, name: "Veg Hong Kong Noodles", price: 180, image: "/Menu_Images/Veg_Hong_Kong_Noodles.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 130, name: "Veg Mushroom Noodles", price: 170, image: "/Menu_Images/Veg_Mushroom_Noodles.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 1, popular: false },
      { id: 131, name: "Veg Manchurian Noodles", price: 180, image: "/Menu_Images/Veg_Manchurian_Noodles.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 132, name: "Veg Sherpa Noodles", price: 190, image: "/Menu_Images/Veg_Sherpa_Noodles.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 133, name: "Veg Triple Schezwan Noodles", price: 200, image: "/Menu_Images/Veg_Triple_Schezwan_Noodles.jpg", rating: 4.8, time: "22 mins", veg: true, spicy: 4, popular: false },
      { id: 134, name: "Veg Chilly Garlic Noodles", price: 170, image: "/Menu_Images/Veg_Chilly_Garlic_Noodles.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 3, popular: false },
    ],
    "noodles-nonveg": [
      { id: 135, name: "Egg Hakka Noodles", price: 160, image: "/Menu_Images/Egg_Hakka_Noodles.jpg", rating: 4.5, time: "15 mins", veg: false, spicy: 1, popular: false },
      { id: 136, name: "Egg Schezwan Noodles", price: 180, image: "/Menu_Images/Egg_Schezwan_Noodles.jpg", rating: 4.6, time: "15 mins", veg: false, spicy: 3, popular: false },
      { id: 137, name: "Chicken Hakka Noodles", price: 200, image: "/Menu_Images/Chicken_Hakka_Noodles.jpg", rating: 4.7, time: "18 mins", veg: false, spicy: 1, popular: false },
      { id: 138, name: "Chicken Schezwan Noodles", price: 220, image: "/Menu_Images/Chicken_Schezwan_Noodles.jpg", rating: 4.8, time: "18 mins", veg: false, spicy: 3, popular: false },
      { id: 139, name: "Chicken Singapore Noodles", price: 230, image: "/Menu_Images/Chicken_Singapore_Noodles.jpg", rating: 4.7, time: "20 mins", veg: false, spicy: 2, popular: false },
      { id: 140, name: "Chicken Hong Kong Noodles", price: 240, image: "/Menu_Images/Chicken_Hong_Kong_Noodles.jpg", rating: 4.8, time: "20 mins", veg: false, spicy: 2, popular: false },
      { id: 141, name: "Chicken Mushroom Noodles", price: 230, image: "/Menu_Images/Chicken_Mushroom_Noodles.jpg", rating: 4.7, time: "20 mins", veg: false, spicy: 1, popular: false },
      { id: 142, name: "Chicken Triple Schezwan Noodles", price: 260, image: "/Menu_Images/Chicken_Triple_Schezwan_Noodles.jpg", rating: 4.9, time: "25 mins", veg: false, spicy: 4, popular: false },
      { id: 143, name: "Chicken Sherpa Noodles", price: 250, image: "/Menu_Images/Chicken_Sherpa_Noodles.jpg", rating: 4.8, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 144, name: "Chicken Thousand Noodles", price: 270, image: "/Menu_Images/Chicken_Thousand_Noodles.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 145, name: "Chicken Chilly Basil Noodles", price: 280, image: "/Menu_Images/Chicken_Chilly_Basil_Noodles.jpg", rating: 4.9, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 146, name: "Chicken Manchurian Noodles", price: 240, image: "/Menu_Images/Chicken_Manchurian_Noodles.jpeg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 147, name: "Chicken Chilly Garlic Noodles", price: 230, image: "/Menu_Images/Chicken_Chilly_Garlic_Noodles.jpg", rating: 4.7, time: "20 mins", veg: false, spicy: 3, popular: false },
    ],
    "rice-veg": [
      { id: 148, name: "Veg Fried Rice", price: 120, image: "/Menu_Images/Veg_Fried_Rice.jpg", rating: 4.4, time: "15 mins", veg: true, spicy: 1, popular: false },
      { id: 149, name: "Veg Schezwan Rice", price: 140, image: "/Menu_Images/Veg_Schezwan_Rice.jpg", rating: 4.5, time: "15 mins", veg: true, spicy: 3, popular: false },
      { id: 150, name: "Veg Singapore Rice", price: 150, image: "/Menu_Images/Veg_Singapore_Rice.jpg", rating: 4.4, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 151, name: "Veg Hong Kong Rice", price: 160, image: "/Menu_Images/Veg_Hong_Kong_Rice.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 152, name: "Veg Schezwan Combination Rice", price: 170, image: "/Menu_Images/Veg_Schezwan_Combination_Rice.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 3, popular: false },
      { id: 153, name: "Veg Manchurian Rice", price: 160, image: "/Menu_Images/Veg_Manchurian_Rice.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 154, name: "Veg Triple Schezwan Rice", price: 180, image: "/Menu_Images/Veg_Triple_Schezwan_Rice.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 4, popular: false },
      { id: 155, name: "Paneer Fried Rice", price: 180, image: "/Menu_Images/Paneer_Fried_Rice.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 1, popular: false },
      { id: 156, name: "Paneer Schezwan Rice", price: 200, image: "/Menu_Images/Paneer_Schezwan_Rice.jpg", rating: 4.7, time: "18 mins", veg: true, spicy: 3, popular: false },
      { id: 157, name: "Veg Burnt Garlic Rice", price: 150, image: "/Menu_Images/Veg_Burnt_Garlic_Rice.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 1, popular: false },
      { id: 158, name: "Veg Ginger Garlic Rice", price: 140, image: "/Menu_Images/Veg_Ginger_Garlic_Rice.jpg", rating: 4.4, time: "15 mins", veg: true, spicy: 1, popular: false },
      { id: 159, name: "Veg Sherpa Rice", price: 170, image: "/Menu_Images/Veg_Sherpa_Rice.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 160, name: "Veg Chilly Garlic Rice", price: 150, image: "/Menu_Images/Veg_Chilly_Garlic_Rice.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 3, popular: false },
      { id: 161, name: "Veg Chilly Basil Rice", price: 160, image: "/Menu_Images/Veg_Chilly_Basil_Rice.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 2, popular: false },
    ],
    "rice-nonveg": [
      { id: 162, name: "Chicken Schezwan Rice", price: 200, image: "/Menu_Images/Chicken_Schezwan_Rice.jpg", rating: 4.7, time: "20 mins", veg: false, spicy: 3, popular: false },
      { id: 163, name: "Chicken Singapore Rice", price: 210, image: "/Menu_Images/Chicken_Singapore_Rice.jpeg", rating: 4.6, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 164, name: "Chicken Hong Kong Rice", price: 220, image: "/Menu_Images/Chicken_Hong_Kong_Rice.jpg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 165, name: "Chicken Schezwan Combination Rice", price: 240, image: "/Menu_Images/Chicken_Schezwan_Combination_Rice.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 3, popular: false },
      { id: 166, name: "Chicken Burnt Garlic Rice", price: 210, image: "/Menu_Images/Chicken_Burnt_Garlic_Rice.jpg", rating: 4.6, time: "20 mins", veg: false, spicy: 1, popular: false },
      { id: 167, name: "Chicken Chilly Garlic Rice", price: 200, image: "/Menu_Images/Chicken_Chilly_Garlic_Rice.jpg", rating: 4.6, time: "20 mins", veg: false, spicy: 3, popular: false },
      { id: 168, name: "Chicken Manchurian Rice", price: 220, image: "/Menu_Images/Chicken_Manchurian_Rice.jpg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 169, name: "Chicken Triple Schezwan Rice", price: 250, image: "/Menu_Images/Chicken_Triple_Schezwan_Rice.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 4, popular: false },
      { id: 170, name: "Chicken Sherpa Rice", price: 230, image: "/Menu_Images/Chicken_Sherpa_Rice.jpg", rating: 4.7, time: "22 mins", veg: false, spicy: 2, popular: false },
      { id: 171, name: "Chicken Thousand Rice", price: 250, image: "/Menu_Images/Chicken_Thousand_Rice.jpg", rating: 4.8, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 172, name: "Chicken Packing Rice", price: 190, image: "/Menu_Images/Chicken_Packing_Rice.jpg", rating: 4.5, time: "18 mins", veg: false, spicy: 1, popular: false },
      { id: 173, name: "Chicken Ginger Garlic Rice", price: 200, image: "/Menu_Images/Chicken_Ginger_Garlic_Rice.jpg", rating: 4.6, time: "20 mins", veg: false, spicy: 1, popular: false },
      { id: 174, name: "Chicken Chilly Basil Rice", price: 240, image: "/Menu_Images/Chicken_Chilly_Basil_Rice.jpg", rating: 4.8, time: "22 mins", veg: false, spicy: 2, popular: false },
    ],
    "indian-veg": [
      { id: 175, name: "Dal Fry", price: 120, image: "/Menu_Images/Dal_Fry.jpg", rating: 4.5, time: "15 mins", veg: true, spicy: 1, popular: false },
      { id: 176, name: "Dal Tadka", price: 130, image: "/Menu_Images/Dal_Tadka.jpg", rating: 4.6, time: "15 mins", veg: true, spicy: 1, popular: false },
      { id: 177, name: "Dal Palak", price: 140, image: "/Menu_Images/Dal_Palak.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 1, popular: false },
      { id: 178, name: "Dal Khichadi", price: 100, image: "/Menu_Images/Dal_Khichadi.jpg", rating: 4.3, time: "20 mins", veg: true, spicy: 0, popular: false },
      { id: 179, name: "Palak Khichadi", price: 110, image: "/Menu_Images/Palak_Khichadi.jpg", rating: 4.4, time: "20 mins", veg: true, spicy: 0, popular: false },
      { id: 180, name: "Dal Khichadi Tadka", price: 120, image: "/Menu_Images/Dal_Khichadi_Tadka.jpg", rating: 4.4, time: "22 mins", veg: true, spicy: 1, popular: false },
      { id: 181, name: "Palak Khichadi Tadka", price: 130, image: "/Menu_Images/Palak_Khichadi_Tadka.jpg", rating: 4.5, time: "22 mins", veg: true, spicy: 1, popular: false },
      { id: 182, name: "Mix Veg", price: 160, image: "/Menu_Images/Mix_Veg.jpg", rating: 4.5, time: "20 mins", veg: true, spicy: 1, popular: false },
      { id: 183, name: "Veg Kadhai", price: 170, image: "/Menu_Images/Veg_Kadhai.jpg", rating: 4.6, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 184, name: "Veg Kolhapuri", price: 180, image: "/Menu_Images/Veg_Kolhapuri.jpg", rating: 4.7, time: "25 mins", veg: true, spicy: 3, popular: false },
    ],
    "chicken-gravy": [
      { id: 185, name: "Chicken Masala", price: 240, image: "/Menu_Images/Chicken_Masala.jpg", rating: 4.7, time: "25 mins", veg: false, spicy: 2, popular: true },
      { id: 186, name: "Chicken Curry", price: 220, image: "/Menu_Images/Chicken_Curry.jpg", rating: 4.6, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 187, name: "Chicken Kadhai", price: 260, image: "/Menu_Images/Chicken_Kadhai.jpg", rating: 4.7, time: "28 mins", veg: false, spicy: 2, popular: false },
      { id: 188, name: "Chicken Kolhapuri", price: 280, image: "/Menu_Images/Chicken_Kolhapuri.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 3, popular: false },
      { id: 189, name: "Chicken Tawa Masala", price: 270, image: "/Menu_Images/Chicken_Tawa_Masala.jpg", rating: 4.7, time: "28 mins", veg: false, spicy: 2, popular: false },
      { id: 190, name: "Chicken Gavti Masala", price: 290, image: "/Menu_Images/Chicken_Gavti_Masala.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 3, popular: false },
      { id: 191, name: "Chicken Tikka Masala", price: 320, image: "/Menu_Images/Chicken_Tikka_Masala.jpg", rating: 4.9, time: "30 mins", veg: false, spicy: 2, popular: false, bestseller: true },
      { id: 192, name: "Chicken Maratha", price: 300, image: "/Menu_Images/Chicken_Maratha.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 3, popular: false },
      { id: 193, name: "Chicken Lasuni Masala", price: 310, image: "/Menu_Images/Chicken_Lasuni_Masala.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 2, popular: false },
      { id: 194, name: "Chicken Lapeta", price: 290, image: "/Menu_Images/Chicken_Lapeta.jpg", rating: 4.7, time: "28 mins", veg: false, spicy: 2, popular: false },
      { id: 195, name: "Butter Chicken", price: 320, image: "/Menu_Images/Butter_Chicken.jpg", rating: 4.9, time: "30 mins", veg: false, spicy: 1, popular: false },
      { id: 196, name: "Chicken Malwani Masala", price: 330, image: "/Menu_Images/Chicken_Malwani_Masala.jpg", rating: 4.9, time: "32 mins", veg: false, spicy: 3, popular: false },
      { id: 197, name: "Chicken Tikka Lemon Masala", price: 340, image: "/Menu_Images/Chicken_Tikka_Lemon_Masala.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 2, popular: false },
      { id: 198, name: "Chicken Hyderabadi Masala", price: 350, image: "/Menu_Images/Chicken_Hyderabadi_Masala.jpg", rating: 4.9, time: "35 mins", veg: false, spicy: 3, popular: false },
      { id: 199, name: "Chicken Mughlai", price: 360, image: "/Menu_Images/Chicken_Mughlai.jpg", rating: 4.9, time: "35 mins", veg: false, spicy: 2, popular: false },
      { id: 200, name: "Chicken Pahadi Masala", price: 330, image: "/Menu_Images/Chicken_Pahadi_Masala.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 2, popular: false },
      { id: 201, name: "Chicken Handi (Half)", price: 180, image: "/Menu_Images/Chicken_Handi.jpg", rating: 4.7, time: "25 mins", veg: false, spicy: 2, popular: false },
      { id: 202, name: "Chicken Handi (Full)", price: 320, image: "/Menu_Images/Chicken_Handi.jpg", rating: 4.8, time: "30 mins", veg: false, spicy: 2, popular: false },
      { id: 203, name: "Chicken Do Pyaza", price: 300, image: "/Menu_Images/Chicken_Do_Pyaza.jpeg", rating: 4.7, time: "28 mins", veg: false, spicy: 2, popular: false },
    ],
    "egg-dishes": [
      { id: 204, name: "Boiled Egg", price: 40, image: "/Menu_Images/Boiled_Egg.jpeg", rating: 4.2, time: "8 mins", veg: false, spicy: 0, popular: false },
      { id: 205, name: "Egg Omelette", price: 60, image: "/Menu_Images/egg omelette.jpeg", rating: 4.4, time: "10 mins", veg: false, spicy: 1, popular: false },
      { id: 206, name: "Egg Bhurji", price: 80, image: "/Menu_Images/Egg_Bhurji.jpg", rating: 4.5, time: "12 mins", veg: false, spicy: 2, popular: false },
      { id: 207, name: "Egg Masala", price: 100, image: "/Menu_Images/Egg_Masala.jpg", rating: 4.6, time: "15 mins", veg: false, spicy: 2, popular: false },
      { id: 208, name: "Egg Curry", price: 120, image: "/Menu_Images/Egg_Curry.jpg", rating: 4.6, time: "18 mins", veg: false, spicy: 2, popular: false },
      { id: 209, name: "Anda Ghotala", price: 90, image: "/Menu_Images/Anda_Ghotala.jpg", rating: 4.5, time: "12 mins", veg: false, spicy: 2, popular: false },
      { id: 210, name: "Egg Fried Rice", price: 140, image: "/Menu_Images/Egg_Fried_Rice.jpg", rating: 4.5, time: "15 mins", veg: false, spicy: 1, popular: false },
      { id: 211, name: "Egg Schezwan Rice", price: 160, image: "/Menu_Images/Egg_Schezwan_Rice.jpg", rating: 4.6, time: "15 mins", veg: false, spicy: 3, popular: false },
    ],
    "chinese-gravy": [
      { id: 212, name: "Manchurian Gravy / Chilly", price: 140, image: "/Menu_Images/Manchurian_Gravy.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 213, name: "Schezwan Gravy", price: 160, image: "/Menu_Images/Schezwan_Gravy.jpg", rating: 4.6, time: "18 mins", veg: true, spicy: 3, popular: false },
      { id: 214, name: "Chilly Gravy", price: 150, image: "/Menu_Images/Chilly_Gravy.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 215, name: "Kum Pav Gravy", price: 170, image: "/Menu_Images/Kum_Pav_Gravy.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 216, name: "Hot Garlic Gravy", price: 160, image: "/Menu_Images/Hot_Garlic_Gravy.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 3, popular: false },
      { id: 217, name: "Oyster Gravy", price: 180, image: "/Menu_Images/Oyster_Gravy.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 1, popular: false },
      { id: 218, name: "Paneer Schezwan Gravy", price: 200, image: "/Menu_Images/Paneer_Schezwan_Gravy.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 3, popular: false },
      { id: 219, name: "Paneer Manchurian Gravy", price: 190, image: "/Menu_Images/Paneer_Manchurian_Gravy.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 220, name: "Paneer Chilly Gravy", price: 180, image: "/Menu_Images/Paneer_Chilly_Gravy.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 2, popular: false },
    ],
    "punjabi-special": [
      { id: 221, name: "Veg Handi", price: 180, image: "/Menu_Images/Veg_Handi.jpg", rating: 4.6, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 222, name: "Paneer Masala", price: 200, image: "/Menu_Images/Paneer_Masala.jpg", rating: 4.7, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 223, name: "Paneer Tikka Masala", price: 240, image: "/Menu_Images/Paneer_Tikka_Masala.jpg", rating: 4.8, time: "25 mins", veg: true, spicy: 2, popular: false, bestseller: true },
      { id: 224, name: "Paneer Tawa", price: 220, image: "/Menu_Images/Paneer_Tawa.jpg", rating: 4.7, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 225, name: "Paneer Kadhai", price: 230, image: "/Menu_Images/Paneer_Kadhai.jpg", rating: 4.7, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 226, name: "Paneer Butter Masala", price: 250, image: "/Menu_Images/Paneer_Butter_Masala.jpg", rating: 4.8, time: "25 mins", veg: true, spicy: 1, popular: true },
      { id: 227, name: "Paneer Mutter", price: 210, image: "/Menu_Images/Paneer_Mutter.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 1, popular: false },
      { id: 228, name: "Palak Paneer", price: 220, image: "/Menu_Images/Palak_Paneer.jpg", rating: 4.7, time: "22 mins", veg: true, spicy: 1, popular: false },
      { id: 229, name: "Mushroom Masala", price: 190, image: "/Menu_Images/Mushroom_Masala.jpeg", rating: 4.6, time: "20 mins", veg: true, spicy: 2, popular: false },
      { id: 230, name: "Mushroom Tikka Masala", price: 210, image: "/Menu_Images/Mushroom_Tikka_Masala.jpeg", rating: 4.7, time: "22 mins", veg: true, spicy: 2, popular: false },
      { id: 231, name: "Lasuni Palak", price: 160, image: "/Menu_Images/Lasuni_Palak.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
      { id: 232, name: "Veg Maratha", price: 170, image: "/Menu_Images/Veg_Maratha.jpg", rating: 4.6, time: "20 mins", veg: true, spicy: 3, popular: false },
      { id: 233, name: "Sev Bhaji", price: 140, image: "/Menu_Images/Sev_Bhaji.jpg", rating: 4.4, time: "15 mins", veg: true, spicy: 2, popular: false },
      { id: 234, name: "Masoor Fry Masala", price: 130, image: "/Menu_Images/Masoor_Fry_Masala.jpg", rating: 4.5, time: "18 mins", veg: true, spicy: 2, popular: false },
    ],
  };

  // Get all items from all categories and add missing properties
  const allItems = Object.entries(menuItems).flatMap(([categoryKey, items]) =>
    items.map(item => ({
      ...item,
      // Add missing properties if they don't exist
      bestseller: item.bestseller ?? (item.rating >= 4.8),
      egg: item.egg ?? (item.name.toLowerCase().includes('egg') && !item.veg),
      category: item.category ?? getCategoryFromKey(categoryKey, item)
    }))
  );

  // Helper function to determine category based on menu key and item properties
  function getCategoryFromKey(menuKey: string, item: any): string {
    switch (menuKey) {
      case 'biryani':
        if (item.egg) return 'egg-dishes';
        if (item.veg) return 'biryani-veg';
        return 'biryani-non-veg';
      case 'tandoori-breads':
        if (!item.veg) return 'tandoori-non-veg';
        return 'tandoori-breads';
      case 'bulk-order':
        if (item.veg) return 'bulk-order-veg';
        return 'bulk-order-non-veg';
      case 'mutton-gravy':
        return 'mutton-gravy';
      case 'tandoori-veg':
        return 'tandoori-veg';
      case 'tandoori-chicken':
        return 'tandoori-non-veg';
      case 'sea-food':
        return 'sea-food';
      case 'papad-salad':
        return 'papad-salad';
      case 'soups-veg':
        return 'soups-veg';
      case 'soups-nonveg':
        return 'soups-non-veg';
      case 'starters-veg':
        return 'veg-starters';
      case 'starters-nonveg':
        return 'chicken-starters';
      case 'noodles-veg':
        return 'noodles-veg';
      case 'noodles-nonveg':
        if (item.egg) return 'egg-dishes';
        return 'noodles-non-veg';
      case 'rice-veg':
        return 'rice-veg';
      case 'rice-nonveg':
        if (item.egg) return 'egg-dishes';
        return 'rice-non-veg';
      case 'indian-veg':
        return 'indian-punjabi-veg';
      case 'chicken-gravy':
        return 'chicken-gravy';
      case 'egg-dishes':
        return 'egg-dishes';
      case 'chinese-gravy':
        return 'gravy-chinese';
      case 'punjabi-special':
        return 'indian-veg-special';
      default:
        return menuKey;
    }
  }

  const filteredItems = allItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply filter logic based on selected filters
    if (selectedFilters.allItems) return matchesSearch;

    let matchesFilter = false;

    // Special categories
    if (selectedFilters.popular && item.popular) matchesFilter = true;
    if (selectedFilters.bestseller && (item.bestseller || item.rating >= 4.8)) matchesFilter = true;

    // Specific category filters - each filter should match exact category
    if (selectedFilters.biryani && item.category === 'biryani-veg') matchesFilter = true;
    if (selectedFilters.riceVeg && item.category === 'rice-veg') matchesFilter = true;
    if (selectedFilters.indianPunjabiVeg && item.category === 'indian-punjabi-veg') matchesFilter = true;
    if (selectedFilters.indianVegSpecialDishes && item.category === 'indian-veg-special') matchesFilter = true;
    if (selectedFilters.gravyChinese && item.category === 'gravy-chinese') matchesFilter = true;
    if (selectedFilters.tandooriBreads && item.category === 'tandoori-breads') matchesFilter = true;
    if (selectedFilters.bulkOrder && item.category === 'bulk-order-veg') matchesFilter = true;
    if (selectedFilters.tandooriVeg && item.category === 'tandoori-veg') matchesFilter = true;
    if (selectedFilters.papadSalad && item.category === 'papad-salad') matchesFilter = true;
    if (selectedFilters.soupsVeg && item.category === 'soups-veg') matchesFilter = true;
    if (selectedFilters.vegStarters && item.category === 'veg-starters') matchesFilter = true;
    if (selectedFilters.noodlesVeg && item.category === 'noodles-veg') matchesFilter = true;

    // Non-vegetarian categories
    if (selectedFilters.biryaniNonVeg && item.category === 'biryani-non-veg') matchesFilter = true;
    if (selectedFilters.riceNonVeg && item.category === 'rice-non-veg') matchesFilter = true;
    if (selectedFilters.chickenGravy && item.category === 'chicken-gravy') matchesFilter = true;
    if (selectedFilters.bulkOrderNonVeg && item.category === 'bulk-order-non-veg') matchesFilter = true;
    if (selectedFilters.muttonGravy && item.category === 'mutton-gravy') matchesFilter = true;
    if (selectedFilters.tandooriNonVeg && item.category === 'tandoori-non-veg') matchesFilter = true;
    if (selectedFilters.seaFood && item.category === 'sea-food') matchesFilter = true;
    if (selectedFilters.soupsNonVeg && item.category === 'soups-non-veg') matchesFilter = true;
    if (selectedFilters.chickenStarters && item.category === 'chicken-starters') matchesFilter = true;
    if (selectedFilters.noodlesNonVeg && item.category === 'noodles-non-veg') matchesFilter = true;

    // Egg items
    if (selectedFilters.eggDishes && item.category === 'egg-dishes') matchesFilter = true;

    return matchesSearch && matchesFilter;
  });

  const addToCart = (item: any) => {
    const existingItem = cart.find(cartItem => cartItem.id === item.id);
    if (existingItem) {
      setCart(cart.map(cartItem => 
        cartItem.id === item.id 
          ? { ...cartItem, quantity: cartItem.quantity + 1 }
          : cartItem
      ));
    } else {
      setCart([...cart, { ...item, quantity: 1 }]);
    }
  };

  const removeFromCart = (itemId: number) => {
    const existingItem = cart.find(cartItem => cartItem.id === itemId);
    if (existingItem && existingItem.quantity > 1) {
      setCart(cart.map(cartItem => 
        cartItem.id === itemId 
          ? { ...cartItem, quantity: cartItem.quantity - 1 }
          : cartItem
      ));
    } else {
      setCart(cart.filter(cartItem => cartItem.id !== itemId));
    }
  };

  const getCartItemQuantity = (itemId: number) => {
    const item = cart.find(cartItem => cartItem.id === itemId);
    return item ? item.quantity : 0;
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const getSpicyIndicator = (level: number) => {
    if (level === 0) return null;
    return (
      <div className="flex">
        {[...Array(level)].map((_, i) => (
          <Flame key={i} className="h-3 w-3 text-red-500" />
        ))}
      </div>
    );
  };

  return (
    <div className="apk-page-container bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-3 sm:p-4 apk-header-fixed">
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <div className="flex items-center gap-2 sm:gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20 h-8 w-8 sm:h-10 sm:w-10 back-button-highlight tap-target"
              onClick={() => navigate("/delivery/tables")}
            >
              <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
            <div>
              <h1 className="text-base sm:text-lg font-semibold">Table {tableId}</h1>
              <p className="text-white/80 text-xs sm:text-sm">Take Order</p>
            </div>
          </div>
          <Logo size="sm" variant="white" />
        </div>

        {/* Search */}
        <div className="relative mb-3 sm:mb-4">
          <Search className="absolute left-3 top-2.5 sm:top-3 h-4 w-4 text-white/70" />
          <Input
            placeholder="Search menu items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/70 h-9 sm:h-10 text-sm sm:text-base"
          />
        </div>

        {/* Filter Button */}
        <div className="flex justify-between items-center mb-3 sm:mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFilterModal(true)}
            className="text-white hover:bg-white/20 flex items-center gap-1 sm:gap-2 h-8 sm:h-9 px-2 sm:px-3 text-xs sm:text-sm"
          >
            <Filter className="h-3 w-3 sm:h-4 sm:w-4" />
            Filter Categories
          </Button>
          {Object.values(selectedFilters).some(v => v && selectedFilters.allItems !== v) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedFilters({
                allItems: true,
                popular: false,
                bestseller: false,
                vegetarian: false,
                biryani: false,
                riceVeg: false,
                indianPunjabiVeg: false,
                indianVegSpecial: false,
                gravyChinese: false,
                tandooriBreads: false,
                bulkOrder: false,
                tandooriVeg: false
              })}
              className="text-white hover:bg-white/20 text-xs h-8 sm:h-9 px-2 sm:px-3"
            >
              Clear All
            </Button>
          )}
        </div>


      </div>

      {/* Menu Items */}
      <div className="p-3 sm:p-4 apk-content-with-medium-header apk-content-with-footer">
        <div className="grid gap-3 sm:gap-4">
          {filteredItems.map((item) => (
            <Card key={item.id} className="shadow-card border-0 bg-white overflow-hidden">
              <CardContent className="p-3 sm:p-4">
                <div className="flex gap-3 sm:gap-4">
                  {/* Item Image */}
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center flex-shrink-0 overflow-hidden">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover rounded-xl"
                      onError={(e) => {
                        // Fallback to a default food icon if image fails to load
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.parentElement!.innerHTML = '<div class="text-2xl sm:text-3xl">🍽️</div>';
                      }}
                    />
                  </div>

                  {/* Item Details */}
                  <div className="flex-1 min-w-0">
                    <div className="mb-2">
                      <div className="flex items-start gap-2 mb-1 flex-wrap">
                        <h3 className="font-semibold text-gray-900 text-sm sm:text-base leading-tight">{item.name}</h3>
                        <div className="flex items-center gap-1 flex-shrink-0">
                          {item.veg ? (
                            <Leaf className="h-3 w-3 sm:h-4 sm:w-4 text-green-500" />
                          ) : (
                            <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-red-500 rounded-sm flex items-center justify-center">
                              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full"></div>
                            </div>
                          )}
                          {item.popular && (
                            <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs px-1 py-0">
                              Popular
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 sm:gap-3 text-xs sm:text-sm text-gray-600 mb-2 flex-wrap">
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span>{item.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{item.time}</span>
                        </div>
                        {getSpicyIndicator(item.spicy)}
                      </div>
                      <div className="text-base sm:text-lg font-bold text-primary">₹{item.price}</div>
                    </div>

                    {/* Add to Cart Controls */}
                    <div className="flex items-center justify-end mt-2">
                      {getCartItemQuantity(item.id) === 0 ? (
                        <Button
                          variant="delivery"
                          size="sm"
                          onClick={() => addToCart(item)}
                          className="h-7 sm:h-8 px-3 sm:px-4 text-xs sm:text-sm"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Add
                        </Button>
                      ) : (
                        <div className="flex items-center gap-1 sm:gap-2 bg-primary rounded-lg p-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFromCart(item.id)}
                            className="h-5 w-5 sm:h-6 sm:w-6 text-white hover:bg-white/20"
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="text-white font-semibold min-w-[16px] sm:min-w-[20px] text-center text-xs sm:text-sm">
                            {getCartItemQuantity(item.id)}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => addToCart(item)}
                            className="h-5 w-5 sm:h-6 sm:w-6 text-white hover:bg-white/20"
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No items found</h3>
            <p className="text-gray-600">Try searching with different keywords</p>
          </div>
        )}
      </div>

      {/* Existing KOT Alert */}
      {existingKOT && (
        <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-50 to-blue-100 border-t-2 border-blue-300 p-3 sm:p-4 z-30 mobile-safe-content shadow-lg">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-blue-900">
              🍽️ Active Order for Table {tableId}
            </span>
            <Badge variant="secondary" className="bg-blue-200 text-blue-800 text-xs">
              KOT #{existingKOT.kotNumber}
            </Badge>
          </div>
          <div className="text-xs text-blue-700 mb-3 bg-white/50 rounded px-2 py-1">
            📋 {existingKOT.items.length} items ordered • 💰 ₹{existingKOT.totalAmount} total
            <br />
            🕒 Started: {new Date(existingKOT.createdAt).toLocaleString()}
            {existingKOT.versions.length > 1 && (
              <span className="text-blue-600"> • Updated {existingKOT.versions.length - 1} time(s)</span>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="default"
              size="sm"
              className="flex-1 text-xs bg-green-600 hover:bg-green-700"
              onClick={() => {
                // Complete existing KOT
                kotStorage.completeKOT(existingKOT.kotNumber);
                setExistingKOT(null);
                toast({
                  title: "Order Completed!",
                  description: `Table ${tableId} order has been completed successfully`,
                });
              }}
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Complete Order
            </Button>
          </div>
        </div>
      )}

      {/* Cart Footer */}
      {cart.length > 0 && (
        <div className="apk-footer-fixed bg-white border-t shadow-lg p-3 sm:p-4">
          <div className="flex items-center justify-between mb-2 sm:mb-3">
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="font-semibold text-gray-900 text-sm sm:text-base">
                {getTotalItems()} items
              </span>
            </div>
            <div className="text-lg sm:text-xl font-bold text-primary">
              ₹{getTotalAmount()}
            </div>
          </div>

          {existingKOT ? (
            // Show options when there's an existing KOT
            <div className="space-y-2">
              <Button
                variant="delivery"
                size="lg"
                className="w-full h-10 sm:h-12 text-sm sm:text-base"
                onClick={() => navigate(`/delivery/kot/${tableId}`, {
                  state: {
                    cart,
                    tableId,
                    existingKOT,
                    addToExisting: true
                  }
                })}
              >
                Print KOT (Add to #{existingKOT.kotNumber})
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="w-full h-8 sm:h-10 text-xs sm:text-sm"
                onClick={() => navigate(`/delivery/kot/${tableId}`, {
                  state: {
                    cart,
                    tableId,
                    createNew: true
                  }
                })}
              >
                Print New KOT
              </Button>
            </div>
          ) : (
            // Show normal KOT generation when no existing KOT
            <Button
              variant="delivery"
              size="lg"
              className="w-full h-10 sm:h-12 text-sm sm:text-base"
              onClick={() => navigate(`/delivery/kot/${tableId}`, { state: { cart, tableId } })}
            >
              Print KOT
            </Button>
          )}
        </div>
      )}

      {/* Filter Modal */}
      <Dialog open={showFilterModal} onOpenChange={setShowFilterModal}>
        <DialogContent className="w-[95vw] max-w-md mx-auto rounded-lg max-h-[90vh] flex flex-col apk-modal-above-header">
          <DialogHeader className="pb-3 sm:pb-4 flex-shrink-0">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-base sm:text-lg font-semibold text-orange-600">
                Filter Categories
              </DialogTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowFilterModal(false)}
                className="h-7 w-7 sm:h-8 sm:w-8 rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          <div className="space-y-4 sm:space-y-6 flex-1 overflow-y-auto">
            {/* Special Categories */}
            <div>
              <h3 className="text-sm font-medium text-purple-600 mb-2 sm:mb-3 flex items-center gap-2">
                ✨ Special Categories
              </h3>
              <div className="space-y-2 sm:space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <Checkbox
                      checked={selectedFilters.allItems}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedFilters({
                            allItems: true,
                            popular: false,
                            bestseller: false,
                            biryani: false,
                            riceVeg: false,
                            indianPunjabiVeg: false,
                            indianVegSpecialDishes: false,
                            gravyChinese: false,
                            tandooriBreads: false,
                            bulkOrder: false,
                            tandooriVeg: false,
                            papadSalad: false,
                            soupsVeg: false,
                            vegStarters: false,
                            noodlesVeg: false,
                            biryaniNonVeg: false,
                            riceNonVeg: false,
                            chickenGravy: false,
                            bulkOrderNonVeg: false,
                            muttonGravy: false,
                            tandooriNonVeg: false,
                            seaFood: false,
                            soupsNonVeg: false,
                            chickenStarters: false,
                            noodlesNonVeg: false,
                            eggDishes: false
                          });
                        }
                      }}
                    />
                    <span className="text-sm">All Items</span>
                  </div>
                  {selectedFilters.allItems && <Check className="h-4 w-4 text-green-600" />}
                </div>

                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedFilters.popular}
                    onCheckedChange={(checked) =>
                      setSelectedFilters(prev => ({ ...prev, popular: !!checked, allItems: false }))
                    }
                  />
                  <Flame className="h-4 w-4 text-orange-500" />
                  <span className="text-sm">Popular</span>
                </div>

                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedFilters.bestseller}
                    onCheckedChange={(checked) =>
                      setSelectedFilters(prev => ({ ...prev, bestseller: !!checked, allItems: false }))
                    }
                  />
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">Bestseller</span>
                </div>
              </div>
            </div>

            {/* Vegetarian */}
            <div>
              <h3 className="text-sm font-medium text-green-600 mb-3 flex items-center gap-2">
                🌱 Vegetarian
              </h3>
              <div className="space-y-3">
                {[
                  { id: 'biryani', label: '🍚 Biryani', key: 'biryani' },
                  { id: 'riceVeg', label: '🍚 Rice (Veg)', key: 'riceVeg' },
                  { id: 'indianPunjabiVeg', label: '🍚 Indian & Punjabi (Veg)', key: 'indianPunjabiVeg' },
                  { id: 'indianVegSpecialDishes', label: '🍚 Indian (Veg) Special Dishes', key: 'indianVegSpecialDishes' },
                  { id: 'gravyChinese', label: '🍜 Gravy (Chinese)', key: 'gravyChinese' },
                  { id: 'tandooriBreads', label: '🍞 Tandoori Breads', key: 'tandooriBreads' },
                  { id: 'bulkOrder', label: '📦 Bulk Order', key: 'bulkOrder' },
                  { id: 'tandooriVeg', label: '🍞 Tandoori (Veg)', key: 'tandooriVeg' },
                  { id: 'papadSalad', label: '🥗 Papad & Salad', key: 'papadSalad' },
                  { id: 'soupsVeg', label: '🍲 Soups (Veg)', key: 'soupsVeg' },
                  { id: 'vegStarters', label: '🥗 Veg Starters', key: 'vegStarters' },
                  { id: 'noodlesVeg', label: '🍜 Noodles (Veg)', key: 'noodlesVeg' }
                ].map((filter) => (
                  <div key={filter.id} className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedFilters[filter.key as keyof typeof selectedFilters]}
                      onCheckedChange={(checked) =>
                        setSelectedFilters(prev => ({ ...prev, [filter.key]: !!checked, allItems: false }))
                      }
                    />
                    <span className="text-sm">{filter.label}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Non-Vegetarian */}
            <div>
              <h3 className="text-sm font-medium text-red-600 mb-3 flex items-center gap-2">
                🔥 Non-Vegetarian
              </h3>
              <div className="space-y-3">
                {[
                  { id: 'biryaniNonVeg', label: '🍚 Biryani (Non-Veg)', key: 'biryaniNonVeg' },
                  { id: 'riceNonVeg', label: '🍚 Rice (Non-Veg)', key: 'riceNonVeg' },
                  { id: 'chickenGravy', label: '🍗 Chicken Gravy', key: 'chickenGravy' },
                  { id: 'bulkOrderNonVeg', label: '📦 Bulk Order (Non-Veg)', key: 'bulkOrderNonVeg' },
                  { id: 'muttonGravy', label: '🐑 Mutton Gravy', key: 'muttonGravy' },
                  { id: 'tandooriNonVeg', label: '🍞 Tandoori (Non-Veg)', key: 'tandooriNonVeg' },
                  { id: 'seaFood', label: '🐟 Sea Food', key: 'seaFood' },
                  { id: 'soupsNonVeg', label: '🍲 Soups (Non-Veg)', key: 'soupsNonVeg' },
                  { id: 'chickenStarters', label: '🍗 Chicken Starters', key: 'chickenStarters' },
                  { id: 'noodlesNonVeg', label: '🍜 Noodles (Non-Veg)', key: 'noodlesNonVeg' }
                ].map((filter) => (
                  <div key={filter.id} className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedFilters[filter.key as keyof typeof selectedFilters]}
                      onCheckedChange={(checked) =>
                        setSelectedFilters(prev => ({ ...prev, [filter.key]: !!checked, allItems: false }))
                      }
                    />
                    <span className="text-sm">{filter.label}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Egg Items */}
            <div>
              <h3 className="text-sm font-medium text-orange-600 mb-3 flex items-center gap-2">
                🥚 Egg Items
              </h3>
              <div className="flex items-center gap-3">
                <Checkbox
                  checked={selectedFilters.eggDishes}
                  onCheckedChange={(checked) =>
                    setSelectedFilters(prev => ({ ...prev, eggDishes: !!checked, allItems: false }))
                  }
                />
                <span className="text-sm">🥚 Egg Dishes</span>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex gap-2 sm:gap-3 pt-3 sm:pt-4 border-t flex-shrink-0">
            <Button
              variant="outline"
              className="flex-1 h-9 sm:h-10 text-xs sm:text-sm"
              onClick={() => setSelectedFilters({
                allItems: true,
                popular: false,
                bestseller: false,
                biryani: false,
                riceVeg: false,
                indianPunjabiVeg: false,
                indianVegSpecialDishes: false,
                gravyChinese: false,
                tandooriBreads: false,
                bulkOrder: false,
                tandooriVeg: false,
                papadSalad: false,
                soupsVeg: false,
                vegStarters: false,
                noodlesVeg: false,
                biryaniNonVeg: false,
                riceNonVeg: false,
                chickenGravy: false,
                bulkOrderNonVeg: false,
                muttonGravy: false,
                tandooriNonVeg: false,
                seaFood: false,
                soupsNonVeg: false,
                chickenStarters: false,
                noodlesNonVeg: false,
                eggDishes: false
              })}
            >
              Clear All
            </Button>
            <Button
              className="flex-1 bg-orange-600 hover:bg-orange-700 h-9 sm:h-10 text-xs sm:text-sm"
              onClick={() => {
                setShowFilterModal(false);
                // Apply filters logic here
              }}
            >
              Apply Filter ({Object.values(selectedFilters).filter(Boolean).length})
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TableOrder;
