// Initialize sample data for testing attendance and salary systems

import { createStaffPin, getAllStaffPins } from './staffPinStorage';
import { markCheckIn, markCheckOut, getAllAttendanceRecords } from './attendanceStorage';

// Sample staff data
const sampleStaff = [
  {
    name: "<PERSON><PERSON>",
    phone: "9876543210",
    role: "staff" as const,
    createdBy: "admin"
  },
  {
    name: "<PERSON>",
    phone: "8765432109",
    role: "staff" as const,
    createdBy: "admin"
  },
  {
    name: "<PERSON><PERSON>",
    phone: "7654321098",
    role: "staff" as const,
    createdBy: "admin"
  },
  {
    name: "<PERSON><PERSON>",
    phone: "6543210987",
    role: "staff" as const,
    createdBy: "admin"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    phone: "5432109876",
    role: "admin" as const,
    createdBy: "admin"
  }
];

// Initialize sample staff PINs if they don't exist
export const initializeSampleStaff = () => {
  const existingStaff = getAllStaffPins();
  
  // Only create sample staff if no staff exists (except default admin)
  const nonAdminStaff = existingStaff.filter(staff => staff.role !== 'manager');
  
  if (nonAdminStaff.length === 0) {
    console.log('🔧 Initializing sample staff data...');
    
    sampleStaff.forEach(staffData => {
      try {
        const newStaff = createStaffPin(staffData);
        console.log(`✅ Created staff: ${newStaff.name} (PIN: ${newStaff.pin})`);
      } catch (error) {
        console.error(`❌ Failed to create staff ${staffData.name}:`, error);
      }
    });
    
    console.log('🎉 Sample staff data initialized successfully!');
  } else {
    console.log('ℹ️ Staff data already exists, skipping initialization');
  }
};

// Generate sample attendance data for the last 30 days
export const generateSampleAttendanceData = () => {
  const existingRecords = getAllAttendanceRecords();
  
  // Only generate if no attendance records exist
  if (existingRecords.length === 0) {
    console.log('🔧 Generating sample attendance data...');
    
    const staff = getAllStaffPins().filter(s => s.role !== 'manager' && s.isActive);
    const today = new Date();
    
    // Generate attendance for last 30 days
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      // Skip weekends for some variety (assuming Sunday = 0, Saturday = 6)
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      staff.forEach(staffMember => {
        // 90% attendance rate, lower on weekends
        const attendanceChance = isWeekend ? 0.3 : 0.9;
        const shouldAttend = Math.random() < attendanceChance;
        
        if (shouldAttend) {
          try {
            // Generate random check-in time between 8:00 AM and 10:00 AM
            const checkInHour = 8 + Math.random() * 2;
            const checkInMinute = Math.random() * 60;
            const checkInTime = new Date(date);
            checkInTime.setHours(Math.floor(checkInHour), Math.floor(checkInMinute), 0, 0);
            
            // Generate random check-out time between 6:00 PM and 10:00 PM
            const checkOutHour = 18 + Math.random() * 4;
            const checkOutMinute = Math.random() * 60;
            const checkOutTime = new Date(date);
            checkOutTime.setHours(Math.floor(checkOutHour), Math.floor(checkOutMinute), 0, 0);
            
            // Create attendance record manually (simulating past dates)
            const attendanceRecord = {
              id: `${staffMember.id}-${dateStr}`,
              staffId: staffMember.id,
              staffName: staffMember.name,
              staffPhone: staffMember.phone,
              date: dateStr,
              checkInTime: checkInTime.toISOString(),
              checkOutTime: checkOutTime.toISOString(),
              status: 'checked-out' as const,
              totalHours: Math.round(((checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60)) * 100) / 100,
              notes: i === 0 ? 'Sample data for testing' : undefined
            };
            
            // Add to localStorage directly
            const records = getAllAttendanceRecords();
            records.push(attendanceRecord);
            localStorage.setItem('wok_ka_tadka_attendance', JSON.stringify(records));
            
          } catch (error) {
            console.error(`❌ Failed to create attendance for ${staffMember.name} on ${dateStr}:`, error);
          }
        }
      });
    }
    
    console.log('🎉 Sample attendance data generated successfully!');
  } else {
    console.log('ℹ️ Attendance data already exists, skipping generation');
  }
};

// Initialize all sample data
export const initializeAllSampleData = () => {
  console.log('🚀 Initializing sample data for testing...');
  
  // Initialize staff first
  initializeSampleStaff();
  
  // Then generate attendance data
  setTimeout(() => {
    generateSampleAttendanceData();
  }, 100); // Small delay to ensure staff data is saved
  
  console.log('✅ Sample data initialization complete!');
};

// Clear all sample data (for testing purposes)
export const clearAllSampleData = () => {
  console.log('🧹 Clearing all sample data...');
  
  // Clear attendance records
  localStorage.removeItem('wok_ka_tadka_attendance');
  
  // Clear salary records
  localStorage.removeItem('wok_ka_tadka_salary_records');
  localStorage.removeItem('wok_ka_tadka_salary_payments');
  
  // Clear staff PINs (except admin)
  const allStaff = getAllStaffPins();
  const adminOnly = allStaff.filter(staff => staff.role === 'manager');
  localStorage.setItem('wok_ka_tadka_staff_pins', JSON.stringify(adminOnly));
  
  console.log('🗑️ All sample data cleared!');
};

// Display current data summary
export const displayDataSummary = () => {
  const staff = getAllStaffPins();
  const attendance = getAllAttendanceRecords();
  
  console.log('📊 Current Data Summary:');
  console.log(`👥 Staff Members: ${staff.length}`);
  console.log(`📅 Attendance Records: ${attendance.length}`);
  
  // Group attendance by staff
  const attendanceByStaff = attendance.reduce((acc, record) => {
    acc[record.staffId] = (acc[record.staffId] || 0) + 1;
    return acc;
  }, {} as {[key: string]: number});
  
  console.log('📈 Attendance by Staff:');
  Object.entries(attendanceByStaff).forEach(([staffId, count]) => {
    const staffMember = staff.find(s => s.id === staffId);
    console.log(`  ${staffMember?.name || 'Unknown'}: ${count} records`);
  });
};

// Auto-initialize on import (only in development)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Check if we should auto-initialize
  const shouldAutoInit = localStorage.getItem('auto_init_sample_data') !== 'false';
  
  if (shouldAutoInit) {
    // Only run once per session
    const sessionKey = 'sample_data_initialized_' + Date.now().toString().slice(0, -5);
    if (!sessionStorage.getItem(sessionKey)) {
      setTimeout(() => {
        initializeAllSampleData();
        sessionStorage.setItem(sessionKey, 'true');
      }, 1000);
    }
  }
}

// Export utility functions for manual use
export const sampleDataUtils = {
  initializeStaff: initializeSampleStaff,
  generateAttendance: generateSampleAttendanceData,
  initializeAll: initializeAllSampleData,
  clearAll: clearAllSampleData,
  displaySummary: displayDataSummary
};
